"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_agent-collaboration_ConversationSupervisor_ts";
exports.ids = ["_rsc_src_lib_agent-collaboration_ConversationSupervisor_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/agent-collaboration/ConversationNodes.ts":
/*!**********************************************************!*\
  !*** ./src/lib/agent-collaboration/ConversationNodes.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationNodes: () => (/* binding */ ConversationNodes)\n/* harmony export */ });\n/* harmony import */ var _providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../providers/executeProviderRequest */ \"(rsc)/./src/lib/providers/executeProviderRequest.ts\");\n/* harmony import */ var _encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../encryption */ \"(rsc)/./src/lib/encryption.ts\");\n// True Agent Collaboration - LangGraph Conversation Nodes\n// These nodes handle different phases of real agent-to-agent conversation\n\n\nclass ConversationNodes {\n    /**\n   * Brainstorming Phase Node - Agents share initial ideas and explore the problem space\n   */ static createBrainstormingNode() {\n        return async (state)=>{\n            console.log(`[Brainstorming] Starting brainstorming phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Each agent contributes initial ideas\n            for (const agentId of agentKeys){\n                const agentKey = state.agentApiKeys[agentId];\n                const contextSummary = manager.generateContextSummary();\n                const brainstormPrompt = `${contextSummary}\n\n[BRAINSTORMING PHASE - INITIAL IDEAS]\nYou are ${manager.getState().agentRoles[agentId]} participating in a collaborative brainstorming session.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nYour task in this brainstorming phase:\n1. **ANALYZE** the request and share your initial understanding\n2. **PROPOSE** creative approaches and ideas\n3. **IDENTIFY** key challenges and opportunities\n4. **ASK QUESTIONS** that will help the team understand the problem better\n5. **BUILD** on any ideas already shared by other agents\n\n${manager.getRecentTurns(3).length > 0 ? 'Respond to and build on the ideas already shared by your teammates.' : 'You are starting the brainstorming - share your initial thoughts and ideas.'}\n\nFocus on generating diverse, creative ideas rather than detailed solutions. Be collaborative and engaging!`;\n                try {\n                    const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                        custom_api_config_id: state.customApiConfigId,\n                        messages: [\n                            {\n                                role: 'user',\n                                content: brainstormPrompt\n                            }\n                        ],\n                        temperature: 0.8,\n                        max_tokens: 2000,\n                        stream: false,\n                        role: 'orchestration'\n                    });\n                    if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                        const response = result.responseData.choices[0].message.content;\n                        // Add to conversation\n                        manager.addTurn({\n                            agent: agentId,\n                            agentLabel: agentKey.label,\n                            message: response,\n                            messageType: 'proposal',\n                            respondingTo: manager.getRecentTurns(1)[0]?.id // Respond to most recent if exists\n                        });\n                        console.log(`[Brainstorming] ✅ ${agentKey.label} contributed ideas (${response.length} chars)`);\n                    } else {\n                        console.error(`[Brainstorming] ❌ ${agentKey.label} failed to contribute`);\n                    }\n                } catch (error) {\n                    console.error(`[Brainstorming] Error with ${agentKey.label}:`, error);\n                }\n            }\n            // Update phase progress\n            manager.setPhaseProgress('brainstorming', 1.0);\n            manager.updateQualityMetrics();\n            return {\n                nextAction: 'advance_phase',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Debate Phase Node - Agents challenge ideas, identify conflicts, and refine thinking\n   */ static createDebateNode() {\n        return async (state)=>{\n            console.log(`[Debate] Starting debate phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Advance to debate phase\n            manager.advancePhase();\n            // Each agent critiques and challenges ideas\n            for (const agentId of agentKeys){\n                const agentKey = state.agentApiKeys[agentId];\n                const contextSummary = manager.generateContextSummary();\n                const recentTurns = manager.getRecentTurns(5);\n                const debatePrompt = `${contextSummary}\n\n[DEBATE PHASE - CRITICAL ANALYSIS]\nYou are ${manager.getState().agentRoles[agentId]} in a critical debate phase.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nRecent Team Discussion:\n${recentTurns.map((turn)=>`${turn.agentLabel}: ${turn.message}`).join('\\n\\n')}\n\nYour task in this debate phase:\n1. **CRITIQUE** ideas shared by other agents - identify flaws, gaps, or concerns\n2. **CHALLENGE** assumptions and approaches that might not work\n3. **DEFEND** good ideas and explain why they're valuable\n4. **IDENTIFY** conflicts between different approaches\n5. **PROPOSE** alternative solutions to problems you identify\n6. **ASK HARD QUESTIONS** that need to be resolved\n\nBe constructively critical - challenge ideas to make them better, not to tear them down. Focus on finding the best path forward through rigorous analysis.`;\n                try {\n                    const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                        custom_api_config_id: state.customApiConfigId,\n                        messages: [\n                            {\n                                role: 'user',\n                                content: debatePrompt\n                            }\n                        ],\n                        temperature: 0.6,\n                        max_tokens: 2500,\n                        stream: false,\n                        role: 'orchestration'\n                    });\n                    if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                        const response = result.responseData.choices[0].message.content;\n                        // Analyze response for conflicts and issues\n                        const hasDisagreement = response.toLowerCase().includes('disagree') || response.toLowerCase().includes('concern') || response.toLowerCase().includes('problem');\n                        if (hasDisagreement) {\n                            // Extract the main concern/issue\n                            const issueMatch = response.match(/(?:concern|problem|issue|disagree)[^.]*\\.?/i);\n                            if (issueMatch) {\n                                manager.addUnresolvedIssue(issueMatch[0], [\n                                    agentId\n                                ], 'medium');\n                            }\n                        }\n                        manager.addTurn({\n                            agent: agentId,\n                            agentLabel: agentKey.label,\n                            message: response,\n                            messageType: hasDisagreement ? 'critique' : 'improvement',\n                            respondingTo: recentTurns[recentTurns.length - 1]?.id\n                        });\n                        console.log(`[Debate] ✅ ${agentKey.label} provided critical analysis (${response.length} chars)`);\n                    }\n                } catch (error) {\n                    console.error(`[Debate] Error with ${agentKey.label}:`, error);\n                }\n            }\n            manager.setPhaseProgress('debate', 1.0);\n            manager.updateQualityMetrics();\n            // Check if we need another round of debate or can move to synthesis\n            const unresolvedIssues = manager.getUnresolvedIssues();\n            if (unresolvedIssues.length > 0 && state.iterationCount < state.maxIterations) {\n                return {\n                    nextAction: 'continue_discussion',\n                    iterationCount: state.iterationCount + 1\n                };\n            }\n            return {\n                nextAction: 'advance_phase',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Synthesis Phase Node - Agents collaborate to build the final solution\n   */ static createSynthesisNode() {\n        return async (state)=>{\n            console.log(`[Synthesis] Starting synthesis phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Advance to synthesis phase\n            manager.advancePhase();\n            // First, try to resolve any remaining issues\n            const unresolvedIssues = manager.getUnresolvedIssues();\n            if (unresolvedIssues.length > 0) {\n                console.log(`[Synthesis] Resolving ${unresolvedIssues.length} unresolved issues first`);\n                for (const issue of unresolvedIssues.slice(0, 3)){\n                    for (const agentId of agentKeys){\n                        const agentKey = state.agentApiKeys[agentId];\n                        const resolutionPrompt = `[ISSUE RESOLUTION]\nYou are ${manager.getState().agentRoles[agentId]} helping resolve a team disagreement.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nUnresolved Issue: \"${issue.description}\"\n\nContext: ${manager.generateContextSummary()}\n\nYour task:\n1. **PROPOSE** a specific solution to resolve this issue\n2. **EXPLAIN** why your solution addresses the core concern\n3. **CONSIDER** how this fits with the overall approach\n4. **BUILD CONSENSUS** by finding common ground\n\nFocus on finding a solution that the whole team can agree on.`;\n                        try {\n                            const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                                custom_api_config_id: state.customApiConfigId,\n                                messages: [\n                                    {\n                                        role: 'user',\n                                        content: resolutionPrompt\n                                    }\n                                ],\n                                temperature: 0.4,\n                                max_tokens: 1500,\n                                stream: false,\n                                role: 'orchestration'\n                            });\n                            if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                                const response = result.responseData.choices[0].message.content;\n                                manager.addTurn({\n                                    agent: agentId,\n                                    agentLabel: agentKey.label,\n                                    message: response,\n                                    messageType: 'agreement',\n                                    respondingTo: undefined\n                                });\n                                // Check if this resolves the issue\n                                if (response.toLowerCase().includes('agree') || response.toLowerCase().includes('solution')) {\n                                    manager.resolveIssue(issue.id, response);\n                                }\n                            }\n                        } catch (error) {\n                            console.error(`[Synthesis] Error resolving issue with ${agentKey.label}:`, error);\n                        }\n                    }\n                }\n            }\n            // Now build the final collaborative solution\n            console.log(`[Synthesis] Building final collaborative solution`);\n            const leadAgent = agentKeys[0]; // Use first agent as synthesis coordinator\n            const leadAgentKey = state.agentApiKeys[leadAgent];\n            const synthesisPrompt = `[FINAL SOLUTION SYNTHESIS]\nYou are ${manager.getState().agentRoles[leadAgent]} coordinating the final solution synthesis.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nComplete Team Discussion:\n${manager.getState().conversationHistory.map((turn)=>`${turn.agentLabel} (${turn.messageType}): ${turn.message}`).join('\\n\\n')}\n\nTeam Consensus Points:\n${manager.getConsensusItems().map((item)=>`- ${item.topic}: ${item.agreedPoint}`).join('\\n')}\n\nYour task:\n1. **SYNTHESIZE** all the team's ideas into one comprehensive solution\n2. **INCORPORATE** the best elements from each agent's contributions\n3. **ADDRESS** the original request completely and thoroughly\n4. **ENSURE** the solution reflects the team's consensus and collaboration\n5. **PROVIDE** a high-quality, detailed response that showcases the collective intelligence\n\nCreate the final solution that represents the best collaborative thinking of the entire team.`;\n            try {\n                const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(leadAgentKey.provider, leadAgentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(leadAgentKey.encrypted_api_key), {\n                    custom_api_config_id: state.customApiConfigId,\n                    messages: [\n                        {\n                            role: 'user',\n                            content: synthesisPrompt\n                        }\n                    ],\n                    temperature: 0.3,\n                    max_tokens: 4000,\n                    stream: false,\n                    role: 'orchestration'\n                });\n                if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                    const finalSolution = result.responseData.choices[0].message.content;\n                    manager.setFinalSolution(finalSolution);\n                    manager.addTurn({\n                        agent: leadAgent,\n                        agentLabel: leadAgentKey.label,\n                        message: finalSolution,\n                        messageType: 'synthesis'\n                    });\n                    console.log(`[Synthesis] ✅ Final solution synthesized (${finalSolution.length} chars)`);\n                }\n            } catch (error) {\n                console.error(`[Synthesis] Error creating final solution:`, error);\n            }\n            manager.setPhaseProgress('synthesis', 1.0);\n            manager.updateQualityMetrics();\n            return {\n                nextAction: 'advance_phase',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Validation Phase Node - Team validates the final solution\n   */ static createValidationNode() {\n        return async (state)=>{\n            console.log(`[Validation] Starting validation phase`);\n            const manager = state.conversationManager;\n            const agentKeys = Object.keys(state.agentApiKeys);\n            // Advance to validation phase\n            manager.advancePhase();\n            const finalSolution = manager.getState().finalSolution;\n            if (!finalSolution) {\n                console.error(`[Validation] No final solution to validate`);\n                return {\n                    nextAction: 'complete'\n                };\n            }\n            // Each agent validates the final solution\n            let validationScore = 0;\n            let validationCount = 0;\n            for (const agentId of agentKeys){\n                const agentKey = state.agentApiKeys[agentId];\n                const validationPrompt = `[SOLUTION VALIDATION]\nYou are ${manager.getState().agentRoles[agentId]} validating the team's final solution.\n\nOriginal Request: \"${manager.getState().userPrompt}\"\n\nFinal Team Solution:\n${finalSolution}\n\nYour task:\n1. **EVALUATE** how well the solution addresses the original request\n2. **CHECK** if it incorporates the team's best ideas and consensus\n3. **ASSESS** the quality, completeness, and effectiveness\n4. **IDENTIFY** any remaining gaps or improvements needed\n5. **RATE** the solution on a scale of 1-10\n\nProvide your validation in this format:\n## Validation Assessment\n[Your detailed assessment of the solution]\n\n## Quality Rating: X/10\n[Explanation of your rating]\n\n## Final Approval: YES/NO\n[Whether you approve this as the final solution]`;\n                try {\n                    const result = await (0,_providers_executeProviderRequest__WEBPACK_IMPORTED_MODULE_0__.executeProviderRequest)(agentKey.provider, agentKey.predefined_model_id, await (0,_encryption__WEBPACK_IMPORTED_MODULE_1__.decrypt)(agentKey.encrypted_api_key), {\n                        custom_api_config_id: state.customApiConfigId,\n                        messages: [\n                            {\n                                role: 'user',\n                                content: validationPrompt\n                            }\n                        ],\n                        temperature: 0.2,\n                        max_tokens: 2000,\n                        stream: false,\n                        role: 'orchestration'\n                    });\n                    if (result.success && result.responseData?.choices?.[0]?.message?.content) {\n                        const response = result.responseData.choices[0].message.content;\n                        // Extract rating\n                        const ratingMatch = response.match(/(?:Quality Rating|Rating):\\s*(\\d+)\\/10/i);\n                        if (ratingMatch) {\n                            validationScore += parseInt(ratingMatch[1]);\n                            validationCount++;\n                        }\n                        manager.addTurn({\n                            agent: agentId,\n                            agentLabel: agentKey.label,\n                            message: response,\n                            messageType: 'agreement'\n                        });\n                        console.log(`[Validation] ✅ ${agentKey.label} completed validation`);\n                    }\n                } catch (error) {\n                    console.error(`[Validation] Error with ${agentKey.label}:`, error);\n                }\n            }\n            // Calculate final quality score\n            const avgValidationScore = validationCount > 0 ? validationScore / validationCount : 0;\n            manager.getState().qualityMetrics.noveltyScore = avgValidationScore / 10;\n            manager.setPhaseProgress('validation', 1.0);\n            manager.updateQualityMetrics();\n            console.log(`[Validation] ✅ Validation complete. Average score: ${avgValidationScore.toFixed(1)}/10`);\n            return {\n                nextAction: 'complete',\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agent-collaboration/ConversationNodes.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agent-collaboration/ConversationState.ts":
/*!**********************************************************!*\
  !*** ./src/lib/agent-collaboration/ConversationState.ts ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationStateManager: () => (/* binding */ ConversationStateManager)\n/* harmony export */ });\n// True Agent Collaboration - Conversation State Management\n// This manages the real-time conversation state for genuine agent-to-agent communication\nclass ConversationStateManager {\n    constructor(conversationId, userPrompt, agents, maxRounds = 5){\n        this.state = {\n            conversationId,\n            userPrompt,\n            currentPhase: 'brainstorming',\n            conversationHistory: [],\n            activeAgents: agents.map((a)=>a.id),\n            agentRoles: agents.reduce((acc, agent)=>{\n                acc[agent.id] = agent.role || `Agent ${agent.id.replace('agent_', '')} (${agent.label})`;\n                return acc;\n            }, {}),\n            unresolvedIssues: [],\n            consensusItems: [],\n            qualityMetrics: {\n                overallScore: 0,\n                participationBalance: 0,\n                iterativeImprovement: 0,\n                consensusStrength: 0,\n                noveltyScore: 0\n            },\n            phaseProgress: {\n                brainstorming: 0,\n                debate: 0,\n                synthesis: 0,\n                validation: 0\n            },\n            currentSolutions: {},\n            startTime: Date.now(),\n            lastActivity: Date.now(),\n            maxRounds,\n            currentRound: 1\n        };\n    }\n    // Add a new conversation turn\n    addTurn(turn) {\n        const newTurn = {\n            ...turn,\n            id: `turn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            timestamp: Date.now()\n        };\n        this.state.conversationHistory.push(newTurn);\n        this.state.lastActivity = Date.now();\n        console.log(`[Conversation] ${turn.agentLabel} added ${turn.messageType}: ${turn.message.substring(0, 100)}...`);\n        return newTurn;\n    }\n    // Get conversation turns for a specific agent or responding to a specific turn\n    getTurnsBy(criteria) {\n        return this.state.conversationHistory.filter((turn)=>{\n            if (criteria.agent && turn.agent !== criteria.agent) return false;\n            if (criteria.respondingTo && turn.respondingTo !== criteria.respondingTo) return false;\n            if (criteria.messageType && turn.messageType !== criteria.messageType) return false;\n            return true;\n        });\n    }\n    // Get the latest turns (for context)\n    getRecentTurns(count = 5) {\n        return this.state.conversationHistory.slice(-count);\n    }\n    // Add an unresolved issue\n    addUnresolvedIssue(description, involvedAgents, priority = 'medium') {\n        const issue = {\n            id: `issue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n            description,\n            involvedAgents,\n            proposedSolutions: [],\n            priority,\n            createdAt: Date.now()\n        };\n        this.state.unresolvedIssues.push(issue);\n        console.log(`[Conversation] New unresolved issue: ${description}`);\n        return issue;\n    }\n    // Resolve an issue\n    resolveIssue(issueId, solution) {\n        const issueIndex = this.state.unresolvedIssues.findIndex((issue)=>issue.id === issueId);\n        if (issueIndex === -1) return false;\n        const issue = this.state.unresolvedIssues[issueIndex];\n        // Add to consensus items\n        this.addConsensusItem(issue.description, solution, issue.involvedAgents);\n        // Remove from unresolved\n        this.state.unresolvedIssues.splice(issueIndex, 1);\n        console.log(`[Conversation] Resolved issue: ${issue.description} -> ${solution}`);\n        return true;\n    }\n    // Add a consensus item\n    addConsensusItem(topic, agreedPoint, supportingAgents, confidence = 0.8) {\n        const consensus = {\n            topic,\n            agreedPoint,\n            supportingAgents,\n            confidence\n        };\n        this.state.consensusItems.push(consensus);\n        console.log(`[Conversation] New consensus: ${topic} -> ${agreedPoint} (${supportingAgents.length} agents)`);\n        return consensus;\n    }\n    // Update quality metrics\n    updateQualityMetrics() {\n        const history = this.state.conversationHistory;\n        const agents = this.state.activeAgents;\n        // Participation balance (how evenly agents participated)\n        const participationCounts = agents.reduce((acc, agent)=>{\n            acc[agent] = history.filter((turn)=>turn.agent === agent).length;\n            return acc;\n        }, {});\n        const participationValues = Object.values(participationCounts);\n        const avgParticipation = participationValues.reduce((a, b)=>a + b, 0) / participationValues.length;\n        const participationVariance = participationValues.reduce((acc, val)=>acc + Math.pow(val - avgParticipation, 2), 0) / participationValues.length;\n        this.state.qualityMetrics.participationBalance = Math.max(0, 1 - participationVariance / (avgParticipation || 1));\n        // Consensus strength (how many consensus items vs unresolved issues)\n        const totalIssues = this.state.consensusItems.length + this.state.unresolvedIssues.length;\n        this.state.qualityMetrics.consensusStrength = totalIssues > 0 ? this.state.consensusItems.length / totalIssues : 0;\n        // Iterative improvement (are later messages building on earlier ones?)\n        const responseConnections = history.filter((turn)=>turn.respondingTo).length;\n        this.state.qualityMetrics.iterativeImprovement = history.length > 0 ? responseConnections / history.length : 0;\n        // Overall score (weighted average)\n        this.state.qualityMetrics.overallScore = this.state.qualityMetrics.participationBalance * 0.3 + this.state.qualityMetrics.consensusStrength * 0.4 + this.state.qualityMetrics.iterativeImprovement * 0.3;\n        console.log(`[Conversation] Quality metrics updated: Overall=${this.state.qualityMetrics.overallScore.toFixed(2)}, Consensus=${this.state.qualityMetrics.consensusStrength.toFixed(2)}, Balance=${this.state.qualityMetrics.participationBalance.toFixed(2)}`);\n    }\n    // Phase management\n    advancePhase() {\n        const phases = [\n            'brainstorming',\n            'debate',\n            'synthesis',\n            'validation',\n            'complete'\n        ];\n        const currentIndex = phases.indexOf(this.state.currentPhase);\n        if (currentIndex < phases.length - 1) {\n            this.state.currentPhase = phases[currentIndex + 1];\n            console.log(`[Conversation] Advanced to phase: ${this.state.currentPhase}`);\n            return true;\n        }\n        return false;\n    }\n    setPhaseProgress(phase, progress) {\n        this.state.phaseProgress[phase] = Math.max(0, Math.min(1, progress));\n    }\n    // Solution management\n    updateAgentSolution(agent, solution) {\n        this.state.currentSolutions[agent] = solution;\n        console.log(`[Conversation] ${agent} updated their solution (${solution.length} chars)`);\n    }\n    setFinalSolution(solution) {\n        this.state.finalSolution = solution;\n        console.log(`[Conversation] Final solution set (${solution.length} chars)`);\n    }\n    // State access\n    getState() {\n        return this.state;\n    }\n    getCurrentPhase() {\n        return this.state.currentPhase;\n    }\n    getUnresolvedIssues() {\n        return [\n            ...this.state.unresolvedIssues\n        ];\n    }\n    getConsensusItems() {\n        return [\n            ...this.state.consensusItems\n        ];\n    }\n    getQualityMetrics() {\n        return {\n            ...this.state.qualityMetrics\n        };\n    }\n    // Check if conversation should continue\n    shouldContinue() {\n        // Continue if we haven't reached max rounds and there are unresolved issues\n        const hasUnresolvedIssues = this.state.unresolvedIssues.length > 0;\n        const withinRoundLimit = this.state.currentRound < this.state.maxRounds;\n        const qualityThreshold = this.state.qualityMetrics.overallScore >= 0.7;\n        return withinRoundLimit && (hasUnresolvedIssues || !qualityThreshold);\n    }\n    nextRound() {\n        this.state.currentRound++;\n        console.log(`[Conversation] Starting round ${this.state.currentRound}/${this.state.maxRounds}`);\n    }\n    // Generate conversation summary for context\n    generateContextSummary() {\n        const recentTurns = this.getRecentTurns(3);\n        const unresolvedCount = this.state.unresolvedIssues.length;\n        const consensusCount = this.state.consensusItems.length;\n        return `\n## Conversation Context (Round ${this.state.currentRound}/${this.state.maxRounds}, Phase: ${this.state.currentPhase})\n\n**Recent Discussion:**\n${recentTurns.map((turn)=>`- ${turn.agentLabel}: ${turn.message.substring(0, 150)}...`).join('\\n')}\n\n**Consensus Reached (${consensusCount} items):**\n${this.state.consensusItems.map((item)=>`- ${item.topic}: ${item.agreedPoint}`).join('\\n')}\n\n**Unresolved Issues (${unresolvedCount} remaining):**\n${this.state.unresolvedIssues.map((issue)=>`- ${issue.description} (${issue.priority} priority)`).join('\\n')}\n\n**Quality Score:** ${this.state.qualityMetrics.overallScore.toFixed(2)}/1.0\n`.trim();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agent-collaboration/ConversationState.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/agent-collaboration/ConversationSupervisor.ts":
/*!***************************************************************!*\
  !*** ./src/lib/agent-collaboration/ConversationSupervisor.ts ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConversationSupervisor: () => (/* binding */ ConversationSupervisor)\n/* harmony export */ });\n/* harmony import */ var _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @langchain/langgraph */ \"(rsc)/./node_modules/@langchain/langgraph/index.js\");\n/* harmony import */ var _ConversationState__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ConversationState */ \"(rsc)/./src/lib/agent-collaboration/ConversationState.ts\");\n/* harmony import */ var _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ConversationNodes */ \"(rsc)/./src/lib/agent-collaboration/ConversationNodes.ts\");\n// True Agent Collaboration - Conversation Supervisor\n// Orchestrates the entire conversation flow and ensures quality collaboration\n\n\n\nclass ConversationSupervisor {\n    constructor(conversationId, userPrompt, agentApiKeys, customApiConfigId, maxIterations = 10){\n        // Initialize conversation manager\n        const agents = Object.entries(agentApiKeys).map(([id, key])=>({\n                id,\n                label: key.label,\n                role: `Agent ${id.replace('agent_', '')} (${key.label})`\n            }));\n        this.conversationManager = new _ConversationState__WEBPACK_IMPORTED_MODULE_1__.ConversationStateManager(conversationId, userPrompt, agents, Math.ceil(maxIterations / 2) // Max rounds is half of max iterations\n        );\n        this.agentApiKeys = agentApiKeys;\n        this.customApiConfigId = customApiConfigId;\n        this.maxIterations = maxIterations;\n    }\n    /**\n   * Execute the full conversation workflow\n   */ async executeCollaboration() {\n        console.log(`[Conversation Supervisor] 🚀 Starting true agent collaboration with ${Object.keys(this.agentApiKeys).length} agents`);\n        try {\n            // Build the conversation workflow\n            const workflow = this.buildConversationWorkflow();\n            // Initial state\n            const initialState = {\n                messages: [],\n                conversationManager: this.conversationManager,\n                agentApiKeys: this.agentApiKeys,\n                customApiConfigId: this.customApiConfigId,\n                iterationCount: 0,\n                maxIterations: this.maxIterations,\n                nextAction: 'continue_discussion'\n            };\n            // Execute the workflow\n            console.log(`[Conversation Supervisor] 📋 Executing conversation workflow...`);\n            const finalState = await workflow.invoke(initialState);\n            // Extract results\n            const finalSolution = this.conversationManager.getState().finalSolution;\n            const qualityMetrics = this.conversationManager.getQualityMetrics();\n            const agentNames = Object.values(this.agentApiKeys).map((key)=>key.label);\n            if (!finalSolution) {\n                console.error(`[Conversation Supervisor] ❌ No final solution generated`);\n                return {\n                    success: false,\n                    error: 'Collaboration completed but no final solution was generated',\n                    agentNames\n                };\n            }\n            // Generate the final collaborative response\n            const finalResponse = this.generateCollaborativeResponse(finalSolution, qualityMetrics);\n            console.log(`[Conversation Supervisor] ✅ Collaboration completed successfully!`);\n            console.log(`[Conversation Supervisor] 📊 Quality Score: ${qualityMetrics.overallScore.toFixed(2)}/1.0`);\n            return {\n                success: true,\n                finalResponse,\n                agentNames,\n                qualityMetrics,\n                conversationSummary: this.generateConversationSummary()\n            };\n        } catch (error) {\n            console.error(`[Conversation Supervisor] ❌ Collaboration failed:`, error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : 'Unknown error',\n                agentNames: Object.values(this.agentApiKeys).map((key)=>key.label)\n            };\n        }\n    }\n    /**\n   * Build the LangGraph conversation workflow\n   */ buildConversationWorkflow() {\n        const workflow = new _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.StateGraph({\n            channels: {\n                messages: {\n                    value: (x, y)=>x.concat(y),\n                    default: ()=>[]\n                },\n                conversationManager: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.conversationManager\n                },\n                agentApiKeys: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.agentApiKeys\n                },\n                customApiConfigId: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.customApiConfigId\n                },\n                currentAgent: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>undefined\n                },\n                nextAction: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>'continue_discussion'\n                },\n                iterationCount: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>0\n                },\n                maxIterations: {\n                    value: (x, y)=>y ?? x,\n                    default: ()=>this.maxIterations\n                }\n            }\n        });\n        // Add conversation phase nodes\n        workflow.addNode('brainstorming', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createBrainstormingNode());\n        workflow.addNode('debate', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createDebateNode());\n        workflow.addNode('synthesis', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createSynthesisNode());\n        workflow.addNode('validation', _ConversationNodes__WEBPACK_IMPORTED_MODULE_2__.ConversationNodes.createValidationNode());\n        workflow.addNode('supervisor', this.createSupervisorNode());\n        // Define the conversation flow\n        workflow.addEdge(_langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.START, 'brainstorming');\n        workflow.addEdge('brainstorming', 'supervisor');\n        workflow.addEdge('debate', 'supervisor');\n        workflow.addEdge('synthesis', 'supervisor');\n        workflow.addEdge('validation', _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END);\n        // Supervisor decides the next phase\n        workflow.addConditionalEdges('supervisor', this.createConversationRouter(), {\n            'continue_debate': 'debate',\n            'move_to_synthesis': 'synthesis',\n            'move_to_validation': 'validation',\n            'complete': _langchain_langgraph__WEBPACK_IMPORTED_MODULE_0__.END\n        });\n        return workflow.compile();\n    }\n    /**\n   * Create the supervisor node that manages conversation flow\n   */ createSupervisorNode() {\n        return async (state)=>{\n            console.log(`[Supervisor] 👑 Evaluating conversation progress...`);\n            const manager = state.conversationManager;\n            const currentPhase = manager.getCurrentPhase();\n            const qualityMetrics = manager.getQualityMetrics();\n            const unresolvedIssues = manager.getUnresolvedIssues();\n            console.log(`[Supervisor] Phase: ${currentPhase}, Quality: ${qualityMetrics.overallScore.toFixed(2)}, Issues: ${unresolvedIssues.length}`);\n            // Determine next action based on conversation state\n            let nextAction;\n            if (currentPhase === 'brainstorming') {\n                // Move to debate if we have enough ideas\n                const ideaCount = manager.getState().conversationHistory.filter((turn)=>turn.messageType === 'proposal').length;\n                nextAction = ideaCount >= Object.keys(state.agentApiKeys).length ? 'continue_debate' : 'continue_debate';\n            } else if (currentPhase === 'debate') {\n                // Move to synthesis if issues are resolved or we've debated enough\n                const shouldContinueDebate = unresolvedIssues.length > 2 && state.iterationCount < state.maxIterations * 0.6;\n                nextAction = shouldContinueDebate ? 'continue_debate' : 'move_to_synthesis';\n            } else if (currentPhase === 'synthesis') {\n                // Move to validation after synthesis\n                nextAction = 'move_to_validation';\n            } else {\n                // Complete the conversation\n                nextAction = 'complete';\n            }\n            console.log(`[Supervisor] 🎯 Decision: ${nextAction}`);\n            return {\n                nextAction: nextAction,\n                iterationCount: state.iterationCount + 1\n            };\n        };\n    }\n    /**\n   * Create the conversation router that decides next steps\n   */ createConversationRouter() {\n        return (state)=>{\n            const manager = state.conversationManager;\n            const currentPhase = manager.getCurrentPhase();\n            const nextAction = state.nextAction;\n            // Safety check for max iterations\n            if (state.iterationCount >= state.maxIterations) {\n                console.log(`[Router] 🛑 Max iterations reached, moving to completion`);\n                return 'complete';\n            }\n            // Route based on supervisor decision\n            switch(nextAction){\n                case 'continue_discussion':\n                    if (currentPhase === 'brainstorming') return 'continue_debate';\n                    if (currentPhase === 'debate') return 'continue_debate';\n                    return 'move_to_synthesis';\n                case 'advance_phase':\n                    if (currentPhase === 'brainstorming') return 'continue_debate';\n                    if (currentPhase === 'debate') return 'move_to_synthesis';\n                    if (currentPhase === 'synthesis') return 'move_to_validation';\n                    return 'complete';\n                case 'complete':\n                    return 'complete';\n                default:\n                    return nextAction || 'complete';\n            }\n        };\n    }\n    /**\n   * Generate the final collaborative response with proper formatting\n   */ generateCollaborativeResponse(finalSolution, qualityMetrics) {\n        const agentCount = Object.keys(this.agentApiKeys).length;\n        const conversationHistory = this.conversationManager.getState().conversationHistory;\n        const consensusItems = this.conversationManager.getConsensusItems();\n        // Extract key contributions from each agent\n        const agentContributions = Object.entries(this.agentApiKeys).map(([agentId, agentKey])=>{\n            const agentTurns = conversationHistory.filter((turn)=>turn.agent === agentId);\n            const keyContribution = agentTurns.find((turn)=>turn.messageType === 'proposal' || turn.messageType === 'synthesis');\n            return {\n                agent: agentKey.label,\n                contribution: keyContribution?.message.substring(0, 200) + '...' || 0\n            };\n        });\n        return `# 🤖 **True Agent Collaboration Complete**\n\n*This response was created through genuine multi-agent collaboration with real-time discussion, debate, and consensus building between ${agentCount} AI agents.*\n\n## 📋 **Collaborative Solution**\n\n${finalSolution}\n\n## 🗣️ **Agent Contributions & Real Discussion**\n\n${agentContributions.map((contrib)=>`**${contrib.agent}:** ${contrib.contribution}`).join('\\n\\n')}\n\n## 🤝 **Genuine Collaboration Process**\n\n**✅ Brainstorming Phase:** All agents shared initial ideas and explored the problem space together\n**✅ Debate Phase:** Agents challenged each other's ideas, identified conflicts, and refined thinking through real discussion\n**✅ Synthesis Phase:** Team collaborated to resolve disagreements and build the final solution\n**✅ Validation Phase:** All agents validated and approved the final collaborative result\n\n## 📊 **Collaboration Quality Metrics**\n\n- **Overall Quality Score:** ${(qualityMetrics.overallScore * 100).toFixed(0)}%\n- **Participation Balance:** ${(qualityMetrics.participationBalance * 100).toFixed(0)}%\n- **Iterative Improvement:** ${(qualityMetrics.iterativeImprovement * 100).toFixed(0)}%\n- **Consensus Strength:** ${(qualityMetrics.consensusStrength * 100).toFixed(0)}%\n\n## 🎯 **Team Consensus Points**\n\n${consensusItems.length > 0 ? consensusItems.map((item)=>`- **${item.topic}:** ${item.agreedPoint}`).join('\\n') : '- The team reached consensus through iterative discussion and debate'}\n\n**Participating Agents:** ${Object.values(this.agentApiKeys).map((key)=>key.label).join(', ')}\n\n**Collaboration Type:** True multi-agent conversation with real-time discussion, conflict resolution, and consensus building\n\n---\n\n*This solution represents genuine collective intelligence achieved through structured agent-to-agent collaboration, not simulated teamwork.*`;\n    }\n    /**\n   * Generate a summary of the conversation for debugging/analysis\n   */ generateConversationSummary() {\n        const state = this.conversationManager.getState();\n        const turnCount = state.conversationHistory.length;\n        const phaseDistribution = state.conversationHistory.reduce((acc, turn)=>{\n            acc[turn.messageType] = (acc[turn.messageType] || 0) + 1;\n            return acc;\n        }, {});\n        return `\nConversation Summary:\n- Total Turns: ${turnCount}\n- Phases Completed: ${state.currentPhase}\n- Consensus Items: ${state.consensusItems.length}\n- Unresolved Issues: ${state.unresolvedIssues.length}\n- Turn Distribution: ${JSON.stringify(phaseDistribution)}\n- Quality Score: ${state.qualityMetrics.overallScore.toFixed(2)}\n- Duration: ${((Date.now() - state.startTime) / 1000).toFixed(1)}s\n`.trim();\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/agent-collaboration/ConversationSupervisor.ts\n");

/***/ })

};
;