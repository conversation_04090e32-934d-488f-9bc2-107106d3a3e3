"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_orchestration_progressEmitter_ts";
exports.ids = ["_rsc_src_lib_orchestration_progressEmitter_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/orchestration/progressEmitter.ts":
/*!**************************************************!*\
  !*** ./src/lib/orchestration/progressEmitter.ts ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! events */ \"events\");\n/* harmony import */ var events__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(events__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * RouKey Orchestration Progress Emitter\n * \n * Captures orchestration progress events and emits them to the frontend\n * for real-time status updates with proper RouKey branding.\n */ \nclass OrchestrationProgressEmitter extends events__WEBPACK_IMPORTED_MODULE_0__.EventEmitter {\n    constructor(){\n        super(), this.currentColorIndex = 0, this.colors = [\n            'blue',\n            'purple',\n            'indigo',\n            'cyan',\n            'teal',\n            'green',\n            'yellow',\n            'orange',\n            'emerald',\n            'red' // Error\n        ];\n    }\n    static getInstance() {\n        if (!OrchestrationProgressEmitter.instance) {\n            OrchestrationProgressEmitter.instance = new OrchestrationProgressEmitter();\n        }\n        return OrchestrationProgressEmitter.instance;\n    }\n    getNextColor() {\n        const color = this.colors[this.currentColorIndex % this.colors.length];\n        this.currentColorIndex++;\n        return color;\n    }\n    emitProgress(type, message, data) {\n        const event = {\n            id: crypto.randomUUID(),\n            timestamp: new Date().toISOString(),\n            type,\n            message,\n            data,\n            colorIndex: this.currentColorIndex\n        };\n        // Increment color index for visual differentiation\n        this.currentColorIndex++;\n        console.log(`[Progress Emitter] ${message}`);\n        this.emit('progress', event);\n    }\n    // Convenience methods for different progress types\n    classificationStart() {\n        this.emitProgress('classification_start', '🔍 Multi-role task detected by RouKey\\'s Classifier');\n    }\n    classificationComplete(roles, threshold) {\n        this.emitProgress('classification_complete', `✅ Detected ${roles.length} roles: ${roles.join(', ')}`, {\n            roles,\n            threshold\n        });\n    }\n    roleSelectionComplete(selectedRoles, filteredRoles) {\n        this.emitProgress('role_selection', `🎯 Selected ${selectedRoles.length} roles for orchestration`, {\n            selectedRoles,\n            filteredRoles\n        });\n    }\n    workflowSelectionComplete(workflowType, reasoning) {\n        this.emitProgress('workflow_selection', `🏗️ RouKey Multi-Role System selected ${workflowType} workflow`, {\n            workflowType,\n            reasoning\n        });\n    }\n    agentCreationStart() {\n        this.emitProgress('agent_creation_start', '🤖 Creating specialized agents...');\n    }\n    agentCreationComplete(agents) {\n        this.emitProgress('agent_creation_complete', `✅ Created ${agents.length} specialized agents`, {\n            agents\n        });\n    }\n    supervisorInitStart() {\n        this.emitProgress('supervisor_init', '👑 Initializing supervisor coordination...');\n    }\n    supervisorInitComplete(supervisorRole) {\n        this.emitProgress('supervisor_init', '✅ Supervisor ready for coordination', {\n            supervisorRole\n        });\n    }\n    taskPlanningStart() {\n        this.emitProgress('task_planning', '📋 Planning task distribution...');\n    }\n    taskPlanningComplete(plan) {\n        this.emitProgress('task_planning', '✅ Task distribution planned', {\n            plan\n        });\n    }\n    agentWorkStart(role, task) {\n        this.emitProgress('agent_work_start', `🚀 ${role} agent starting work...`, {\n            role,\n            task\n        });\n    }\n    agentWorkComplete(role, result) {\n        this.emitProgress('agent_work_complete', `✅ ${role} agent completed work`, {\n            role,\n            result\n        });\n    }\n    supervisorSynthesisStart() {\n        this.emitProgress('supervisor_synthesis', '🔄 Supervisor synthesizing results...');\n    }\n    supervisorSynthesisComplete(synthesis) {\n        this.emitProgress('supervisor_synthesis', '✅ Final synthesis complete', {\n            synthesis\n        });\n    }\n    orchestrationComplete(result) {\n        this.emitProgress('orchestration_complete', '🎉 RouKey Multi-Role orchestration complete!', {\n            result\n        });\n    }\n    error(step, error) {\n        this.emitProgress('error', `❌ Error in ${step}: ${error}`, {\n            step,\n            error\n        });\n    }\n    // Conversation tracking methods\n    supervisorMessage(message, context) {\n        this.emitProgress('supervisor_message', `👑 Supervisor: ${message}`, {\n            speaker: 'supervisor',\n            message,\n            timestamp: new Date().toISOString(),\n            ...context\n        });\n    }\n    agentMessage(role, message, context) {\n        const roleEmoji = this.getRoleEmoji(role);\n        this.emitProgress('agent_message', `${roleEmoji} ${role}: ${message}`, {\n            speaker: role,\n            message,\n            timestamp: new Date().toISOString(),\n            ...context\n        });\n    }\n    conversationUpdate(participants, currentSpeaker) {\n        this.emitProgress('conversation_update', `💬 Conversation between ${participants.join(', ')}`, {\n            participants,\n            currentSpeaker,\n            timestamp: new Date().toISOString()\n        });\n    }\n    // Helper method to get role-specific emojis\n    getRoleEmoji(role) {\n        const emojiMap = {\n            'brainstorming_ideation': '🧠',\n            'coding_backend': '💻',\n            'coding_frontend': '🎨',\n            'writing': '✍️',\n            'research_synthesis': '🔍',\n            'summarization_briefing': '📋',\n            'translation_localization': '🌐',\n            'data_extraction_structuring': '📊',\n            'general_chat': '💬'\n        };\n        return emojiMap[role] || '🤖';\n    }\n    // Reset color index for new orchestration sessions\n    resetColorIndex() {\n        this.currentColorIndex = 0;\n    }\n    // Get current color for UI\n    getCurrentColor() {\n        return this.colors[(this.currentColorIndex - 1) % this.colors.length] || 'blue';\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OrchestrationProgressEmitter);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/orchestration/progressEmitter.ts\n");

/***/ })

};
;