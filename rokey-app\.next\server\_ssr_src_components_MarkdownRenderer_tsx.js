"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_MarkdownRenderer_tsx";
exports.ids = ["_ssr_src_components_MarkdownRenderer_tsx"];
exports.modules = {

/***/ "(ssr)/./src/components/MarkdownRenderer.tsx":
/*!*********************************************!*\
  !*** ./src/components/MarkdownRenderer.tsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ MarkdownRenderer)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react_markdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! react-markdown */ \"(ssr)/./node_modules/react-markdown/lib/index.js\");\n/* harmony import */ var remark_gfm__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! remark-gfm */ \"(ssr)/./node_modules/remark-gfm/lib/index.js\");\n/* harmony import */ var _barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Prism!=!react-syntax-highlighter */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/prism.js\");\n/* harmony import */ var react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-syntax-highlighter/dist/esm/styles/prism */ \"(ssr)/./node_modules/react-syntax-highlighter/dist/esm/styles/prism/one-dark.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _CopyButton__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./CopyButton */ \"(ssr)/./src/components/CopyButton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n// Error Boundary component for handling markdown rendering errors\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_1__.Component {\n    constructor(props){\n        super(props);\n        this.state = {\n            hasError: false\n        };\n    }\n    static getDerivedStateFromError() {\n        return {\n            hasError: true\n        };\n    }\n    componentDidCatch() {\n        this.props.onError();\n    }\n    render() {\n        if (this.state.hasError) {\n            return null; // Let parent component handle the error display\n        }\n        return this.props.children;\n    }\n}\n// Custom remark plugin to prevent code blocks from being wrapped in paragraphs\nfunction remarkUnwrapCodeBlocks() {\n    return (tree)=>{\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.visit)(tree, 'paragraph', (node, index, parent)=>{\n            if (node.children.length === 1 && node.children[0].type === 'code' && typeof index === 'number' && parent && parent.children) {\n                // Replace paragraph containing only a code block with the code block itself\n                parent.children[index] = node.children[0];\n            }\n        });\n    };\n}\nfunction MarkdownRenderer({ content, className = '' }) {\n    const [hasError, setHasError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkdownRenderer.useEffect\": ()=>{\n            setHasError(false);\n        }\n    }[\"MarkdownRenderer.useEffect\"], [\n        content\n    ]);\n    // Preprocess content to handle plain text with line breaks\n    const preprocessContent = (text)=>{\n        // If content already has markdown formatting (headers, lists, etc.), return as-is\n        if (text.includes('# ') || text.includes('## ') || text.includes('* ') || text.includes('- ') || text.includes('**') || text.includes('```')) {\n            return text;\n        }\n        // For plain text content, convert line breaks to proper markdown paragraphs\n        // Split by double line breaks first (already proper paragraphs)\n        const paragraphs = text.split(/\\n\\s*\\n/);\n        // Process each paragraph to handle single line breaks within paragraphs\n        const processedParagraphs = paragraphs.map((paragraph)=>{\n            // Trim whitespace\n            paragraph = paragraph.trim();\n            if (!paragraph) return '';\n            // If paragraph contains single line breaks, treat them as soft breaks\n            // Replace single line breaks with spaces, but preserve intentional breaks\n            return paragraph.replace(/\\n(?!\\n)/g, ' ');\n        });\n        // Join paragraphs with double line breaks for proper markdown formatting\n        return processedParagraphs.filter((p)=>p).join('\\n\\n');\n    };\n    const processedContent = preprocessContent(content);\n    if (hasError) {\n        // Fallback to simple text rendering if markdown fails\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `markdown-content ${className}`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                className: \"whitespace-pre-wrap text-sm text-white leading-relaxed\",\n                children: content\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 94,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 93,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `markdown-content ${className}`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n            fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-pulse bg-gray-100 rounded p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded mb-2 w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 11\n                    }, void 0),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 11\n                    }, void 0)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 104,\n                columnNumber: 9\n            }, void 0),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ErrorBoundary, {\n                onError: ()=>setHasError(true),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_markdown__WEBPACK_IMPORTED_MODULE_4__.Markdown, {\n                    remarkPlugins: [\n                        remark_gfm__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n                        remarkUnwrapCodeBlocks\n                    ],\n                    components: {\n                        // Headers\n                        h1: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-3 mt-4 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, void 0),\n                        h2: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-lg font-bold mb-2 mt-3 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, void 0),\n                        h3: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-base font-bold mb-2 mt-3 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, void 0),\n                        h4: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-sm font-bold mb-1 mt-2 first:mt-0 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 131,\n                                columnNumber: 11\n                            }, void 0),\n                        // Paragraphs\n                        p: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mb-3 last:mb-0 leading-relaxed text-white break-words\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 138,\n                                columnNumber: 11\n                            }, void 0),\n                        // Bold and italic\n                        strong: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                className: \"font-bold text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 145,\n                                columnNumber: 11\n                            }, void 0),\n                        em: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                                className: \"italic text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 11\n                            }, void 0),\n                        // Lists\n                        ul: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"list-disc list-inside mb-3 space-y-1 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, void 0),\n                        ol: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ol\", {\n                                className: \"list-decimal list-inside mb-3 space-y-1 text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 11\n                            }, void 0),\n                        li: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"leading-relaxed text-white\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 167,\n                                columnNumber: 11\n                            }, void 0),\n                        // Code blocks and inline code\n                        code: ({ node, inline, className, children, ...props })=>{\n                            const match = /language-(\\w+)/.exec(className || '');\n                            const language = match ? match[1] : '';\n                            const codeContent = String(children).replace(/\\n$/, '');\n                            if (!inline) {\n                                // Check if this is a short single-line code snippet that should be treated as enhanced inline\n                                const isShortSnippet = codeContent.length <= 60 && !codeContent.includes('\\n') && !language;\n                                if (isShortSnippet) {\n                                    // Treat short snippets as enhanced inline code with subtle highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                        className: \"bg-orange-50 text-orange-700 px-1.5 py-0.5 rounded text-sm font-mono border border-orange-200\",\n                                        children: codeContent\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                                // Handle actual code blocks (both with and without language detection)\n                                if (language) {\n                                    // Code block with syntax highlighting\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 198,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Prism_react_syntax_highlighter__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                style: react_syntax_highlighter_dist_esm_styles_prism__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n                                                language: language,\n                                                PreTag: \"div\",\n                                                className: \"text-sm\",\n                                                ...props,\n                                                children: codeContent\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 17\n                                    }, void 0);\n                                } else {\n                                    // Multi-line code block without language (plain text code block)\n                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"my-3 rounded-lg overflow-hidden relative group bg-gray-900 text-gray-100\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-3 right-3 z-20 opacity-70 hover:opacity-100 transition-opacity duration-200\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CopyButton__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                    text: codeContent,\n                                                    variant: \"code\",\n                                                    size: \"sm\",\n                                                    title: \"Copy code\",\n                                                    className: \"bg-gray-800/80 hover:bg-gray-700/90 backdrop-blur-sm border border-gray-600/50\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 223,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 222,\n                                                columnNumber: 19\n                                            }, void 0),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"pre\", {\n                                                className: \"p-4 text-sm font-mono overflow-x-auto\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                                    children: codeContent\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                    lineNumber: 232,\n                                                    columnNumber: 21\n                                                }, void 0)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 19\n                                            }, void 0)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                        lineNumber: 220,\n                                        columnNumber: 17\n                                    }, void 0);\n                                }\n                            }\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"code\", {\n                                className: \"bg-gray-800 text-gray-100 px-1.5 py-0.5 rounded text-sm font-mono\",\n                                ...props,\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 240,\n                                columnNumber: 13\n                            }, void 0);\n                        },\n                        // Blockquotes\n                        blockquote: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"blockquote\", {\n                                className: \"border-l-4 border-orange-500 pl-4 my-3 italic text-gray-300\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 251,\n                                columnNumber: 11\n                            }, void 0),\n                        // Links\n                        a: ({ children, href })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"text-orange-600 hover:text-orange-700 underline transition-colors duration-200\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 258,\n                                columnNumber: 11\n                            }, void 0),\n                        // Tables\n                        table: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"overflow-x-auto my-3\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                    className: \"min-w-full border border-gray-600 rounded-lg\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                    lineNumber: 271,\n                                    columnNumber: 13\n                                }, void 0)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 270,\n                                columnNumber: 11\n                            }, void 0),\n                        thead: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                className: \"bg-gray-800\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 277,\n                                columnNumber: 11\n                            }, void 0),\n                        tbody: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                className: \"divide-y divide-gray-600 bg-gray-900\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, void 0),\n                        tr: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                className: \"hover:bg-gray-800\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, void 0),\n                        th: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                className: \"px-3 py-2 text-left text-xs font-medium text-gray-300 uppercase tracking-wider border-b border-gray-600\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 292,\n                                columnNumber: 11\n                            }, void 0),\n                        td: ({ children })=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                className: \"px-3 py-2 text-sm text-white border-b border-gray-600\",\n                                children: children\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, void 0),\n                        // Horizontal rule\n                        hr: ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"hr\", {\n                                className: \"my-4 border-gray-600\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, void 0)\n                    },\n                    children: processedContent\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n                lineNumber: 110,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n            lineNumber: 103,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\MarkdownRenderer.tsx\",\n        lineNumber: 102,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/MarkdownRenderer.tsx\n");

/***/ })

};
;