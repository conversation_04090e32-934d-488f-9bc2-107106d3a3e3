"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_utils_clearUserCache_ts";
exports.ids = ["_ssr_src_utils_clearUserCache_ts"];
exports.modules = {

/***/ "(ssr)/./src/utils/clearUserCache.ts":
/*!*************************************!*\
  !*** ./src/utils/clearUserCache.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearAllUserCache: () => (/* binding */ clearAllUserCache),\n/* harmony export */   clearUserSpecificCache: () => (/* binding */ clearUserSpecificCache)\n/* harmony export */ });\n// Utility to clear all user-related cache and storage\nasync function clearAllUserCache() {\n    console.log('Clearing all user cache and storage...');\n    try {\n        // Clear localStorage\n        if (false) {}\n    } catch (error) {\n        console.warn('Failed to clear localStorage:', error);\n    }\n    try {\n        // Clear sessionStorage\n        if (false) {}\n    } catch (error) {\n        console.warn('Failed to clear sessionStorage:', error);\n    }\n    try {\n        // Clear advanced cache\n        const { globalCache } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/utils/advancedCache */ \"(ssr)/./src/utils/advancedCache.ts\"));\n        globalCache.clear();\n        console.log('Advanced cache cleared');\n    } catch (error) {\n        console.warn('Failed to clear advanced cache:', error);\n    }\n    try {\n        // Clear any browser cache for API requests\n        if ('caches' in window) {\n            const cacheNames = await caches.keys();\n            await Promise.all(cacheNames.map((cacheName)=>caches.delete(cacheName)));\n            console.log('Browser caches cleared');\n        }\n    } catch (error) {\n        console.warn('Failed to clear browser caches:', error);\n    }\n    try {\n        // Clear any IndexedDB data (if used)\n        if ('indexedDB' in window) {\n            // This is a more aggressive approach - you might want to be more selective\n            console.log('IndexedDB clearing not implemented (would need specific database names)');\n        }\n    } catch (error) {\n        console.warn('Failed to clear IndexedDB:', error);\n    }\n    console.log('Cache clearing completed');\n}\n// Clear user-specific cache entries only (less aggressive)\nasync function clearUserSpecificCache(userId) {\n    console.log('Clearing user-specific cache for user:', userId);\n    try {\n        // Clear advanced cache with user tags\n        const { globalCache } = await Promise.resolve(/*! import() */).then(__webpack_require__.bind(__webpack_require__, /*! @/utils/advancedCache */ \"(ssr)/./src/utils/advancedCache.ts\"));\n        globalCache.invalidateByTags([\n            'user',\n            'subscription',\n            'usage'\n        ]);\n        if (userId) {\n            globalCache.invalidateByTags([\n                `user_${userId}`\n            ]);\n        }\n        console.log('User-specific cache cleared');\n    } catch (error) {\n        console.warn('Failed to clear user-specific cache:', error);\n    }\n    try {\n        // Clear user-specific localStorage items\n        if (false) {}\n    } catch (error) {\n        console.warn('Failed to clear user-specific localStorage:', error);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/utils/clearUserCache.ts\n");

/***/ })

};
;