/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/activity/route";
exports.ids = ["app/api/activity/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Factivity%2Froute&page=%2Fapi%2Factivity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Factivity%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Factivity%2Froute&page=%2Fapi%2Factivity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Factivity%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_activity_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/activity/route.ts */ \"(rsc)/./src/app/api/activity/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/activity/route\",\n        pathname: \"/api/activity\",\n        filename: \"route\",\n        bundlePath: \"app/api/activity/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\activity\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_activity_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Factivity%2Froute&page=%2Fapi%2Factivity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Factivity%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/activity/route.ts":
/*!***************************************!*\
  !*** ./src/app/api/activity/route.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\nasync function GET(request) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        if (!supabase) {\n            console.error('Supabase client could not be initialized in /api/activity.');\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Server configuration error.'\n            }, {\n                status: 500\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const limit = parseInt(searchParams.get('limit') || '10');\n        // Get recent request logs for activity feed (including workflows)\n        const { data: logs, error: logsError } = await supabase.from('request_logs').select(`\n        id,\n        request_timestamp,\n        status_code,\n        llm_model_name,\n        llm_provider_name,\n        error_message,\n        cost,\n        input_tokens,\n        output_tokens,\n        custom_api_config_id,\n        workflow_id,\n        role_used,\n        processing_duration_ms\n      `).order('request_timestamp', {\n            ascending: false\n        }).limit(limit);\n        if (logsError) {\n            console.error('Error fetching activity logs:', logsError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch activity data'\n            }, {\n                status: 500\n            });\n        }\n        // Get recent API key additions\n        const { data: apiKeys, error: keysError } = await supabase.from('api_keys').select(`\n        id,\n        created_at,\n        label,\n        provider,\n        predefined_model_id\n      `).order('created_at', {\n            ascending: false\n        }).limit(5);\n        if (keysError) {\n            console.error('Error fetching API keys:', keysError);\n        }\n        // Get recent custom config updates\n        const { data: configs, error: configsError } = await supabase.from('custom_api_configs').select(`\n        id,\n        updated_at,\n        name,\n        routing_strategy\n      `).order('updated_at', {\n            ascending: false\n        }).limit(5);\n        if (configsError) {\n            console.error('Error fetching configs:', configsError);\n        }\n        // Combine and format activity data\n        const activities = [];\n        // Add request activities (router configs and workflows)\n        if (logs) {\n            logs.forEach((log)=>{\n                const isSuccess = log.status_code >= 200 && log.status_code < 300;\n                const isError = log.status_code >= 400;\n                const isWorkflow = log.workflow_id !== null;\n                let action = isWorkflow ? 'Workflow executed' : 'Request completed';\n                let status = 'success';\n                if (isError) {\n                    action = isWorkflow ? 'Workflow failed' : 'Request failed';\n                    status = 'error';\n                } else if (log.status_code >= 300) {\n                    action = isWorkflow ? 'Workflow redirected' : 'Request redirected';\n                    status = 'warning';\n                }\n                // For workflows, show execution time instead of just tokens\n                let performanceInfo = null;\n                if (isWorkflow && log.processing_duration_ms) {\n                    performanceInfo = `${log.processing_duration_ms}ms execution`;\n                } else if (log.input_tokens && log.output_tokens) {\n                    performanceInfo = `${log.input_tokens} in, ${log.output_tokens} out`;\n                }\n                activities.push({\n                    id: `${isWorkflow ? 'workflow' : 'request'}-${log.id}`,\n                    type: isWorkflow ? 'workflow' : 'request',\n                    action,\n                    model: log.llm_model_name || (isWorkflow ? 'Multi-Model Workflow' : 'Unknown Model'),\n                    provider: log.llm_provider_name || (isWorkflow ? 'Multi-Provider' : null),\n                    timestamp: log.request_timestamp,\n                    status,\n                    details: log.error_message,\n                    cost: log.cost,\n                    tokens: performanceInfo,\n                    workflow_id: log.workflow_id,\n                    config_id: log.custom_api_config_id\n                });\n            });\n        }\n        // Add API key activities\n        if (apiKeys) {\n            apiKeys.forEach((key)=>{\n                activities.push({\n                    id: `key-${key.id}`,\n                    type: 'api_key',\n                    action: 'New API key added',\n                    model: key.predefined_model_id || key.provider,\n                    provider: key.provider,\n                    timestamp: key.created_at,\n                    status: 'info',\n                    details: key.label\n                });\n            });\n        }\n        // Add config activities\n        if (configs) {\n            configs.forEach((config)=>{\n                activities.push({\n                    id: `config-${config.id}`,\n                    type: 'config',\n                    action: 'Configuration updated',\n                    model: config.name,\n                    provider: config.routing_strategy,\n                    timestamp: config.updated_at,\n                    status: 'info',\n                    details: `Routing strategy: ${config.routing_strategy}`\n                });\n            });\n        }\n        // Get recent workflow updates\n        const { data: workflows, error: workflowsError } = await supabase.from('manual_build_workflows').select(`\n        id,\n        updated_at,\n        name,\n        node_count,\n        is_active\n      `).order('updated_at', {\n            ascending: false\n        }).limit(5);\n        if (workflowsError) {\n            console.error('Error fetching workflows:', workflowsError);\n        }\n        // Add workflow activities\n        if (workflows) {\n            workflows.forEach((workflow)=>{\n                activities.push({\n                    id: `workflow-update-${workflow.id}`,\n                    type: 'workflow_update',\n                    action: 'Workflow updated',\n                    model: workflow.name,\n                    provider: `${workflow.node_count} nodes`,\n                    timestamp: workflow.updated_at,\n                    status: workflow.is_active ? 'info' : 'warning',\n                    details: workflow.is_active ? 'Active workflow' : 'Inactive workflow'\n                });\n            });\n        }\n        // Sort by timestamp and limit\n        activities.sort((a, b)=>new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());\n        const limitedActivities = activities.slice(0, limit);\n        // Format timestamps\n        const formattedActivities = limitedActivities.map((activity)=>({\n                ...activity,\n                time: getTimeAgo(new Date(activity.timestamp))\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            activities: formattedActivities,\n            total: activities.length\n        });\n    } catch (error) {\n        console.error('Error in /api/activity:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\nfunction getTimeAgo(date) {\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) return 'Just now';\n    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} minutes ago`;\n    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} hours ago`;\n    return `${Math.floor(diffInSeconds / 86400)} days ago`;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/activity/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Factivity%2Froute&page=%2Fapi%2Factivity%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Factivity%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();