/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/analytics/summary/route";
exports.ids = ["app/api/analytics/summary/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fsummary%2Froute&page=%2Fapi%2Fanalytics%2Fsummary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fsummary%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fsummary%2Froute&page=%2Fapi%2Fanalytics%2Fsummary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fsummary%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_analytics_summary_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/analytics/summary/route.ts */ \"(rsc)/./src/app/api/analytics/summary/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/analytics/summary/route\",\n        pathname: \"/api/analytics/summary\",\n        filename: \"route\",\n        bundlePath: \"app/api/analytics/summary/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\analytics\\\\summary\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_analytics_summary_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fsummary%2Froute&page=%2Fapi%2Fanalytics%2Fsummary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fsummary%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/analytics/summary/route.ts":
/*!************************************************!*\
  !*** ./src/app/api/analytics/summary/route.ts ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _utils_logFormatting__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/utils/logFormatting */ \"(rsc)/./src/utils/logFormatting.ts\");\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! zod */ \"(rsc)/./node_modules/zod/dist/esm/index.js\");\n\n\n\n\n// Schema for query parameters\nconst AnalyticsQuerySchema = zod__WEBPACK_IMPORTED_MODULE_3__.z.object({\n    startDate: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().datetime({\n        offset: true\n    }).optional(),\n    endDate: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().datetime({\n        offset: true\n    }).optional(),\n    customApiConfigId: zod__WEBPACK_IMPORTED_MODULE_3__.z.string().uuid().optional(),\n    groupBy: zod__WEBPACK_IMPORTED_MODULE_3__.z[\"enum\"]([\n        'day',\n        'week',\n        'month',\n        'provider',\n        'model'\n    ]).optional().default('day')\n});\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const queryParams = Object.fromEntries(searchParams.entries());\n        // Validate query parameters\n        const validatedParams = AnalyticsQuerySchema.parse(queryParams);\n        const { startDate, endDate, customApiConfigId, groupBy } = validatedParams;\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientOnRequest)();\n        // Check authentication\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            console.error('Authentication failed in /api/analytics/summary:', authError);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized: You must be logged in to view analytics.'\n            }, {\n                status: 401\n            });\n        }\n        // Build base query\n        let query = supabase.from('request_logs').select(`\n        request_timestamp,\n        status_code,\n        cost,\n        input_tokens,\n        output_tokens,\n        llm_provider_name,\n        llm_model_name,\n        custom_api_config_id\n      `).eq('user_id', user.id) // Filter by authenticated user\n        .not('cost', 'is', null); // Only include records with cost data\n        // Apply filters\n        if (startDate) {\n            query = query.gte('request_timestamp', startDate);\n        }\n        if (endDate) {\n            query = query.lte('request_timestamp', endDate);\n        }\n        if (customApiConfigId) {\n            query = query.eq('custom_api_config_id', customApiConfigId);\n        }\n        const { data: logs, error } = await query.order('request_timestamp', {\n            ascending: false\n        });\n        if (error) {\n            console.error('[Analytics API Error]', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch analytics data',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Calculate summary statistics\n        const totalRequests = logs.length;\n        const successfulRequests = logs.filter((log)=>log.status_code && log.status_code < 400).length;\n        const totalCost = logs.reduce((sum, log)=>sum + (log.cost || 0), 0);\n        const totalInputTokens = logs.reduce((sum, log)=>sum + (log.input_tokens || 0), 0);\n        const totalOutputTokens = logs.reduce((sum, log)=>sum + (log.output_tokens || 0), 0);\n        // Group data based on groupBy parameter\n        let groupedData = [];\n        if (groupBy === 'provider') {\n            const providerStats = logs.reduce((acc, log)=>{\n                const rawProvider = log.llm_provider_name || 'Unknown';\n                const provider = (0,_utils_logFormatting__WEBPACK_IMPORTED_MODULE_2__.formatProviderName)(rawProvider); // Apply formatting here\n                if (!acc[provider]) {\n                    acc[provider] = {\n                        name: provider,\n                        requests: 0,\n                        cost: 0,\n                        input_tokens: 0,\n                        output_tokens: 0,\n                        success_rate: 0\n                    };\n                }\n                acc[provider].requests += 1;\n                acc[provider].cost += log.cost || 0;\n                acc[provider].input_tokens += log.input_tokens || 0;\n                acc[provider].output_tokens += log.output_tokens || 0;\n                if (log.status_code && log.status_code < 400) {\n                    acc[provider].success_rate += 1;\n                }\n                return acc;\n            }, {});\n            groupedData = Object.values(providerStats).map((provider)=>({\n                    ...provider,\n                    success_rate: provider.requests > 0 ? provider.success_rate / provider.requests * 100 : 0\n                }));\n        } else if (groupBy === 'model') {\n            const modelStats = logs.reduce((acc, log)=>{\n                const model = log.llm_model_name || 'Unknown';\n                if (!acc[model]) {\n                    acc[model] = {\n                        name: model,\n                        requests: 0,\n                        cost: 0,\n                        input_tokens: 0,\n                        output_tokens: 0,\n                        provider: log.llm_provider_name || 'Unknown'\n                    };\n                }\n                acc[model].requests += 1;\n                acc[model].cost += log.cost || 0;\n                acc[model].input_tokens += log.input_tokens || 0;\n                acc[model].output_tokens += log.output_tokens || 0;\n                return acc;\n            }, {});\n            groupedData = Object.values(modelStats);\n        } else {\n            // Group by time period (day, week, month)\n            const timeStats = logs.reduce((acc, log)=>{\n                const date = new Date(log.request_timestamp);\n                let key;\n                if (groupBy === 'day') {\n                    key = date.toISOString().split('T')[0]; // YYYY-MM-DD\n                } else if (groupBy === 'week') {\n                    const weekStart = new Date(date);\n                    weekStart.setDate(date.getDate() - date.getDay());\n                    key = weekStart.toISOString().split('T')[0];\n                } else {\n                    key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;\n                }\n                if (!acc[key]) {\n                    acc[key] = {\n                        period: key,\n                        requests: 0,\n                        cost: 0,\n                        input_tokens: 0,\n                        output_tokens: 0\n                    };\n                }\n                acc[key].requests += 1;\n                acc[key].cost += log.cost || 0;\n                acc[key].input_tokens += log.input_tokens || 0;\n                acc[key].output_tokens += log.output_tokens || 0;\n                return acc;\n            }, {});\n            groupedData = Object.values(timeStats).sort((a, b)=>a.period.localeCompare(b.period));\n        }\n        const response = {\n            summary: {\n                total_requests: totalRequests,\n                successful_requests: successfulRequests,\n                success_rate: totalRequests > 0 ? successfulRequests / totalRequests * 100 : 0,\n                total_cost: totalCost,\n                total_input_tokens: totalInputTokens,\n                total_output_tokens: totalOutputTokens,\n                total_tokens: totalInputTokens + totalOutputTokens,\n                average_cost_per_request: totalRequests > 0 ? totalCost / totalRequests : 0\n            },\n            grouped_data: groupedData,\n            filters: {\n                start_date: startDate,\n                end_date: endDate,\n                custom_api_config_id: customApiConfigId,\n                group_by: groupBy\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(response);\n    } catch (error) {\n        console.error('[Analytics API Error]', error);\n        if (error instanceof zod__WEBPACK_IMPORTED_MODULE_3__.z.ZodError) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid query parameters',\n                details: error.errors\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal server error',\n            details: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/analytics/summary/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(rsc)/./src/utils/logFormatting.ts":
/*!************************************!*\
  !*** ./src/utils/logFormatting.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatModelName: () => (/* binding */ formatModelName),\n/* harmony export */   formatProviderName: () => (/* binding */ formatProviderName),\n/* harmony export */   formatRoleName: () => (/* binding */ formatRoleName),\n/* harmony export */   generateRoleUsedMessage: () => (/* binding */ generateRoleUsedMessage),\n/* harmony export */   getRoleUsedBadgeClass: () => (/* binding */ getRoleUsedBadgeClass),\n/* harmony export */   transformRoleUsed: () => (/* binding */ transformRoleUsed)\n/* harmony export */ });\n/**\n * Utility functions for transforming debug-style log messages into production-ready, user-friendly text\n */ /**\n * Check if a string looks like a role name (vs a technical debug pattern)\n */ const isLikelyRoleName = (str)=>{\n    // Exclude obvious technical patterns\n    const technicalPatterns = [\n        /default_key/i,\n        /attempt_\\d+/i,\n        /status_\\d+/i,\n        /failed/i,\n        /success/i,\n        /complexity_rr/i,\n        /fallback_position/i,\n        /^[a-f0-9-]{8,}/i,\n        /_then_/i,\n        /classification_/i,\n        /no_prompt/i,\n        /error/i\n    ];\n    // If it matches any technical pattern, it's not a role name\n    if (technicalPatterns.some((pattern)=>pattern.test(str))) {\n        return false;\n    }\n    // If it's a simple word or snake_case without numbers/technical terms, likely a role\n    return /^[a-z_]+$/i.test(str) && str.length > 2 && str.length < 50;\n};\n/**\n * Transform debug-style role_used messages into user-friendly text\n */ const transformRoleUsed = (roleUsed)=>{\n    if (!roleUsed) return {\n        text: 'N/A',\n        type: 'fallback'\n    };\n    // Handle simple role names first (clean role names without technical patterns)\n    if (isLikelyRoleName(roleUsed)) {\n        return {\n            text: formatRoleName(roleUsed),\n            type: 'role',\n            details: `Role-based routing: ${formatRoleName(roleUsed)}`\n        };\n    }\n    // Handle default key success patterns\n    if (roleUsed.includes('default_key') && roleUsed.includes('success')) {\n        const attemptMatch = roleUsed.match(/attempt_(\\d+)/);\n        const attempt = attemptMatch ? parseInt(attemptMatch[1]) : 1;\n        return {\n            text: attempt === 1 ? 'Default Key' : `Default Key (Attempt ${attempt})`,\n            type: 'success',\n            details: attempt > 1 ? `Required ${attempt} attempts to succeed` : undefined\n        };\n    }\n    // Handle default key failure patterns\n    if (roleUsed.includes('default_key') && roleUsed.includes('failed')) {\n        const attemptMatch = roleUsed.match(/attempt_(\\d+)/);\n        const statusMatch = roleUsed.match(/status_(\\w+)/);\n        const attempt = attemptMatch ? parseInt(attemptMatch[1]) : 1;\n        const status = statusMatch ? statusMatch[1] : 'unknown';\n        return {\n            text: `Failed (Attempt ${attempt})`,\n            type: 'error',\n            details: `Failed with status: ${status}`\n        };\n    }\n    // Handle multiple attempts failed\n    if (roleUsed.includes('default_all') && roleUsed.includes('attempts_failed')) {\n        const countMatch = roleUsed.match(/default_all_(\\d+)_attempts/);\n        const count = countMatch ? parseInt(countMatch[1]) : 0;\n        return {\n            text: `All Keys Failed (${count} attempts)`,\n            type: 'error',\n            details: `Tried ${count} different API keys, all failed`\n        };\n    }\n    // Handle enhanced complexity-based routing with proximal search details\n    if (roleUsed.includes('complexity_rr_clsf_') || roleUsed.includes('complexity_level_')) {\n        // Enhanced pattern: complexity_rr_clsf_3_used_lvl_4_key_selected\n        const enhancedMatch = roleUsed.match(/complexity_rr_clsf_(\\d+)_used_lvl_(\\d+)/);\n        if (enhancedMatch) {\n            const classifiedLevel = enhancedMatch[1];\n            const usedLevel = enhancedMatch[2];\n            if (classifiedLevel === usedLevel) {\n                return {\n                    text: `Complexity Level ${usedLevel}`,\n                    type: 'success',\n                    details: `Classified and routed to complexity level ${usedLevel}`\n                };\n            } else {\n                return {\n                    text: `Complexity ${classifiedLevel}→${usedLevel}`,\n                    type: 'success',\n                    details: `Classified as level ${classifiedLevel}, routed to available level ${usedLevel}`\n                };\n            }\n        }\n        // Simple pattern: complexity_level_3\n        const levelMatch = roleUsed.match(/complexity_level_(\\d+)/);\n        if (levelMatch) {\n            const level = levelMatch[1];\n            return {\n                text: `Complexity Level ${level}`,\n                type: 'success',\n                details: `Routed based on prompt complexity analysis`\n            };\n        }\n    }\n    // Handle strict fallback\n    if (roleUsed.includes('fallback_position_')) {\n        const posMatch = roleUsed.match(/fallback_position_(\\d+)/);\n        const position = posMatch ? parseInt(posMatch[1]) : 0;\n        return {\n            text: `Fallback Key #${position + 1}`,\n            type: 'success',\n            details: `Used fallback key at position ${position + 1}`\n        };\n    }\n    // Handle intelligent role routing\n    if (roleUsed.includes('intelligent_role_')) {\n        const roleMatch = roleUsed.match(/intelligent_role_(.+)$/);\n        const detectedRole = roleMatch ? roleMatch[1] : 'unknown';\n        return {\n            text: `Smart: ${formatRoleName(detectedRole)}`,\n            type: 'role',\n            details: `AI detected role: ${formatRoleName(detectedRole)}`\n        };\n    }\n    // Enhanced fallback: Extract meaningful information from any unrecognized pattern\n    return extractMeaningfulInfo(roleUsed);\n};\n/**\n * Extract meaningful information from unrecognized role_used patterns\n */ const extractMeaningfulInfo = (roleUsed)=>{\n    // Try to extract complexity information\n    const complexityMatch = roleUsed.match(/complexity[_\\s]*(\\d+)/i);\n    if (complexityMatch) {\n        const level = complexityMatch[1];\n        return {\n            text: `Complexity Level ${level}`,\n            type: 'success',\n            details: `Extracted complexity level ${level} from routing pattern`\n        };\n    }\n    // Try to extract role names from complex patterns\n    const roleNameMatch = extractRoleFromPattern(roleUsed);\n    if (roleNameMatch) {\n        return {\n            text: formatRoleName(roleNameMatch),\n            type: 'role',\n            details: `Extracted role: ${formatRoleName(roleNameMatch)}`\n        };\n    }\n    // Try to extract fallback information\n    const fallbackMatch = roleUsed.match(/fallback[_\\s]*(\\d+)/i);\n    if (fallbackMatch) {\n        const position = parseInt(fallbackMatch[1]);\n        return {\n            text: `Fallback Key #${position + 1}`,\n            type: 'success',\n            details: `Extracted fallback position ${position + 1}`\n        };\n    }\n    // Try to extract attempt information\n    const attemptMatch = roleUsed.match(/attempt[_\\s]*(\\d+)/i);\n    if (attemptMatch) {\n        const attempt = parseInt(attemptMatch[1]);\n        const isSuccess = roleUsed.toLowerCase().includes('success');\n        const isFailed = roleUsed.toLowerCase().includes('fail');\n        if (isSuccess) {\n            return {\n                text: attempt === 1 ? 'Default Key' : `Default Key (Attempt ${attempt})`,\n                type: 'success',\n                details: `Extracted success on attempt ${attempt}`\n            };\n        } else if (isFailed) {\n            return {\n                text: `Failed (Attempt ${attempt})`,\n                type: 'error',\n                details: `Extracted failure on attempt ${attempt}`\n            };\n        }\n    }\n    // Last resort: try to clean up the raw string for display\n    const cleanedText = cleanRawRoleUsed(roleUsed);\n    return {\n        text: cleanedText,\n        type: 'fallback',\n        details: `Raw routing pattern: ${roleUsed}`\n    };\n};\n/**\n * Extract role names from complex patterns\n */ const extractRoleFromPattern = (str)=>{\n    // Look for patterns like \"classified_as_ROLENAME_something\"\n    const classifiedMatch = str.match(/classified_as_([a-z_]+)_/i);\n    if (classifiedMatch && isLikelyRoleName(classifiedMatch[1])) {\n        return classifiedMatch[1];\n    }\n    // Look for patterns like \"role_ROLENAME_something\"\n    const roleMatch = str.match(/role_([a-z_]+)_/i);\n    if (roleMatch && isLikelyRoleName(roleMatch[1])) {\n        return roleMatch[1];\n    }\n    // Look for patterns like \"fb_role_ROLENAME\"\n    const fbRoleMatch = str.match(/fb_role_([a-z_]+)/i);\n    if (fbRoleMatch && isLikelyRoleName(fbRoleMatch[1])) {\n        return fbRoleMatch[1];\n    }\n    return null;\n};\n/**\n * Clean up raw role_used strings for display as last resort\n */ const cleanRawRoleUsed = (str)=>{\n    // Remove common technical prefixes/suffixes\n    const cleaned = str.replace(/^default_key_[a-f0-9-]+_/i, '').replace(/_attempt_\\d+$/i, '').replace(/_status_\\w+$/i, '').replace(/_key_selected$/i, '').replace(/_then_.*$/i, '').replace(/^complexity_rr_/i, '').replace(/_no_.*$/i, '');\n    // If we cleaned it down to something reasonable, format it\n    if (cleaned && cleaned.length > 2 && cleaned.length < 30 && isLikelyRoleName(cleaned)) {\n        return formatRoleName(cleaned);\n    }\n    // Otherwise, just clean up the original string minimally\n    return str.replace(/_/g, ' ').replace(/([a-z])([A-Z])/g, '$1 $2').split(' ').map((word)=>word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()).join(' ').substring(0, 30) + (str.length > 30 ? '...' : '');\n};\n/**\n * Convert snake_case role names to Title Case\n */ const formatRoleName = (roleName)=>{\n    return roleName.split('_').map((word)=>word.charAt(0).toUpperCase() + word.slice(1)).join(' ');\n};\n/**\n * Get CSS classes for role used badges based on type\n */ const getRoleUsedBadgeClass = (type)=>{\n    switch(type){\n        case 'role':\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-blue-500/15 text-blue-300 border border-blue-500/25';\n        case 'success':\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-green-500/15 text-green-300 border border-green-500/25';\n        case 'error':\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-red-500/15 text-red-300 border border-red-500/25';\n        case 'fallback':\n        default:\n            return 'inline-block px-2 py-1 rounded text-xs font-medium bg-orange-500/15 text-orange-300 border border-orange-500/25';\n    }\n};\n/**\n * Generate production-ready role_used strings for logging\n */ const generateRoleUsedMessage = {\n    // Default routing messages\n    defaultKeySuccess: (attempt = 1)=>attempt === 1 ? 'default_key_success' : `default_key_success_attempt_${attempt}`,\n    defaultKeyFailed: (attempt = 1, status)=>`default_key_failed${status ? `_status_${status}` : ''}_attempt_${attempt}`,\n    allKeysFailed: (attemptCount)=>`default_all_${attemptCount}_attempts_failed`,\n    // Role-based routing messages\n    roleRouting: (roleName)=>roleName,\n    intelligentRoleRouting: (detectedRole)=>`intelligent_role_${detectedRole}`,\n    // Complexity-based routing messages\n    complexityRouting: (level, keyIndex)=>keyIndex !== undefined ? `complexity_level_${level}_key_${keyIndex}` : `complexity_level_${level}`,\n    // Strict fallback routing messages\n    fallbackRouting: (position)=>`fallback_position_${position}`,\n    // Error states\n    noKeysAvailable: ()=>'no_keys_available',\n    configurationError: ()=>'configuration_error',\n    routingStrategyError: (strategy)=>`routing_strategy_error_${strategy}`\n};\n/**\n * Transform provider names to user-friendly display names\n */ const formatProviderName = (provider)=>{\n    if (!provider) return 'N/A';\n    const providerMap = {\n        'openai': 'OpenAI',\n        'anthropic': 'Anthropic',\n        'google': 'Google',\n        'openrouter': 'OpenRouter',\n        'deepseek': 'DeepSeek',\n        'xai': 'xAI',\n        'langgraph': 'RouKey Orchestration',\n        'langgraph_orchestration': 'RouKey Orchestration',\n        'hybrid_orchestration': 'RouKey Orchestration',\n        'roukey': 'RouKey Orchestration'\n    };\n    return providerMap[provider.toLowerCase()] || provider;\n};\n/**\n * Transform model names to user-friendly display names\n */ const formatModelName = (modelName)=>{\n    if (!modelName) return 'N/A';\n    // Remove common prefixes and make more readable\n    return modelName.replace(/^(gpt-|claude-|gemini-|meta-llama\\/|deepseek-|grok-)/, '').replace(/-/g, ' ').replace(/\\b\\w/g, (l)=>l.toUpperCase());\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvbG9nRm9ybWF0dGluZy50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBQTs7Q0FFQyxHQVFEOztDQUVDLEdBQ0QsTUFBTUEsbUJBQW1CLENBQUNDO0lBQ3hCLHFDQUFxQztJQUNyQyxNQUFNQyxvQkFBb0I7UUFDeEI7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFRCw0REFBNEQ7SUFDNUQsSUFBSUEsa0JBQWtCQyxJQUFJLENBQUNDLENBQUFBLFVBQVdBLFFBQVFDLElBQUksQ0FBQ0osT0FBTztRQUN4RCxPQUFPO0lBQ1Q7SUFFQSxxRkFBcUY7SUFDckYsT0FBTyxhQUFhSSxJQUFJLENBQUNKLFFBQVFBLElBQUlLLE1BQU0sR0FBRyxLQUFLTCxJQUFJSyxNQUFNLEdBQUc7QUFDbEU7QUFFQTs7Q0FFQyxHQUNNLE1BQU1DLG9CQUFvQixDQUFDQztJQUNoQyxJQUFJLENBQUNBLFVBQVUsT0FBTztRQUFFQyxNQUFNO1FBQU9DLE1BQU07SUFBVztJQUV0RCwrRUFBK0U7SUFDL0UsSUFBSVYsaUJBQWlCUSxXQUFXO1FBQzlCLE9BQU87WUFDTEMsTUFBTUUsZUFBZUg7WUFDckJFLE1BQU07WUFDTkUsU0FBUyxDQUFDLG9CQUFvQixFQUFFRCxlQUFlSCxXQUFXO1FBQzVEO0lBQ0Y7SUFFQSxzQ0FBc0M7SUFDdEMsSUFBSUEsU0FBU0ssUUFBUSxDQUFDLGtCQUFrQkwsU0FBU0ssUUFBUSxDQUFDLFlBQVk7UUFDcEUsTUFBTUMsZUFBZU4sU0FBU08sS0FBSyxDQUFDO1FBQ3BDLE1BQU1DLFVBQVVGLGVBQWVHLFNBQVNILFlBQVksQ0FBQyxFQUFFLElBQUk7UUFDM0QsT0FBTztZQUNMTCxNQUFNTyxZQUFZLElBQUksZ0JBQWdCLENBQUMscUJBQXFCLEVBQUVBLFFBQVEsQ0FBQyxDQUFDO1lBQ3hFTixNQUFNO1lBQ05FLFNBQVNJLFVBQVUsSUFBSSxDQUFDLFNBQVMsRUFBRUEsUUFBUSxvQkFBb0IsQ0FBQyxHQUFHRTtRQUNyRTtJQUNGO0lBRUEsc0NBQXNDO0lBQ3RDLElBQUlWLFNBQVNLLFFBQVEsQ0FBQyxrQkFBa0JMLFNBQVNLLFFBQVEsQ0FBQyxXQUFXO1FBQ25FLE1BQU1DLGVBQWVOLFNBQVNPLEtBQUssQ0FBQztRQUNwQyxNQUFNSSxjQUFjWCxTQUFTTyxLQUFLLENBQUM7UUFDbkMsTUFBTUMsVUFBVUYsZUFBZUcsU0FBU0gsWUFBWSxDQUFDLEVBQUUsSUFBSTtRQUMzRCxNQUFNTSxTQUFTRCxjQUFjQSxXQUFXLENBQUMsRUFBRSxHQUFHO1FBQzlDLE9BQU87WUFDTFYsTUFBTSxDQUFDLGdCQUFnQixFQUFFTyxRQUFRLENBQUMsQ0FBQztZQUNuQ04sTUFBTTtZQUNORSxTQUFTLENBQUMsb0JBQW9CLEVBQUVRLFFBQVE7UUFDMUM7SUFDRjtJQUVBLGtDQUFrQztJQUNsQyxJQUFJWixTQUFTSyxRQUFRLENBQUMsa0JBQWtCTCxTQUFTSyxRQUFRLENBQUMsb0JBQW9CO1FBQzVFLE1BQU1RLGFBQWFiLFNBQVNPLEtBQUssQ0FBQztRQUNsQyxNQUFNTyxRQUFRRCxhQUFhSixTQUFTSSxVQUFVLENBQUMsRUFBRSxJQUFJO1FBQ3JELE9BQU87WUFDTFosTUFBTSxDQUFDLGlCQUFpQixFQUFFYSxNQUFNLFVBQVUsQ0FBQztZQUMzQ1osTUFBTTtZQUNORSxTQUFTLENBQUMsTUFBTSxFQUFFVSxNQUFNLCtCQUErQixDQUFDO1FBQzFEO0lBQ0Y7SUFFQSx3RUFBd0U7SUFDeEUsSUFBSWQsU0FBU0ssUUFBUSxDQUFDLDBCQUEwQkwsU0FBU0ssUUFBUSxDQUFDLHNCQUFzQjtRQUN0RixpRUFBaUU7UUFDakUsTUFBTVUsZ0JBQWdCZixTQUFTTyxLQUFLLENBQUM7UUFDckMsSUFBSVEsZUFBZTtZQUNqQixNQUFNQyxrQkFBa0JELGFBQWEsQ0FBQyxFQUFFO1lBQ3hDLE1BQU1FLFlBQVlGLGFBQWEsQ0FBQyxFQUFFO1lBQ2xDLElBQUlDLG9CQUFvQkMsV0FBVztnQkFDakMsT0FBTztvQkFDTGhCLE1BQU0sQ0FBQyxpQkFBaUIsRUFBRWdCLFdBQVc7b0JBQ3JDZixNQUFNO29CQUNORSxTQUFTLENBQUMsMENBQTBDLEVBQUVhLFdBQVc7Z0JBQ25FO1lBQ0YsT0FBTztnQkFDTCxPQUFPO29CQUNMaEIsTUFBTSxDQUFDLFdBQVcsRUFBRWUsZ0JBQWdCLENBQUMsRUFBRUMsV0FBVztvQkFDbERmLE1BQU07b0JBQ05FLFNBQVMsQ0FBQyxvQkFBb0IsRUFBRVksZ0JBQWdCLDRCQUE0QixFQUFFQyxXQUFXO2dCQUMzRjtZQUNGO1FBQ0Y7UUFFQSxxQ0FBcUM7UUFDckMsTUFBTUMsYUFBYWxCLFNBQVNPLEtBQUssQ0FBQztRQUNsQyxJQUFJVyxZQUFZO1lBQ2QsTUFBTUMsUUFBUUQsVUFBVSxDQUFDLEVBQUU7WUFDM0IsT0FBTztnQkFDTGpCLE1BQU0sQ0FBQyxpQkFBaUIsRUFBRWtCLE9BQU87Z0JBQ2pDakIsTUFBTTtnQkFDTkUsU0FBUyxDQUFDLDBDQUEwQyxDQUFDO1lBQ3ZEO1FBQ0Y7SUFDRjtJQUVBLHlCQUF5QjtJQUN6QixJQUFJSixTQUFTSyxRQUFRLENBQUMsdUJBQXVCO1FBQzNDLE1BQU1lLFdBQVdwQixTQUFTTyxLQUFLLENBQUM7UUFDaEMsTUFBTWMsV0FBV0QsV0FBV1gsU0FBU1csUUFBUSxDQUFDLEVBQUUsSUFBSTtRQUNwRCxPQUFPO1lBQ0xuQixNQUFNLENBQUMsY0FBYyxFQUFFb0IsV0FBVyxHQUFHO1lBQ3JDbkIsTUFBTTtZQUNORSxTQUFTLENBQUMsOEJBQThCLEVBQUVpQixXQUFXLEdBQUc7UUFDMUQ7SUFDRjtJQUVBLGtDQUFrQztJQUNsQyxJQUFJckIsU0FBU0ssUUFBUSxDQUFDLHNCQUFzQjtRQUMxQyxNQUFNaUIsWUFBWXRCLFNBQVNPLEtBQUssQ0FBQztRQUNqQyxNQUFNZ0IsZUFBZUQsWUFBWUEsU0FBUyxDQUFDLEVBQUUsR0FBRztRQUNoRCxPQUFPO1lBQ0xyQixNQUFNLENBQUMsT0FBTyxFQUFFRSxlQUFlb0IsZUFBZTtZQUM5Q3JCLE1BQU07WUFDTkUsU0FBUyxDQUFDLGtCQUFrQixFQUFFRCxlQUFlb0IsZUFBZTtRQUM5RDtJQUNGO0lBRUEsa0ZBQWtGO0lBQ2xGLE9BQU9DLHNCQUFzQnhCO0FBQy9CLEVBQUU7QUFFRjs7Q0FFQyxHQUNELE1BQU13Qix3QkFBd0IsQ0FBQ3hCO0lBQzdCLHdDQUF3QztJQUN4QyxNQUFNeUIsa0JBQWtCekIsU0FBU08sS0FBSyxDQUFDO0lBQ3ZDLElBQUlrQixpQkFBaUI7UUFDbkIsTUFBTU4sUUFBUU0sZUFBZSxDQUFDLEVBQUU7UUFDaEMsT0FBTztZQUNMeEIsTUFBTSxDQUFDLGlCQUFpQixFQUFFa0IsT0FBTztZQUNqQ2pCLE1BQU07WUFDTkUsU0FBUyxDQUFDLDJCQUEyQixFQUFFZSxNQUFNLHFCQUFxQixDQUFDO1FBQ3JFO0lBQ0Y7SUFFQSxrREFBa0Q7SUFDbEQsTUFBTU8sZ0JBQWdCQyx1QkFBdUIzQjtJQUM3QyxJQUFJMEIsZUFBZTtRQUNqQixPQUFPO1lBQ0x6QixNQUFNRSxlQUFldUI7WUFDckJ4QixNQUFNO1lBQ05FLFNBQVMsQ0FBQyxnQkFBZ0IsRUFBRUQsZUFBZXVCLGdCQUFnQjtRQUM3RDtJQUNGO0lBRUEsc0NBQXNDO0lBQ3RDLE1BQU1FLGdCQUFnQjVCLFNBQVNPLEtBQUssQ0FBQztJQUNyQyxJQUFJcUIsZUFBZTtRQUNqQixNQUFNUCxXQUFXWixTQUFTbUIsYUFBYSxDQUFDLEVBQUU7UUFDMUMsT0FBTztZQUNMM0IsTUFBTSxDQUFDLGNBQWMsRUFBRW9CLFdBQVcsR0FBRztZQUNyQ25CLE1BQU07WUFDTkUsU0FBUyxDQUFDLDRCQUE0QixFQUFFaUIsV0FBVyxHQUFHO1FBQ3hEO0lBQ0Y7SUFFQSxxQ0FBcUM7SUFDckMsTUFBTWYsZUFBZU4sU0FBU08sS0FBSyxDQUFDO0lBQ3BDLElBQUlELGNBQWM7UUFDaEIsTUFBTUUsVUFBVUMsU0FBU0gsWUFBWSxDQUFDLEVBQUU7UUFDeEMsTUFBTXVCLFlBQVk3QixTQUFTOEIsV0FBVyxHQUFHekIsUUFBUSxDQUFDO1FBQ2xELE1BQU0wQixXQUFXL0IsU0FBUzhCLFdBQVcsR0FBR3pCLFFBQVEsQ0FBQztRQUVqRCxJQUFJd0IsV0FBVztZQUNiLE9BQU87Z0JBQ0w1QixNQUFNTyxZQUFZLElBQUksZ0JBQWdCLENBQUMscUJBQXFCLEVBQUVBLFFBQVEsQ0FBQyxDQUFDO2dCQUN4RU4sTUFBTTtnQkFDTkUsU0FBUyxDQUFDLDZCQUE2QixFQUFFSSxTQUFTO1lBQ3BEO1FBQ0YsT0FBTyxJQUFJdUIsVUFBVTtZQUNuQixPQUFPO2dCQUNMOUIsTUFBTSxDQUFDLGdCQUFnQixFQUFFTyxRQUFRLENBQUMsQ0FBQztnQkFDbkNOLE1BQU07Z0JBQ05FLFNBQVMsQ0FBQyw2QkFBNkIsRUFBRUksU0FBUztZQUNwRDtRQUNGO0lBQ0Y7SUFFQSwwREFBMEQ7SUFDMUQsTUFBTXdCLGNBQWNDLGlCQUFpQmpDO0lBQ3JDLE9BQU87UUFDTEMsTUFBTStCO1FBQ045QixNQUFNO1FBQ05FLFNBQVMsQ0FBQyxxQkFBcUIsRUFBRUosVUFBVTtJQUM3QztBQUNGO0FBRUE7O0NBRUMsR0FDRCxNQUFNMkIseUJBQXlCLENBQUNsQztJQUM5Qiw0REFBNEQ7SUFDNUQsTUFBTXlDLGtCQUFrQnpDLElBQUljLEtBQUssQ0FBQztJQUNsQyxJQUFJMkIsbUJBQW1CMUMsaUJBQWlCMEMsZUFBZSxDQUFDLEVBQUUsR0FBRztRQUMzRCxPQUFPQSxlQUFlLENBQUMsRUFBRTtJQUMzQjtJQUVBLG1EQUFtRDtJQUNuRCxNQUFNWixZQUFZN0IsSUFBSWMsS0FBSyxDQUFDO0lBQzVCLElBQUllLGFBQWE5QixpQkFBaUI4QixTQUFTLENBQUMsRUFBRSxHQUFHO1FBQy9DLE9BQU9BLFNBQVMsQ0FBQyxFQUFFO0lBQ3JCO0lBRUEsNENBQTRDO0lBQzVDLE1BQU1hLGNBQWMxQyxJQUFJYyxLQUFLLENBQUM7SUFDOUIsSUFBSTRCLGVBQWUzQyxpQkFBaUIyQyxXQUFXLENBQUMsRUFBRSxHQUFHO1FBQ25ELE9BQU9BLFdBQVcsQ0FBQyxFQUFFO0lBQ3ZCO0lBRUEsT0FBTztBQUNUO0FBRUE7O0NBRUMsR0FDRCxNQUFNRixtQkFBbUIsQ0FBQ3hDO0lBQ3hCLDRDQUE0QztJQUM1QyxNQUFNMkMsVUFBVTNDLElBQ2I0QyxPQUFPLENBQUMsNkJBQTZCLElBQ3JDQSxPQUFPLENBQUMsa0JBQWtCLElBQzFCQSxPQUFPLENBQUMsaUJBQWlCLElBQ3pCQSxPQUFPLENBQUMsbUJBQW1CLElBQzNCQSxPQUFPLENBQUMsY0FBYyxJQUN0QkEsT0FBTyxDQUFDLG9CQUFvQixJQUM1QkEsT0FBTyxDQUFDLFlBQVk7SUFFdkIsMkRBQTJEO0lBQzNELElBQUlELFdBQVdBLFFBQVF0QyxNQUFNLEdBQUcsS0FBS3NDLFFBQVF0QyxNQUFNLEdBQUcsTUFBTU4saUJBQWlCNEMsVUFBVTtRQUNyRixPQUFPakMsZUFBZWlDO0lBQ3hCO0lBRUEseURBQXlEO0lBQ3pELE9BQU8zQyxJQUNKNEMsT0FBTyxDQUFDLE1BQU0sS0FDZEEsT0FBTyxDQUFDLG1CQUFtQixTQUMzQkMsS0FBSyxDQUFDLEtBQ05DLEdBQUcsQ0FBQ0MsQ0FBQUEsT0FBUUEsS0FBS0MsTUFBTSxDQUFDLEdBQUdDLFdBQVcsS0FBS0YsS0FBS0csS0FBSyxDQUFDLEdBQUdiLFdBQVcsSUFDcEVjLElBQUksQ0FBQyxLQUNMQyxTQUFTLENBQUMsR0FBRyxNQUFPcEQsQ0FBQUEsSUFBSUssTUFBTSxHQUFHLEtBQUssUUFBUSxFQUFDO0FBQ3BEO0FBRUE7O0NBRUMsR0FDTSxNQUFNSyxpQkFBaUIsQ0FBQzJDO0lBQzdCLE9BQU9BLFNBQ0pSLEtBQUssQ0FBQyxLQUNOQyxHQUFHLENBQUNDLENBQUFBLE9BQVFBLEtBQUtDLE1BQU0sQ0FBQyxHQUFHQyxXQUFXLEtBQUtGLEtBQUtHLEtBQUssQ0FBQyxJQUN0REMsSUFBSSxDQUFDO0FBQ1YsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTUcsd0JBQXdCLENBQUM3QztJQUNwQyxPQUFRQTtRQUNOLEtBQUs7WUFDSCxPQUFPO1FBQ1QsS0FBSztZQUNILE9BQU87UUFDVCxLQUFLO1lBQ0gsT0FBTztRQUNULEtBQUs7UUFDTDtZQUNFLE9BQU87SUFDWDtBQUNGLEVBQUU7QUFFRjs7Q0FFQyxHQUNNLE1BQU04QywwQkFBMEI7SUFDckMsMkJBQTJCO0lBQzNCQyxtQkFBbUIsQ0FBQ3pDLFVBQWtCLENBQUMsR0FDckNBLFlBQVksSUFBSSx3QkFBd0IsQ0FBQyw0QkFBNEIsRUFBRUEsU0FBUztJQUVsRjBDLGtCQUFrQixDQUFDMUMsVUFBa0IsQ0FBQyxFQUFFSSxTQUN0QyxDQUFDLGtCQUFrQixFQUFFQSxTQUFTLENBQUMsUUFBUSxFQUFFQSxRQUFRLEdBQUcsR0FBRyxTQUFTLEVBQUVKLFNBQVM7SUFFN0UyQyxlQUFlLENBQUNDLGVBQ2QsQ0FBQyxZQUFZLEVBQUVBLGFBQWEsZ0JBQWdCLENBQUM7SUFFL0MsOEJBQThCO0lBQzlCQyxhQUFhLENBQUNQLFdBQXFCQTtJQUVuQ1Esd0JBQXdCLENBQUMvQixlQUN2QixDQUFDLGlCQUFpQixFQUFFQSxjQUFjO0lBRXBDLG9DQUFvQztJQUNwQ2dDLG1CQUFtQixDQUFDcEMsT0FBZXFDLFdBQ2pDQSxhQUFhOUMsWUFDVCxDQUFDLGlCQUFpQixFQUFFUyxNQUFNLEtBQUssRUFBRXFDLFVBQVUsR0FDM0MsQ0FBQyxpQkFBaUIsRUFBRXJDLE9BQU87SUFFakMsbUNBQW1DO0lBQ25Dc0MsaUJBQWlCLENBQUNwQyxXQUNoQixDQUFDLGtCQUFrQixFQUFFQSxVQUFVO0lBRWpDLGVBQWU7SUFDZnFDLGlCQUFpQixJQUFNO0lBQ3ZCQyxvQkFBb0IsSUFBTTtJQUMxQkMsc0JBQXNCLENBQUNDLFdBQXFCLENBQUMsdUJBQXVCLEVBQUVBLFVBQVU7QUFDbEYsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTUMscUJBQXFCLENBQUNDO0lBQ2pDLElBQUksQ0FBQ0EsVUFBVSxPQUFPO0lBRXRCLE1BQU1DLGNBQXNDO1FBQzFDLFVBQVU7UUFDVixhQUFhO1FBQ2IsVUFBVTtRQUNWLGNBQWM7UUFDZCxZQUFZO1FBQ1osT0FBTztRQUNQLGFBQWE7UUFDYiwyQkFBMkI7UUFDM0Isd0JBQXdCO1FBQ3hCLFVBQVU7SUFDWjtJQUVBLE9BQU9BLFdBQVcsQ0FBQ0QsU0FBU2pDLFdBQVcsR0FBRyxJQUFJaUM7QUFDaEQsRUFBRTtBQUVGOztDQUVDLEdBQ00sTUFBTUUsa0JBQWtCLENBQUNDO0lBQzlCLElBQUksQ0FBQ0EsV0FBVyxPQUFPO0lBRXZCLGdEQUFnRDtJQUNoRCxPQUFPQSxVQUNKN0IsT0FBTyxDQUFDLHdEQUF3RCxJQUNoRUEsT0FBTyxDQUFDLE1BQU0sS0FDZEEsT0FBTyxDQUFDLFNBQVM4QixDQUFBQSxJQUFLQSxFQUFFekIsV0FBVztBQUN4QyxFQUFFIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXHNyY1xcdXRpbHNcXGxvZ0Zvcm1hdHRpbmcudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBVdGlsaXR5IGZ1bmN0aW9ucyBmb3IgdHJhbnNmb3JtaW5nIGRlYnVnLXN0eWxlIGxvZyBtZXNzYWdlcyBpbnRvIHByb2R1Y3Rpb24tcmVhZHksIHVzZXItZnJpZW5kbHkgdGV4dFxuICovXG5cbmV4cG9ydCBpbnRlcmZhY2UgUm9sZVVzZWRJbmZvIHtcbiAgdGV4dDogc3RyaW5nO1xuICB0eXBlOiAnc3VjY2VzcycgfCAnZXJyb3InIHwgJ2ZhbGxiYWNrJyB8ICdyb2xlJztcbiAgZGV0YWlscz86IHN0cmluZztcbn1cblxuLyoqXG4gKiBDaGVjayBpZiBhIHN0cmluZyBsb29rcyBsaWtlIGEgcm9sZSBuYW1lICh2cyBhIHRlY2huaWNhbCBkZWJ1ZyBwYXR0ZXJuKVxuICovXG5jb25zdCBpc0xpa2VseVJvbGVOYW1lID0gKHN0cjogc3RyaW5nKTogYm9vbGVhbiA9PiB7XG4gIC8vIEV4Y2x1ZGUgb2J2aW91cyB0ZWNobmljYWwgcGF0dGVybnNcbiAgY29uc3QgdGVjaG5pY2FsUGF0dGVybnMgPSBbXG4gICAgL2RlZmF1bHRfa2V5L2ksXG4gICAgL2F0dGVtcHRfXFxkKy9pLFxuICAgIC9zdGF0dXNfXFxkKy9pLFxuICAgIC9mYWlsZWQvaSxcbiAgICAvc3VjY2Vzcy9pLFxuICAgIC9jb21wbGV4aXR5X3JyL2ksXG4gICAgL2ZhbGxiYWNrX3Bvc2l0aW9uL2ksXG4gICAgL15bYS1mMC05LV17OCx9L2ksIC8vIFVVSURzIG9yIGxvbmcgaGV4IHN0cmluZ3NcbiAgICAvX3RoZW5fL2ksXG4gICAgL2NsYXNzaWZpY2F0aW9uXy9pLFxuICAgIC9ub19wcm9tcHQvaSxcbiAgICAvZXJyb3IvaVxuICBdO1xuICBcbiAgLy8gSWYgaXQgbWF0Y2hlcyBhbnkgdGVjaG5pY2FsIHBhdHRlcm4sIGl0J3Mgbm90IGEgcm9sZSBuYW1lXG4gIGlmICh0ZWNobmljYWxQYXR0ZXJucy5zb21lKHBhdHRlcm4gPT4gcGF0dGVybi50ZXN0KHN0cikpKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIFxuICAvLyBJZiBpdCdzIGEgc2ltcGxlIHdvcmQgb3Igc25ha2VfY2FzZSB3aXRob3V0IG51bWJlcnMvdGVjaG5pY2FsIHRlcm1zLCBsaWtlbHkgYSByb2xlXG4gIHJldHVybiAvXlthLXpfXSskL2kudGVzdChzdHIpICYmIHN0ci5sZW5ndGggPiAyICYmIHN0ci5sZW5ndGggPCA1MDtcbn07XG5cbi8qKlxuICogVHJhbnNmb3JtIGRlYnVnLXN0eWxlIHJvbGVfdXNlZCBtZXNzYWdlcyBpbnRvIHVzZXItZnJpZW5kbHkgdGV4dFxuICovXG5leHBvcnQgY29uc3QgdHJhbnNmb3JtUm9sZVVzZWQgPSAocm9sZVVzZWQ6IHN0cmluZyB8IG51bGwpOiBSb2xlVXNlZEluZm8gPT4ge1xuICBpZiAoIXJvbGVVc2VkKSByZXR1cm4geyB0ZXh0OiAnTi9BJywgdHlwZTogJ2ZhbGxiYWNrJyB9O1xuICBcbiAgLy8gSGFuZGxlIHNpbXBsZSByb2xlIG5hbWVzIGZpcnN0IChjbGVhbiByb2xlIG5hbWVzIHdpdGhvdXQgdGVjaG5pY2FsIHBhdHRlcm5zKVxuICBpZiAoaXNMaWtlbHlSb2xlTmFtZShyb2xlVXNlZCkpIHtcbiAgICByZXR1cm4geyBcbiAgICAgIHRleHQ6IGZvcm1hdFJvbGVOYW1lKHJvbGVVc2VkKSwgXG4gICAgICB0eXBlOiAncm9sZScsXG4gICAgICBkZXRhaWxzOiBgUm9sZS1iYXNlZCByb3V0aW5nOiAke2Zvcm1hdFJvbGVOYW1lKHJvbGVVc2VkKX1gXG4gICAgfTtcbiAgfVxuICBcbiAgLy8gSGFuZGxlIGRlZmF1bHQga2V5IHN1Y2Nlc3MgcGF0dGVybnNcbiAgaWYgKHJvbGVVc2VkLmluY2x1ZGVzKCdkZWZhdWx0X2tleScpICYmIHJvbGVVc2VkLmluY2x1ZGVzKCdzdWNjZXNzJykpIHtcbiAgICBjb25zdCBhdHRlbXB0TWF0Y2ggPSByb2xlVXNlZC5tYXRjaCgvYXR0ZW1wdF8oXFxkKykvKTtcbiAgICBjb25zdCBhdHRlbXB0ID0gYXR0ZW1wdE1hdGNoID8gcGFyc2VJbnQoYXR0ZW1wdE1hdGNoWzFdKSA6IDE7XG4gICAgcmV0dXJuIHsgXG4gICAgICB0ZXh0OiBhdHRlbXB0ID09PSAxID8gJ0RlZmF1bHQgS2V5JyA6IGBEZWZhdWx0IEtleSAoQXR0ZW1wdCAke2F0dGVtcHR9KWAsIFxuICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgZGV0YWlsczogYXR0ZW1wdCA+IDEgPyBgUmVxdWlyZWQgJHthdHRlbXB0fSBhdHRlbXB0cyB0byBzdWNjZWVkYCA6IHVuZGVmaW5lZFxuICAgIH07XG4gIH1cbiAgXG4gIC8vIEhhbmRsZSBkZWZhdWx0IGtleSBmYWlsdXJlIHBhdHRlcm5zXG4gIGlmIChyb2xlVXNlZC5pbmNsdWRlcygnZGVmYXVsdF9rZXknKSAmJiByb2xlVXNlZC5pbmNsdWRlcygnZmFpbGVkJykpIHtcbiAgICBjb25zdCBhdHRlbXB0TWF0Y2ggPSByb2xlVXNlZC5tYXRjaCgvYXR0ZW1wdF8oXFxkKykvKTtcbiAgICBjb25zdCBzdGF0dXNNYXRjaCA9IHJvbGVVc2VkLm1hdGNoKC9zdGF0dXNfKFxcdyspLyk7XG4gICAgY29uc3QgYXR0ZW1wdCA9IGF0dGVtcHRNYXRjaCA/IHBhcnNlSW50KGF0dGVtcHRNYXRjaFsxXSkgOiAxO1xuICAgIGNvbnN0IHN0YXR1cyA9IHN0YXR1c01hdGNoID8gc3RhdHVzTWF0Y2hbMV0gOiAndW5rbm93bic7XG4gICAgcmV0dXJuIHsgXG4gICAgICB0ZXh0OiBgRmFpbGVkIChBdHRlbXB0ICR7YXR0ZW1wdH0pYCwgXG4gICAgICB0eXBlOiAnZXJyb3InLFxuICAgICAgZGV0YWlsczogYEZhaWxlZCB3aXRoIHN0YXR1czogJHtzdGF0dXN9YFxuICAgIH07XG4gIH1cbiAgXG4gIC8vIEhhbmRsZSBtdWx0aXBsZSBhdHRlbXB0cyBmYWlsZWRcbiAgaWYgKHJvbGVVc2VkLmluY2x1ZGVzKCdkZWZhdWx0X2FsbCcpICYmIHJvbGVVc2VkLmluY2x1ZGVzKCdhdHRlbXB0c19mYWlsZWQnKSkge1xuICAgIGNvbnN0IGNvdW50TWF0Y2ggPSByb2xlVXNlZC5tYXRjaCgvZGVmYXVsdF9hbGxfKFxcZCspX2F0dGVtcHRzLyk7XG4gICAgY29uc3QgY291bnQgPSBjb3VudE1hdGNoID8gcGFyc2VJbnQoY291bnRNYXRjaFsxXSkgOiAwO1xuICAgIHJldHVybiB7IFxuICAgICAgdGV4dDogYEFsbCBLZXlzIEZhaWxlZCAoJHtjb3VudH0gYXR0ZW1wdHMpYCwgXG4gICAgICB0eXBlOiAnZXJyb3InLFxuICAgICAgZGV0YWlsczogYFRyaWVkICR7Y291bnR9IGRpZmZlcmVudCBBUEkga2V5cywgYWxsIGZhaWxlZGBcbiAgICB9O1xuICB9XG4gIFxuICAvLyBIYW5kbGUgZW5oYW5jZWQgY29tcGxleGl0eS1iYXNlZCByb3V0aW5nIHdpdGggcHJveGltYWwgc2VhcmNoIGRldGFpbHNcbiAgaWYgKHJvbGVVc2VkLmluY2x1ZGVzKCdjb21wbGV4aXR5X3JyX2Nsc2ZfJykgfHwgcm9sZVVzZWQuaW5jbHVkZXMoJ2NvbXBsZXhpdHlfbGV2ZWxfJykpIHtcbiAgICAvLyBFbmhhbmNlZCBwYXR0ZXJuOiBjb21wbGV4aXR5X3JyX2Nsc2ZfM191c2VkX2x2bF80X2tleV9zZWxlY3RlZFxuICAgIGNvbnN0IGVuaGFuY2VkTWF0Y2ggPSByb2xlVXNlZC5tYXRjaCgvY29tcGxleGl0eV9ycl9jbHNmXyhcXGQrKV91c2VkX2x2bF8oXFxkKykvKTtcbiAgICBpZiAoZW5oYW5jZWRNYXRjaCkge1xuICAgICAgY29uc3QgY2xhc3NpZmllZExldmVsID0gZW5oYW5jZWRNYXRjaFsxXTtcbiAgICAgIGNvbnN0IHVzZWRMZXZlbCA9IGVuaGFuY2VkTWF0Y2hbMl07XG4gICAgICBpZiAoY2xhc3NpZmllZExldmVsID09PSB1c2VkTGV2ZWwpIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICB0ZXh0OiBgQ29tcGxleGl0eSBMZXZlbCAke3VzZWRMZXZlbH1gLFxuICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJyxcbiAgICAgICAgICBkZXRhaWxzOiBgQ2xhc3NpZmllZCBhbmQgcm91dGVkIHRvIGNvbXBsZXhpdHkgbGV2ZWwgJHt1c2VkTGV2ZWx9YFxuICAgICAgICB9O1xuICAgICAgfSBlbHNlIHtcbiAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICB0ZXh0OiBgQ29tcGxleGl0eSAke2NsYXNzaWZpZWRMZXZlbH3ihpIke3VzZWRMZXZlbH1gLFxuICAgICAgICAgIHR5cGU6ICdzdWNjZXNzJyxcbiAgICAgICAgICBkZXRhaWxzOiBgQ2xhc3NpZmllZCBhcyBsZXZlbCAke2NsYXNzaWZpZWRMZXZlbH0sIHJvdXRlZCB0byBhdmFpbGFibGUgbGV2ZWwgJHt1c2VkTGV2ZWx9YFxuICAgICAgICB9O1xuICAgICAgfVxuICAgIH1cbiAgICBcbiAgICAvLyBTaW1wbGUgcGF0dGVybjogY29tcGxleGl0eV9sZXZlbF8zXG4gICAgY29uc3QgbGV2ZWxNYXRjaCA9IHJvbGVVc2VkLm1hdGNoKC9jb21wbGV4aXR5X2xldmVsXyhcXGQrKS8pO1xuICAgIGlmIChsZXZlbE1hdGNoKSB7XG4gICAgICBjb25zdCBsZXZlbCA9IGxldmVsTWF0Y2hbMV07XG4gICAgICByZXR1cm4geyBcbiAgICAgICAgdGV4dDogYENvbXBsZXhpdHkgTGV2ZWwgJHtsZXZlbH1gLCBcbiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICBkZXRhaWxzOiBgUm91dGVkIGJhc2VkIG9uIHByb21wdCBjb21wbGV4aXR5IGFuYWx5c2lzYFxuICAgICAgfTtcbiAgICB9XG4gIH1cbiAgXG4gIC8vIEhhbmRsZSBzdHJpY3QgZmFsbGJhY2tcbiAgaWYgKHJvbGVVc2VkLmluY2x1ZGVzKCdmYWxsYmFja19wb3NpdGlvbl8nKSkge1xuICAgIGNvbnN0IHBvc01hdGNoID0gcm9sZVVzZWQubWF0Y2goL2ZhbGxiYWNrX3Bvc2l0aW9uXyhcXGQrKS8pO1xuICAgIGNvbnN0IHBvc2l0aW9uID0gcG9zTWF0Y2ggPyBwYXJzZUludChwb3NNYXRjaFsxXSkgOiAwO1xuICAgIHJldHVybiB7IFxuICAgICAgdGV4dDogYEZhbGxiYWNrIEtleSAjJHtwb3NpdGlvbiArIDF9YCwgXG4gICAgICB0eXBlOiAnc3VjY2VzcycsXG4gICAgICBkZXRhaWxzOiBgVXNlZCBmYWxsYmFjayBrZXkgYXQgcG9zaXRpb24gJHtwb3NpdGlvbiArIDF9YFxuICAgIH07XG4gIH1cbiAgXG4gIC8vIEhhbmRsZSBpbnRlbGxpZ2VudCByb2xlIHJvdXRpbmdcbiAgaWYgKHJvbGVVc2VkLmluY2x1ZGVzKCdpbnRlbGxpZ2VudF9yb2xlXycpKSB7XG4gICAgY29uc3Qgcm9sZU1hdGNoID0gcm9sZVVzZWQubWF0Y2goL2ludGVsbGlnZW50X3JvbGVfKC4rKSQvKTtcbiAgICBjb25zdCBkZXRlY3RlZFJvbGUgPSByb2xlTWF0Y2ggPyByb2xlTWF0Y2hbMV0gOiAndW5rbm93bic7XG4gICAgcmV0dXJuIHtcbiAgICAgIHRleHQ6IGBTbWFydDogJHtmb3JtYXRSb2xlTmFtZShkZXRlY3RlZFJvbGUpfWAsXG4gICAgICB0eXBlOiAncm9sZScsXG4gICAgICBkZXRhaWxzOiBgQUkgZGV0ZWN0ZWQgcm9sZTogJHtmb3JtYXRSb2xlTmFtZShkZXRlY3RlZFJvbGUpfWBcbiAgICB9O1xuICB9XG4gIFxuICAvLyBFbmhhbmNlZCBmYWxsYmFjazogRXh0cmFjdCBtZWFuaW5nZnVsIGluZm9ybWF0aW9uIGZyb20gYW55IHVucmVjb2duaXplZCBwYXR0ZXJuXG4gIHJldHVybiBleHRyYWN0TWVhbmluZ2Z1bEluZm8ocm9sZVVzZWQpO1xufTtcblxuLyoqXG4gKiBFeHRyYWN0IG1lYW5pbmdmdWwgaW5mb3JtYXRpb24gZnJvbSB1bnJlY29nbml6ZWQgcm9sZV91c2VkIHBhdHRlcm5zXG4gKi9cbmNvbnN0IGV4dHJhY3RNZWFuaW5nZnVsSW5mbyA9IChyb2xlVXNlZDogc3RyaW5nKTogUm9sZVVzZWRJbmZvID0+IHtcbiAgLy8gVHJ5IHRvIGV4dHJhY3QgY29tcGxleGl0eSBpbmZvcm1hdGlvblxuICBjb25zdCBjb21wbGV4aXR5TWF0Y2ggPSByb2xlVXNlZC5tYXRjaCgvY29tcGxleGl0eVtfXFxzXSooXFxkKykvaSk7XG4gIGlmIChjb21wbGV4aXR5TWF0Y2gpIHtcbiAgICBjb25zdCBsZXZlbCA9IGNvbXBsZXhpdHlNYXRjaFsxXTtcbiAgICByZXR1cm4ge1xuICAgICAgdGV4dDogYENvbXBsZXhpdHkgTGV2ZWwgJHtsZXZlbH1gLFxuICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgZGV0YWlsczogYEV4dHJhY3RlZCBjb21wbGV4aXR5IGxldmVsICR7bGV2ZWx9IGZyb20gcm91dGluZyBwYXR0ZXJuYFxuICAgIH07XG4gIH1cblxuICAvLyBUcnkgdG8gZXh0cmFjdCByb2xlIG5hbWVzIGZyb20gY29tcGxleCBwYXR0ZXJuc1xuICBjb25zdCByb2xlTmFtZU1hdGNoID0gZXh0cmFjdFJvbGVGcm9tUGF0dGVybihyb2xlVXNlZCk7XG4gIGlmIChyb2xlTmFtZU1hdGNoKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIHRleHQ6IGZvcm1hdFJvbGVOYW1lKHJvbGVOYW1lTWF0Y2gpLFxuICAgICAgdHlwZTogJ3JvbGUnLFxuICAgICAgZGV0YWlsczogYEV4dHJhY3RlZCByb2xlOiAke2Zvcm1hdFJvbGVOYW1lKHJvbGVOYW1lTWF0Y2gpfWBcbiAgICB9O1xuICB9XG5cbiAgLy8gVHJ5IHRvIGV4dHJhY3QgZmFsbGJhY2sgaW5mb3JtYXRpb25cbiAgY29uc3QgZmFsbGJhY2tNYXRjaCA9IHJvbGVVc2VkLm1hdGNoKC9mYWxsYmFja1tfXFxzXSooXFxkKykvaSk7XG4gIGlmIChmYWxsYmFja01hdGNoKSB7XG4gICAgY29uc3QgcG9zaXRpb24gPSBwYXJzZUludChmYWxsYmFja01hdGNoWzFdKTtcbiAgICByZXR1cm4ge1xuICAgICAgdGV4dDogYEZhbGxiYWNrIEtleSAjJHtwb3NpdGlvbiArIDF9YCxcbiAgICAgIHR5cGU6ICdzdWNjZXNzJyxcbiAgICAgIGRldGFpbHM6IGBFeHRyYWN0ZWQgZmFsbGJhY2sgcG9zaXRpb24gJHtwb3NpdGlvbiArIDF9YFxuICAgIH07XG4gIH1cblxuICAvLyBUcnkgdG8gZXh0cmFjdCBhdHRlbXB0IGluZm9ybWF0aW9uXG4gIGNvbnN0IGF0dGVtcHRNYXRjaCA9IHJvbGVVc2VkLm1hdGNoKC9hdHRlbXB0W19cXHNdKihcXGQrKS9pKTtcbiAgaWYgKGF0dGVtcHRNYXRjaCkge1xuICAgIGNvbnN0IGF0dGVtcHQgPSBwYXJzZUludChhdHRlbXB0TWF0Y2hbMV0pO1xuICAgIGNvbnN0IGlzU3VjY2VzcyA9IHJvbGVVc2VkLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ3N1Y2Nlc3MnKTtcbiAgICBjb25zdCBpc0ZhaWxlZCA9IHJvbGVVc2VkLnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoJ2ZhaWwnKTtcblxuICAgIGlmIChpc1N1Y2Nlc3MpIHtcbiAgICAgIHJldHVybiB7XG4gICAgICAgIHRleHQ6IGF0dGVtcHQgPT09IDEgPyAnRGVmYXVsdCBLZXknIDogYERlZmF1bHQgS2V5IChBdHRlbXB0ICR7YXR0ZW1wdH0pYCxcbiAgICAgICAgdHlwZTogJ3N1Y2Nlc3MnLFxuICAgICAgICBkZXRhaWxzOiBgRXh0cmFjdGVkIHN1Y2Nlc3Mgb24gYXR0ZW1wdCAke2F0dGVtcHR9YFxuICAgICAgfTtcbiAgICB9IGVsc2UgaWYgKGlzRmFpbGVkKSB7XG4gICAgICByZXR1cm4ge1xuICAgICAgICB0ZXh0OiBgRmFpbGVkIChBdHRlbXB0ICR7YXR0ZW1wdH0pYCxcbiAgICAgICAgdHlwZTogJ2Vycm9yJyxcbiAgICAgICAgZGV0YWlsczogYEV4dHJhY3RlZCBmYWlsdXJlIG9uIGF0dGVtcHQgJHthdHRlbXB0fWBcbiAgICAgIH07XG4gICAgfVxuICB9XG5cbiAgLy8gTGFzdCByZXNvcnQ6IHRyeSB0byBjbGVhbiB1cCB0aGUgcmF3IHN0cmluZyBmb3IgZGlzcGxheVxuICBjb25zdCBjbGVhbmVkVGV4dCA9IGNsZWFuUmF3Um9sZVVzZWQocm9sZVVzZWQpO1xuICByZXR1cm4ge1xuICAgIHRleHQ6IGNsZWFuZWRUZXh0LFxuICAgIHR5cGU6ICdmYWxsYmFjaycsXG4gICAgZGV0YWlsczogYFJhdyByb3V0aW5nIHBhdHRlcm46ICR7cm9sZVVzZWR9YFxuICB9O1xufTtcblxuLyoqXG4gKiBFeHRyYWN0IHJvbGUgbmFtZXMgZnJvbSBjb21wbGV4IHBhdHRlcm5zXG4gKi9cbmNvbnN0IGV4dHJhY3RSb2xlRnJvbVBhdHRlcm4gPSAoc3RyOiBzdHJpbmcpOiBzdHJpbmcgfCBudWxsID0+IHtcbiAgLy8gTG9vayBmb3IgcGF0dGVybnMgbGlrZSBcImNsYXNzaWZpZWRfYXNfUk9MRU5BTUVfc29tZXRoaW5nXCJcbiAgY29uc3QgY2xhc3NpZmllZE1hdGNoID0gc3RyLm1hdGNoKC9jbGFzc2lmaWVkX2FzXyhbYS16X10rKV8vaSk7XG4gIGlmIChjbGFzc2lmaWVkTWF0Y2ggJiYgaXNMaWtlbHlSb2xlTmFtZShjbGFzc2lmaWVkTWF0Y2hbMV0pKSB7XG4gICAgcmV0dXJuIGNsYXNzaWZpZWRNYXRjaFsxXTtcbiAgfVxuXG4gIC8vIExvb2sgZm9yIHBhdHRlcm5zIGxpa2UgXCJyb2xlX1JPTEVOQU1FX3NvbWV0aGluZ1wiXG4gIGNvbnN0IHJvbGVNYXRjaCA9IHN0ci5tYXRjaCgvcm9sZV8oW2Etel9dKylfL2kpO1xuICBpZiAocm9sZU1hdGNoICYmIGlzTGlrZWx5Um9sZU5hbWUocm9sZU1hdGNoWzFdKSkge1xuICAgIHJldHVybiByb2xlTWF0Y2hbMV07XG4gIH1cblxuICAvLyBMb29rIGZvciBwYXR0ZXJucyBsaWtlIFwiZmJfcm9sZV9ST0xFTkFNRVwiXG4gIGNvbnN0IGZiUm9sZU1hdGNoID0gc3RyLm1hdGNoKC9mYl9yb2xlXyhbYS16X10rKS9pKTtcbiAgaWYgKGZiUm9sZU1hdGNoICYmIGlzTGlrZWx5Um9sZU5hbWUoZmJSb2xlTWF0Y2hbMV0pKSB7XG4gICAgcmV0dXJuIGZiUm9sZU1hdGNoWzFdO1xuICB9XG5cbiAgcmV0dXJuIG51bGw7XG59O1xuXG4vKipcbiAqIENsZWFuIHVwIHJhdyByb2xlX3VzZWQgc3RyaW5ncyBmb3IgZGlzcGxheSBhcyBsYXN0IHJlc29ydFxuICovXG5jb25zdCBjbGVhblJhd1JvbGVVc2VkID0gKHN0cjogc3RyaW5nKTogc3RyaW5nID0+IHtcbiAgLy8gUmVtb3ZlIGNvbW1vbiB0ZWNobmljYWwgcHJlZml4ZXMvc3VmZml4ZXNcbiAgY29uc3QgY2xlYW5lZCA9IHN0clxuICAgIC5yZXBsYWNlKC9eZGVmYXVsdF9rZXlfW2EtZjAtOS1dK18vaSwgJycpXG4gICAgLnJlcGxhY2UoL19hdHRlbXB0X1xcZCskL2ksICcnKVxuICAgIC5yZXBsYWNlKC9fc3RhdHVzX1xcdyskL2ksICcnKVxuICAgIC5yZXBsYWNlKC9fa2V5X3NlbGVjdGVkJC9pLCAnJylcbiAgICAucmVwbGFjZSgvX3RoZW5fLiokL2ksICcnKVxuICAgIC5yZXBsYWNlKC9eY29tcGxleGl0eV9ycl8vaSwgJycpXG4gICAgLnJlcGxhY2UoL19ub18uKiQvaSwgJycpO1xuXG4gIC8vIElmIHdlIGNsZWFuZWQgaXQgZG93biB0byBzb21ldGhpbmcgcmVhc29uYWJsZSwgZm9ybWF0IGl0XG4gIGlmIChjbGVhbmVkICYmIGNsZWFuZWQubGVuZ3RoID4gMiAmJiBjbGVhbmVkLmxlbmd0aCA8IDMwICYmIGlzTGlrZWx5Um9sZU5hbWUoY2xlYW5lZCkpIHtcbiAgICByZXR1cm4gZm9ybWF0Um9sZU5hbWUoY2xlYW5lZCk7XG4gIH1cblxuICAvLyBPdGhlcndpc2UsIGp1c3QgY2xlYW4gdXAgdGhlIG9yaWdpbmFsIHN0cmluZyBtaW5pbWFsbHlcbiAgcmV0dXJuIHN0clxuICAgIC5yZXBsYWNlKC9fL2csICcgJylcbiAgICAucmVwbGFjZSgvKFthLXpdKShbQS1aXSkvZywgJyQxICQyJylcbiAgICAuc3BsaXQoJyAnKVxuICAgIC5tYXAod29yZCA9PiB3b3JkLmNoYXJBdCgwKS50b1VwcGVyQ2FzZSgpICsgd29yZC5zbGljZSgxKS50b0xvd2VyQ2FzZSgpKVxuICAgIC5qb2luKCcgJylcbiAgICAuc3Vic3RyaW5nKDAsIDMwKSArIChzdHIubGVuZ3RoID4gMzAgPyAnLi4uJyA6ICcnKTtcbn07XG5cbi8qKlxuICogQ29udmVydCBzbmFrZV9jYXNlIHJvbGUgbmFtZXMgdG8gVGl0bGUgQ2FzZVxuICovXG5leHBvcnQgY29uc3QgZm9ybWF0Um9sZU5hbWUgPSAocm9sZU5hbWU6IHN0cmluZyk6IHN0cmluZyA9PiB7XG4gIHJldHVybiByb2xlTmFtZVxuICAgIC5zcGxpdCgnXycpXG4gICAgLm1hcCh3b3JkID0+IHdvcmQuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyB3b3JkLnNsaWNlKDEpKVxuICAgIC5qb2luKCcgJyk7XG59O1xuXG4vKipcbiAqIEdldCBDU1MgY2xhc3NlcyBmb3Igcm9sZSB1c2VkIGJhZGdlcyBiYXNlZCBvbiB0eXBlXG4gKi9cbmV4cG9ydCBjb25zdCBnZXRSb2xlVXNlZEJhZGdlQ2xhc3MgPSAodHlwZTogJ3N1Y2Nlc3MnIHwgJ2Vycm9yJyB8ICdmYWxsYmFjaycgfCAncm9sZScpOiBzdHJpbmcgPT4ge1xuICBzd2l0Y2ggKHR5cGUpIHtcbiAgICBjYXNlICdyb2xlJzpcbiAgICAgIHJldHVybiAnaW5saW5lLWJsb2NrIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gYmctYmx1ZS01MDAvMTUgdGV4dC1ibHVlLTMwMCBib3JkZXIgYm9yZGVyLWJsdWUtNTAwLzI1JztcbiAgICBjYXNlICdzdWNjZXNzJzpcbiAgICAgIHJldHVybiAnaW5saW5lLWJsb2NrIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHMgZm9udC1tZWRpdW0gYmctZ3JlZW4tNTAwLzE1IHRleHQtZ3JlZW4tMzAwIGJvcmRlciBib3JkZXItZ3JlZW4tNTAwLzI1JztcbiAgICBjYXNlICdlcnJvcic6XG4gICAgICByZXR1cm4gJ2lubGluZS1ibG9jayBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLXJlZC01MDAvMTUgdGV4dC1yZWQtMzAwIGJvcmRlciBib3JkZXItcmVkLTUwMC8yNSc7XG4gICAgY2FzZSAnZmFsbGJhY2snOlxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gJ2lubGluZS1ibG9jayBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXhzIGZvbnQtbWVkaXVtIGJnLW9yYW5nZS01MDAvMTUgdGV4dC1vcmFuZ2UtMzAwIGJvcmRlciBib3JkZXItb3JhbmdlLTUwMC8yNSc7XG4gIH1cbn07XG5cbi8qKlxuICogR2VuZXJhdGUgcHJvZHVjdGlvbi1yZWFkeSByb2xlX3VzZWQgc3RyaW5ncyBmb3IgbG9nZ2luZ1xuICovXG5leHBvcnQgY29uc3QgZ2VuZXJhdGVSb2xlVXNlZE1lc3NhZ2UgPSB7XG4gIC8vIERlZmF1bHQgcm91dGluZyBtZXNzYWdlc1xuICBkZWZhdWx0S2V5U3VjY2VzczogKGF0dGVtcHQ6IG51bWJlciA9IDEpID0+XG4gICAgYXR0ZW1wdCA9PT0gMSA/ICdkZWZhdWx0X2tleV9zdWNjZXNzJyA6IGBkZWZhdWx0X2tleV9zdWNjZXNzX2F0dGVtcHRfJHthdHRlbXB0fWAsXG5cbiAgZGVmYXVsdEtleUZhaWxlZDogKGF0dGVtcHQ6IG51bWJlciA9IDEsIHN0YXR1cz86IHN0cmluZyB8IG51bWJlcikgPT5cbiAgICBgZGVmYXVsdF9rZXlfZmFpbGVkJHtzdGF0dXMgPyBgX3N0YXR1c18ke3N0YXR1c31gIDogJyd9X2F0dGVtcHRfJHthdHRlbXB0fWAsXG5cbiAgYWxsS2V5c0ZhaWxlZDogKGF0dGVtcHRDb3VudDogbnVtYmVyKSA9PlxuICAgIGBkZWZhdWx0X2FsbF8ke2F0dGVtcHRDb3VudH1fYXR0ZW1wdHNfZmFpbGVkYCxcblxuICAvLyBSb2xlLWJhc2VkIHJvdXRpbmcgbWVzc2FnZXNcbiAgcm9sZVJvdXRpbmc6IChyb2xlTmFtZTogc3RyaW5nKSA9PiByb2xlTmFtZSxcblxuICBpbnRlbGxpZ2VudFJvbGVSb3V0aW5nOiAoZGV0ZWN0ZWRSb2xlOiBzdHJpbmcpID0+XG4gICAgYGludGVsbGlnZW50X3JvbGVfJHtkZXRlY3RlZFJvbGV9YCxcblxuICAvLyBDb21wbGV4aXR5LWJhc2VkIHJvdXRpbmcgbWVzc2FnZXNcbiAgY29tcGxleGl0eVJvdXRpbmc6IChsZXZlbDogbnVtYmVyLCBrZXlJbmRleD86IG51bWJlcikgPT5cbiAgICBrZXlJbmRleCAhPT0gdW5kZWZpbmVkXG4gICAgICA/IGBjb21wbGV4aXR5X2xldmVsXyR7bGV2ZWx9X2tleV8ke2tleUluZGV4fWBcbiAgICAgIDogYGNvbXBsZXhpdHlfbGV2ZWxfJHtsZXZlbH1gLFxuXG4gIC8vIFN0cmljdCBmYWxsYmFjayByb3V0aW5nIG1lc3NhZ2VzXG4gIGZhbGxiYWNrUm91dGluZzogKHBvc2l0aW9uOiBudW1iZXIpID0+XG4gICAgYGZhbGxiYWNrX3Bvc2l0aW9uXyR7cG9zaXRpb259YCxcblxuICAvLyBFcnJvciBzdGF0ZXNcbiAgbm9LZXlzQXZhaWxhYmxlOiAoKSA9PiAnbm9fa2V5c19hdmFpbGFibGUnLFxuICBjb25maWd1cmF0aW9uRXJyb3I6ICgpID0+ICdjb25maWd1cmF0aW9uX2Vycm9yJyxcbiAgcm91dGluZ1N0cmF0ZWd5RXJyb3I6IChzdHJhdGVneTogc3RyaW5nKSA9PiBgcm91dGluZ19zdHJhdGVneV9lcnJvcl8ke3N0cmF0ZWd5fWAsXG59O1xuXG4vKipcbiAqIFRyYW5zZm9ybSBwcm92aWRlciBuYW1lcyB0byB1c2VyLWZyaWVuZGx5IGRpc3BsYXkgbmFtZXNcbiAqL1xuZXhwb3J0IGNvbnN0IGZvcm1hdFByb3ZpZGVyTmFtZSA9IChwcm92aWRlcjogc3RyaW5nIHwgbnVsbCk6IHN0cmluZyA9PiB7XG4gIGlmICghcHJvdmlkZXIpIHJldHVybiAnTi9BJztcblxuICBjb25zdCBwcm92aWRlck1hcDogUmVjb3JkPHN0cmluZywgc3RyaW5nPiA9IHtcbiAgICAnb3BlbmFpJzogJ09wZW5BSScsXG4gICAgJ2FudGhyb3BpYyc6ICdBbnRocm9waWMnLFxuICAgICdnb29nbGUnOiAnR29vZ2xlJyxcbiAgICAnb3BlbnJvdXRlcic6ICdPcGVuUm91dGVyJyxcbiAgICAnZGVlcHNlZWsnOiAnRGVlcFNlZWsnLFxuICAgICd4YWknOiAneEFJJyxcbiAgICAnbGFuZ2dyYXBoJzogJ1JvdUtleSBPcmNoZXN0cmF0aW9uJyxcbiAgICAnbGFuZ2dyYXBoX29yY2hlc3RyYXRpb24nOiAnUm91S2V5IE9yY2hlc3RyYXRpb24nLFxuICAgICdoeWJyaWRfb3JjaGVzdHJhdGlvbic6ICdSb3VLZXkgT3JjaGVzdHJhdGlvbicsXG4gICAgJ3JvdWtleSc6ICdSb3VLZXkgT3JjaGVzdHJhdGlvbicsXG4gIH07XG5cbiAgcmV0dXJuIHByb3ZpZGVyTWFwW3Byb3ZpZGVyLnRvTG93ZXJDYXNlKCldIHx8IHByb3ZpZGVyO1xufTtcblxuLyoqXG4gKiBUcmFuc2Zvcm0gbW9kZWwgbmFtZXMgdG8gdXNlci1mcmllbmRseSBkaXNwbGF5IG5hbWVzXG4gKi9cbmV4cG9ydCBjb25zdCBmb3JtYXRNb2RlbE5hbWUgPSAobW9kZWxOYW1lOiBzdHJpbmcgfCBudWxsKTogc3RyaW5nID0+IHtcbiAgaWYgKCFtb2RlbE5hbWUpIHJldHVybiAnTi9BJztcblxuICAvLyBSZW1vdmUgY29tbW9uIHByZWZpeGVzIGFuZCBtYWtlIG1vcmUgcmVhZGFibGVcbiAgcmV0dXJuIG1vZGVsTmFtZVxuICAgIC5yZXBsYWNlKC9eKGdwdC18Y2xhdWRlLXxnZW1pbmktfG1ldGEtbGxhbWFcXC98ZGVlcHNlZWstfGdyb2stKS8sICcnKVxuICAgIC5yZXBsYWNlKC8tL2csICcgJylcbiAgICAucmVwbGFjZSgvXFxiXFx3L2csIGwgPT4gbC50b1VwcGVyQ2FzZSgpKTtcbn07XG4iXSwibmFtZXMiOlsiaXNMaWtlbHlSb2xlTmFtZSIsInN0ciIsInRlY2huaWNhbFBhdHRlcm5zIiwic29tZSIsInBhdHRlcm4iLCJ0ZXN0IiwibGVuZ3RoIiwidHJhbnNmb3JtUm9sZVVzZWQiLCJyb2xlVXNlZCIsInRleHQiLCJ0eXBlIiwiZm9ybWF0Um9sZU5hbWUiLCJkZXRhaWxzIiwiaW5jbHVkZXMiLCJhdHRlbXB0TWF0Y2giLCJtYXRjaCIsImF0dGVtcHQiLCJwYXJzZUludCIsInVuZGVmaW5lZCIsInN0YXR1c01hdGNoIiwic3RhdHVzIiwiY291bnRNYXRjaCIsImNvdW50IiwiZW5oYW5jZWRNYXRjaCIsImNsYXNzaWZpZWRMZXZlbCIsInVzZWRMZXZlbCIsImxldmVsTWF0Y2giLCJsZXZlbCIsInBvc01hdGNoIiwicG9zaXRpb24iLCJyb2xlTWF0Y2giLCJkZXRlY3RlZFJvbGUiLCJleHRyYWN0TWVhbmluZ2Z1bEluZm8iLCJjb21wbGV4aXR5TWF0Y2giLCJyb2xlTmFtZU1hdGNoIiwiZXh0cmFjdFJvbGVGcm9tUGF0dGVybiIsImZhbGxiYWNrTWF0Y2giLCJpc1N1Y2Nlc3MiLCJ0b0xvd2VyQ2FzZSIsImlzRmFpbGVkIiwiY2xlYW5lZFRleHQiLCJjbGVhblJhd1JvbGVVc2VkIiwiY2xhc3NpZmllZE1hdGNoIiwiZmJSb2xlTWF0Y2giLCJjbGVhbmVkIiwicmVwbGFjZSIsInNwbGl0IiwibWFwIiwid29yZCIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJqb2luIiwic3Vic3RyaW5nIiwicm9sZU5hbWUiLCJnZXRSb2xlVXNlZEJhZGdlQ2xhc3MiLCJnZW5lcmF0ZVJvbGVVc2VkTWVzc2FnZSIsImRlZmF1bHRLZXlTdWNjZXNzIiwiZGVmYXVsdEtleUZhaWxlZCIsImFsbEtleXNGYWlsZWQiLCJhdHRlbXB0Q291bnQiLCJyb2xlUm91dGluZyIsImludGVsbGlnZW50Um9sZVJvdXRpbmciLCJjb21wbGV4aXR5Um91dGluZyIsImtleUluZGV4IiwiZmFsbGJhY2tSb3V0aW5nIiwibm9LZXlzQXZhaWxhYmxlIiwiY29uZmlndXJhdGlvbkVycm9yIiwicm91dGluZ1N0cmF0ZWd5RXJyb3IiLCJzdHJhdGVneSIsImZvcm1hdFByb3ZpZGVyTmFtZSIsInByb3ZpZGVyIiwicHJvdmlkZXJNYXAiLCJmb3JtYXRNb2RlbE5hbWUiLCJtb2RlbE5hbWUiLCJsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/logFormatting.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/cookie","vendor-chunks/zod"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fanalytics%2Fsummary%2Froute&page=%2Fapi%2Fanalytics%2Fsummary%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fanalytics%2Fsummary%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();