/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/chat/conversations/route";
exports.ids = ["app/api/chat/conversations/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_chat_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/chat/conversations/route.ts */ \"(rsc)/./src/app/api/chat/conversations/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/chat/conversations/route\",\n        pathname: \"/api/chat/conversations\",\n        filename: \"route\",\n        bundlePath: \"app/api/chat/conversations/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\chat\\\\conversations\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_chat_conversations_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/chat/conversations/route.ts":
/*!*************************************************!*\
  !*** ./src/app/api/chat/conversations/route.ts ***!
  \*************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n\n\n// GET /api/chat/conversations?custom_api_config_id=<ID> OR ?workflow_id=<ID>\n// Retrieves all chat conversations for a specific custom_api_config_id or workflow_id\nasync function GET(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user (more secure than getSession)\n    const { data: { user }, error: authError } = await supabase.auth.getUser();\n    if (authError || !user) {\n        console.error('Authentication error in GET conversations:', authError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to view conversations.'\n        }, {\n            status: 401\n        });\n    }\n    const { searchParams } = new URL(request.url);\n    const customApiConfigId = searchParams.get('custom_api_config_id');\n    const workflowId = searchParams.get('workflow_id');\n    if (!customApiConfigId && !workflowId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Either custom_api_config_id or workflow_id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    if (customApiConfigId && workflowId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Cannot specify both custom_api_config_id and workflow_id'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Phase 2A Optimization: Get conversations with optimized query - only fetch what we need for the list\n        let query = supabase.from('chat_conversations').select(`\n        id,\n        custom_api_config_id,\n        workflow_id,\n        title,\n        created_at,\n        updated_at\n      `).eq('user_id', user.id) // Filter by user_id for RLS compliance\n        .order('updated_at', {\n            ascending: false\n        }).limit(25); // Phase 1 Optimization: Reduced from 50 to 25 for faster loading\n        // Filter by either custom_api_config_id or workflow_id\n        if (customApiConfigId) {\n            query = query.eq('custom_api_config_id', customApiConfigId);\n        } else if (workflowId) {\n            query = query.eq('workflow_id', workflowId);\n        }\n        const { data: conversations, error } = await query;\n        if (error) {\n            console.error('Supabase error fetching conversations:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to fetch conversations',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        // Get message counts and last message previews efficiently\n        const conversationIds = (conversations || []).map((conv)=>conv.id);\n        if (conversationIds.length === 0) {\n            const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json([], {\n                status: 200\n            });\n            response.headers.set('Cache-Control', 'private, max-age=30'); // Cache for 30 seconds\n            return response;\n        }\n        // Phase 2A Optimization: Use more efficient query with proper indexing\n        const { data: messageStats, error: statsError } = await supabase.from('chat_messages').select('conversation_id, content, created_at, role').in('conversation_id', conversationIds).order('created_at', {\n            ascending: false\n        }) // Simplified ordering for better index usage\n        .limit(conversationIds.length * 2); // Limit to prevent excessive data fetching\n        if (statsError) {\n            console.warn('Error fetching message metadata:', statsError);\n        }\n        // Process message stats efficiently\n        const conversationStats = new Map();\n        if (messageStats) {\n            for (const msg of messageStats){\n                const convId = msg.conversation_id;\n                const existing = conversationStats.get(convId);\n                if (!existing) {\n                    conversationStats.set(convId, {\n                        count: 1,\n                        lastMessage: msg\n                    });\n                } else {\n                    existing.count++;\n                    // Keep the most recent message (first in our ordered result)\n                    if (!existing.lastMessage) {\n                        existing.lastMessage = msg;\n                    }\n                }\n            }\n        }\n        // Process conversations with metadata\n        const processedConversations = (conversations || []).map((conv)=>{\n            const stats = conversationStats.get(conv.id) || {\n                count: 0\n            };\n            // Get last message preview\n            let lastMessagePreview = '';\n            if (stats.lastMessage?.content && Array.isArray(stats.lastMessage.content)) {\n                const textContent = stats.lastMessage.content.filter((part)=>part.type === 'text' && part.text).map((part)=>part.text).join(' ');\n                lastMessagePreview = textContent.length > 100 ? textContent.substring(0, 100) + '...' : textContent;\n            }\n            return {\n                id: conv.id,\n                custom_api_config_id: conv.custom_api_config_id,\n                title: conv.title,\n                created_at: conv.created_at,\n                updated_at: conv.updated_at,\n                message_count: stats.count,\n                last_message_preview: lastMessagePreview\n            };\n        });\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(processedConversations, {\n            status: 200\n        });\n        // Enhanced caching headers for better performance\n        const isPrefetch = request.headers.get('X-Prefetch') === 'true';\n        const cacheMaxAge = isPrefetch ? 300 : 30; // 5 minutes for prefetch, 30 seconds for regular\n        response.headers.set('Cache-Control', `private, max-age=${cacheMaxAge}, stale-while-revalidate=60`);\n        response.headers.set('X-Content-Type-Options', 'nosniff');\n        response.headers.set('Vary', 'X-Prefetch');\n        return response;\n    } catch (e) {\n        console.error('Error in GET /api/chat/conversations:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// POST /api/chat/conversations\n// Creates a new chat conversation\nasync function POST(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in POST conversation:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to create conversations.'\n        }, {\n            status: 401\n        });\n    }\n    const user = session.user;\n    try {\n        const conversationData = await request.json();\n        const { custom_api_config_id, workflow_id, title } = conversationData;\n        if (!title) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Missing required field: title'\n            }, {\n                status: 400\n            });\n        }\n        if (!custom_api_config_id && !workflow_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Either custom_api_config_id or workflow_id is required'\n            }, {\n                status: 400\n            });\n        }\n        if (custom_api_config_id && workflow_id) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Cannot specify both custom_api_config_id and workflow_id'\n            }, {\n                status: 400\n            });\n        }\n        const insertData = {\n            title,\n            user_id: user.id\n        };\n        if (custom_api_config_id) {\n            insertData.custom_api_config_id = custom_api_config_id;\n        } else if (workflow_id) {\n            insertData.workflow_id = workflow_id;\n        }\n        const { data, error } = await supabase.from('chat_conversations').insert(insertData).select().single();\n        if (error) {\n            console.error('Supabase error creating conversation:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to create conversation',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(data, {\n            status: 201\n        });\n    } catch (e) {\n        console.error('Error in POST /api/chat/conversations:', e);\n        if (e.name === 'SyntaxError') {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Invalid request body: Malformed JSON.'\n            }, {\n                status: 400\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n// DELETE /api/chat/conversations?id=<ID>\n// Deletes a specific conversation and all its messages\nasync function DELETE(request) {\n    const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createSupabaseServerClientFromRequest)(request);\n    // Get authenticated user from session\n    const { data: { session }, error: sessionError } = await supabase.auth.getSession();\n    if (sessionError || !session?.user) {\n        console.error('Authentication error in DELETE conversation:', sessionError);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Unauthorized: You must be logged in to delete conversations.'\n        }, {\n            status: 401\n        });\n    }\n    const user = session.user;\n    const { searchParams } = new URL(request.url);\n    const conversationId = searchParams.get('id');\n    if (!conversationId) {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'id query parameter is required'\n        }, {\n            status: 400\n        });\n    }\n    try {\n        // Delete conversation (messages will be deleted automatically due to CASCADE)\n        const { error } = await supabase.from('chat_conversations').delete().eq('id', conversationId).eq('user_id', user.id); // Filter by user_id for RLS compliance\n        if (error) {\n            console.error('Supabase error deleting conversation:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Failed to delete conversation',\n                details: error.message\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            message: 'Conversation deleted successfully'\n        }, {\n            status: 200\n        });\n    } catch (e) {\n        console.error('Error in DELETE /api/chat/conversations:', e);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'An unexpected error occurred',\n            details: e.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/chat/conversations/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServiceRoleClient: () => (/* binding */ createServiceRoleClient),\n/* harmony export */   createSupabaseServerClientFromRequest: () => (/* binding */ createSupabaseServerClientFromRequest),\n/* harmony export */   createSupabaseServerClientOnRequest: () => (/* binding */ createSupabaseServerClientOnRequest)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\n\n// This is the standard setup for creating a Supabase server client\n// in Next.js App Router (Server Components, Route Handlers, Server Actions).\n// Updated for Next.js 15 async cookies requirement\nasync function createSupabaseServerClientOnRequest() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return cookieStore.get(name)?.value;\n            },\n            set (name, value, options) {\n                try {\n                    cookieStore.set({\n                        name,\n                        value,\n                        ...options\n                    });\n                } catch (error) {\n                    // This error can be ignored if running in a Server Component\n                    // where cookies can't be set directly. Cookie setting should be\n                    // handled in Server Actions or Route Handlers.\n                    console.warn(`Failed to set cookie '${name}' (might be in a Server Component):`, error);\n                }\n            },\n            remove (name, options) {\n                try {\n                    // To remove a cookie using the `set` method from `next/headers`,\n                    // you typically set it with an empty value and Max-Age=0 or an expiry date in the past.\n                    cookieStore.set({\n                        name,\n                        value: '',\n                        ...options\n                    });\n                } catch (error) {\n                    // Similar to set, this might fail in a Server Component.\n                    console.warn(`Failed to remove cookie '${name}' (might be in a Server Component):`, error);\n                }\n            }\n        }\n    });\n}\n// Alternative method for API routes that need to handle cookies from request\nfunction createSupabaseServerClientFromRequest(request) {\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imhwa3p6aHB1ZmhieHR4cWF1Z2poIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg3MDQ2MjYsImV4cCI6MjA2NDI4MDYyNn0.iEyssjL4TR3fJMLTyn2Vj4wMVpShuoGTyw3M4R9OZz8\", {\n        cookies: {\n            get (name) {\n                return request.cookies.get(name)?.value;\n            },\n            set (name, value, options) {\n            // In API routes, we can't set cookies directly on the request\n            // This will be handled by the response\n            },\n            remove (name, options) {\n            // In API routes, we can't remove cookies directly on the request\n            // This will be handled by the response\n            }\n        }\n    });\n}\n// Service role client for admin operations (OAuth token storage, etc.)\nfunction createServiceRoleClient() {\n    return (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n        auth: {\n            autoRefreshToken: false,\n            persistSession: false\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fchat%2Fconversations%2Froute&page=%2Fapi%2Fchat%2Fconversations%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fchat%2Fconversations%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();