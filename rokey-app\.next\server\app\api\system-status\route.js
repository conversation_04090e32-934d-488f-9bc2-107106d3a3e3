/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/system-status/route";
exports.ids = ["app/api/system-status/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive":
/*!************************************************************!*\
  !*** ./node_modules/@supabase/realtime-js/dist/main/ sync ***!
  \************************************************************/
/***/ ((module) => {

function webpackEmptyContext(req) {
	var e = new Error("Cannot find module '" + req + "'");
	e.code = 'MODULE_NOT_FOUND';
	throw e;
}
webpackEmptyContext.keys = () => ([]);
webpackEmptyContext.resolve = webpackEmptyContext;
webpackEmptyContext.id = "(rsc)/./node_modules/@supabase/realtime-js/dist/main sync recursive";
module.exports = webpackEmptyContext;

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-status%2Froute&page=%2Fapi%2Fsystem-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-status%2Froute&page=%2Fapi%2Fsystem-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_RoKey_App_rokey_app_src_app_api_system_status_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/system-status/route.ts */ \"(rsc)/./src/app/api/system-status/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/system-status/route\",\n        pathname: \"/api/system-status\",\n        filename: \"route\",\n        bundlePath: \"app/api/system-status/route\"\n    },\n    resolvedPagePath: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\api\\\\system-status\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_RoKey_App_rokey_app_src_app_api_system_status_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-status%2Froute&page=%2Fapi%2Fsystem-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/system-status/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/system-status/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=createClient!=!@supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n\nasync function GET(request) {\n    try {\n        // Use service role client to bypass RLS for system-wide health checks\n        const supabase = (0,_barrel_optimize_names_createClient_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://hpkzzhpufhbxtxqaugjh.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n            auth: {\n                autoRefreshToken: false,\n                persistSession: false\n            }\n        });\n        // Phase 2A Optimization: Run all health checks in parallel for much faster response\n        const oneDayAgo = new Date();\n        oneDayAgo.setDate(oneDayAgo.getDate() - 1);\n        const [apiGatewayResult, routingEngineResult, analyticsResult] = await Promise.allSettled([\n            // API Gateway check\n            supabase.from('custom_api_configs').select('id').limit(1),\n            // Routing Engine check\n            supabase.from('api_keys').select('id').eq('status', 'active').limit(1),\n            // Analytics check\n            supabase.from('request_logs').select('id').gte('request_timestamp', oneDayAgo.toISOString()).limit(1)\n        ]);\n        const checks = [];\n        // Process API Gateway result\n        let apiGatewayStatus = 'operational';\n        let apiGatewayDetails = '';\n        if (apiGatewayResult.status === 'rejected') {\n            apiGatewayStatus = 'down';\n            apiGatewayDetails = apiGatewayResult.reason?.message || 'Connection failed';\n        } else if (apiGatewayResult.value.error) {\n            apiGatewayStatus = 'down';\n            apiGatewayDetails = apiGatewayResult.value.error.message;\n        }\n        checks.push({\n            name: 'API Gateway',\n            status: apiGatewayStatus,\n            details: apiGatewayDetails,\n            lastChecked: new Date().toISOString()\n        });\n        // Process Routing Engine result\n        let routingEngineStatus = 'operational';\n        let routingEngineDetails = '';\n        if (routingEngineResult.status === 'rejected') {\n            routingEngineStatus = 'down';\n            routingEngineDetails = routingEngineResult.reason?.message || 'Connection failed';\n        } else if (routingEngineResult.value.error) {\n            routingEngineStatus = 'degraded';\n            routingEngineDetails = 'Error checking active keys';\n        } else if (!routingEngineResult.value.data || routingEngineResult.value.data.length === 0) {\n            routingEngineStatus = 'degraded';\n            routingEngineDetails = 'No active API keys found';\n        }\n        checks.push({\n            name: 'Routing Engine',\n            status: routingEngineStatus,\n            details: routingEngineDetails,\n            lastChecked: new Date().toISOString()\n        });\n        // Process Analytics result\n        let analyticsStatus = 'operational';\n        let analyticsDetails = '';\n        if (analyticsResult.status === 'rejected') {\n            analyticsStatus = 'down';\n            analyticsDetails = analyticsResult.reason?.message || 'Connection failed';\n        } else if (analyticsResult.value.error) {\n            analyticsStatus = 'degraded';\n            analyticsDetails = 'Error checking recent logs';\n        } else if (!analyticsResult.value.data || analyticsResult.value.data.length === 0) {\n            analyticsStatus = 'degraded';\n            analyticsDetails = 'No recent activity logged';\n        }\n        checks.push({\n            name: 'Analytics',\n            status: analyticsStatus,\n            details: analyticsDetails,\n            lastChecked: new Date().toISOString()\n        });\n        // Calculate overall system health\n        const hasDown = checks.some((check)=>check.status === 'down');\n        const hasDegraded = checks.some((check)=>check.status === 'degraded');\n        let overallStatus = 'operational';\n        if (hasDown) {\n            overallStatus = 'down';\n        } else if (hasDegraded) {\n            overallStatus = 'degraded';\n        }\n        const response = next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            overall_status: overallStatus,\n            checks,\n            last_updated: new Date().toISOString()\n        });\n        // Phase 2A Optimization: Add caching for system status\n        response.headers.set('Cache-Control', 'public, max-age=30, stale-while-revalidate=60');\n        response.headers.set('X-Content-Type-Options', 'nosniff');\n        return response;\n    } catch (error) {\n        console.error('Error in /api/system-status:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            overall_status: 'down',\n            checks: [\n                {\n                    name: 'System Check',\n                    status: 'down',\n                    details: 'Failed to perform system health checks',\n                    lastChecked: new Date().toISOString()\n                }\n            ],\n            last_updated: new Date().toISOString(),\n            error: error.message\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/system-status/route.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fsystem-status%2Froute&page=%2Fapi%2Fsystem-status%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsystem-status%2Froute.ts&appDir=C%3A%5CRoKey%20App%5Crokey-app%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CRoKey%20App%5Crokey-app&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();