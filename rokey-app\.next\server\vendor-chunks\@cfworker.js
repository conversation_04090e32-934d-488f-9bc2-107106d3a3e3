"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@cfworker";
exports.ids = ["vendor-chunks/@cfworker"];
exports.modules = {

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js":
/*!****************************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js ***!
  \****************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   deepCompareStrict: () => (/* binding */ deepCompareStrict)\n/* harmony export */ });\nfunction deepCompareStrict(a, b) {\n    const typeofa = typeof a;\n    if (typeofa !== typeof b) {\n        return false;\n    }\n    if (Array.isArray(a)) {\n        if (!Array.isArray(b)) {\n            return false;\n        }\n        const length = a.length;\n        if (length !== b.length) {\n            return false;\n        }\n        for (let i = 0; i < length; i++) {\n            if (!deepCompareStrict(a[i], b[i])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    if (typeofa === 'object') {\n        if (!a || !b) {\n            return a === b;\n        }\n        const aKeys = Object.keys(a);\n        const bKeys = Object.keys(b);\n        const length = aKeys.length;\n        if (length !== bKeys.length) {\n            return false;\n        }\n        for (const k of aKeys) {\n            if (!deepCompareStrict(a[k], b[k])) {\n                return false;\n            }\n        }\n        return true;\n    }\n    return a === b;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js":
/*!********************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/dereference.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   dereference: () => (/* binding */ dereference),\n/* harmony export */   ignoredKeyword: () => (/* binding */ ignoredKeyword),\n/* harmony export */   initialBaseURI: () => (/* binding */ initialBaseURI),\n/* harmony export */   schemaArrayKeyword: () => (/* binding */ schemaArrayKeyword),\n/* harmony export */   schemaKeyword: () => (/* binding */ schemaKeyword),\n/* harmony export */   schemaMapKeyword: () => (/* binding */ schemaMapKeyword)\n/* harmony export */ });\n/* harmony import */ var _pointer_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js\");\n\nconst schemaKeyword = {\n    additionalItems: true,\n    unevaluatedItems: true,\n    items: true,\n    contains: true,\n    additionalProperties: true,\n    unevaluatedProperties: true,\n    propertyNames: true,\n    not: true,\n    if: true,\n    then: true,\n    else: true\n};\nconst schemaArrayKeyword = {\n    prefixItems: true,\n    items: true,\n    allOf: true,\n    anyOf: true,\n    oneOf: true\n};\nconst schemaMapKeyword = {\n    $defs: true,\n    definitions: true,\n    properties: true,\n    patternProperties: true,\n    dependentSchemas: true\n};\nconst ignoredKeyword = {\n    id: true,\n    $id: true,\n    $ref: true,\n    $schema: true,\n    $anchor: true,\n    $vocabulary: true,\n    $comment: true,\n    default: true,\n    enum: true,\n    const: true,\n    required: true,\n    type: true,\n    maximum: true,\n    minimum: true,\n    exclusiveMaximum: true,\n    exclusiveMinimum: true,\n    multipleOf: true,\n    maxLength: true,\n    minLength: true,\n    pattern: true,\n    format: true,\n    maxItems: true,\n    minItems: true,\n    uniqueItems: true,\n    maxProperties: true,\n    minProperties: true\n};\nlet initialBaseURI = typeof self !== 'undefined' &&\n    self.location &&\n    self.location.origin !== 'null'\n    ?\n        new URL(self.location.origin + self.location.pathname + location.search)\n    : new URL('https://github.com/cfworker');\nfunction dereference(schema, lookup = Object.create(null), baseURI = initialBaseURI, basePointer = '') {\n    if (schema && typeof schema === 'object' && !Array.isArray(schema)) {\n        const id = schema.$id || schema.id;\n        if (id) {\n            const url = new URL(id, baseURI.href);\n            if (url.hash.length > 1) {\n                lookup[url.href] = schema;\n            }\n            else {\n                url.hash = '';\n                if (basePointer === '') {\n                    baseURI = url;\n                }\n                else {\n                    dereference(schema, lookup, baseURI);\n                }\n            }\n        }\n    }\n    else if (schema !== true && schema !== false) {\n        return lookup;\n    }\n    const schemaURI = baseURI.href + (basePointer ? '#' + basePointer : '');\n    if (lookup[schemaURI] !== undefined) {\n        throw new Error(`Duplicate schema URI \"${schemaURI}\".`);\n    }\n    lookup[schemaURI] = schema;\n    if (schema === true || schema === false) {\n        return lookup;\n    }\n    if (schema.__absolute_uri__ === undefined) {\n        Object.defineProperty(schema, '__absolute_uri__', {\n            enumerable: false,\n            value: schemaURI\n        });\n    }\n    if (schema.$ref && schema.__absolute_ref__ === undefined) {\n        const url = new URL(schema.$ref, baseURI.href);\n        url.hash = url.hash;\n        Object.defineProperty(schema, '__absolute_ref__', {\n            enumerable: false,\n            value: url.href\n        });\n    }\n    if (schema.$recursiveRef && schema.__absolute_recursive_ref__ === undefined) {\n        const url = new URL(schema.$recursiveRef, baseURI.href);\n        url.hash = url.hash;\n        Object.defineProperty(schema, '__absolute_recursive_ref__', {\n            enumerable: false,\n            value: url.href\n        });\n    }\n    if (schema.$anchor) {\n        const url = new URL('#' + schema.$anchor, baseURI.href);\n        lookup[url.href] = schema;\n    }\n    for (let key in schema) {\n        if (ignoredKeyword[key]) {\n            continue;\n        }\n        const keyBase = `${basePointer}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_0__.encodePointer)(key)}`;\n        const subSchema = schema[key];\n        if (Array.isArray(subSchema)) {\n            if (schemaArrayKeyword[key]) {\n                const length = subSchema.length;\n                for (let i = 0; i < length; i++) {\n                    dereference(subSchema[i], lookup, baseURI, `${keyBase}/${i}`);\n                }\n            }\n        }\n        else if (schemaMapKeyword[key]) {\n            for (let subKey in subSchema) {\n                dereference(subSchema[subKey], lookup, baseURI, `${keyBase}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_0__.encodePointer)(subKey)}`);\n            }\n        }\n        else {\n            dereference(subSchema, lookup, baseURI, keyBase);\n        }\n    }\n    return lookup;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/format.js":
/*!***************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/format.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   format: () => (/* binding */ format)\n/* harmony export */ });\nconst DATE = /^(\\d\\d\\d\\d)-(\\d\\d)-(\\d\\d)$/;\nconst DAYS = [0, 31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst TIME = /^(\\d\\d):(\\d\\d):(\\d\\d)(\\.\\d+)?(z|[+-]\\d\\d(?::?\\d\\d)?)?$/i;\nconst HOSTNAME = /^(?=.{1,253}\\.?$)[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*\\.?$/i;\nconst URIREF = /^(?:[a-z][a-z0-9+\\-.]*:)?(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'\"()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'\"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\\?(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'\"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nconst URITEMPLATE = /^(?:(?:[^\\x00-\\x20\"'<>%\\\\^`{|}]|%[0-9a-f]{2})|\\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\\*)?)*\\})*$/i;\nconst URL_ = /^(?:(?:https?|ftp):\\/\\/)(?:\\S+(?::\\S*)?@)?(?:(?!10(?:\\.\\d{1,3}){3})(?!127(?:\\.\\d{1,3}){3})(?!169\\.254(?:\\.\\d{1,3}){2})(?!192\\.168(?:\\.\\d{1,3}){2})(?!172\\.(?:1[6-9]|2\\d|3[0-1])(?:\\.\\d{1,3}){2})(?:[1-9]\\d?|1\\d\\d|2[01]\\d|22[0-3])(?:\\.(?:1?\\d{1,2}|2[0-4]\\d|25[0-5])){2}(?:\\.(?:[1-9]\\d?|1\\d\\d|2[0-4]\\d|25[0-4]))|(?:(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-?)*[a-z\\u{00a1}-\\u{ffff}0-9]+)(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}0-9]+-?)*[a-z\\u{00a1}-\\u{ffff}0-9]+)*(?:\\.(?:[a-z\\u{00a1}-\\u{ffff}]{2,})))(?::\\d{2,5})?(?:\\/[^\\s]*)?$/iu;\nconst UUID = /^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i;\nconst JSON_POINTER = /^(?:\\/(?:[^~/]|~0|~1)*)*$/;\nconst JSON_POINTER_URI_FRAGMENT = /^#(?:\\/(?:[a-z0-9_\\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i;\nconst RELATIVE_JSON_POINTER = /^(?:0|[1-9][0-9]*)(?:#|(?:\\/(?:[^~/]|~0|~1)*)*)$/;\nconst EMAIL = (input) => {\n    if (input[0] === '\"')\n        return false;\n    const [name, host, ...rest] = input.split('@');\n    if (!name ||\n        !host ||\n        rest.length !== 0 ||\n        name.length > 64 ||\n        host.length > 253)\n        return false;\n    if (name[0] === '.' || name.endsWith('.') || name.includes('..'))\n        return false;\n    if (!/^[a-z0-9.-]+$/i.test(host) ||\n        !/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+$/i.test(name))\n        return false;\n    return host\n        .split('.')\n        .every(part => /^[a-z0-9]([a-z0-9-]{0,61}[a-z0-9])?$/i.test(part));\n};\nconst IPV4 = /^(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$/;\nconst IPV6 = /^((([0-9a-f]{1,4}:){7}([0-9a-f]{1,4}|:))|(([0-9a-f]{1,4}:){6}(:[0-9a-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){5}(((:[0-9a-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9a-f]{1,4}:){4}(((:[0-9a-f]{1,4}){1,3})|((:[0-9a-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){3}(((:[0-9a-f]{1,4}){1,4})|((:[0-9a-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){2}(((:[0-9a-f]{1,4}){1,5})|((:[0-9a-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9a-f]{1,4}:){1}(((:[0-9a-f]{1,4}){1,6})|((:[0-9a-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9a-f]{1,4}){1,7})|((:[0-9a-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))$/i;\nconst DURATION = (input) => input.length > 1 &&\n    input.length < 80 &&\n    (/^P\\d+([.,]\\d+)?W$/.test(input) ||\n        (/^P[\\dYMDTHS]*(\\d[.,]\\d+)?[YMDHS]$/.test(input) &&\n            /^P([.,\\d]+Y)?([.,\\d]+M)?([.,\\d]+D)?(T([.,\\d]+H)?([.,\\d]+M)?([.,\\d]+S)?)?$/.test(input)));\nfunction bind(r) {\n    return r.test.bind(r);\n}\nconst format = {\n    date,\n    time: time.bind(undefined, false),\n    'date-time': date_time,\n    duration: DURATION,\n    uri,\n    'uri-reference': bind(URIREF),\n    'uri-template': bind(URITEMPLATE),\n    url: bind(URL_),\n    email: EMAIL,\n    hostname: bind(HOSTNAME),\n    ipv4: bind(IPV4),\n    ipv6: bind(IPV6),\n    regex: regex,\n    uuid: bind(UUID),\n    'json-pointer': bind(JSON_POINTER),\n    'json-pointer-uri-fragment': bind(JSON_POINTER_URI_FRAGMENT),\n    'relative-json-pointer': bind(RELATIVE_JSON_POINTER)\n};\nfunction isLeapYear(year) {\n    return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\nfunction date(str) {\n    const matches = str.match(DATE);\n    if (!matches)\n        return false;\n    const year = +matches[1];\n    const month = +matches[2];\n    const day = +matches[3];\n    return (month >= 1 &&\n        month <= 12 &&\n        day >= 1 &&\n        day <= (month == 2 && isLeapYear(year) ? 29 : DAYS[month]));\n}\nfunction time(full, str) {\n    const matches = str.match(TIME);\n    if (!matches)\n        return false;\n    const hour = +matches[1];\n    const minute = +matches[2];\n    const second = +matches[3];\n    const timeZone = !!matches[5];\n    return (((hour <= 23 && minute <= 59 && second <= 59) ||\n        (hour == 23 && minute == 59 && second == 60)) &&\n        (!full || timeZone));\n}\nconst DATE_TIME_SEPARATOR = /t|\\s/i;\nfunction date_time(str) {\n    const dateTime = str.split(DATE_TIME_SEPARATOR);\n    return dateTime.length == 2 && date(dateTime[0]) && time(true, dateTime[1]);\n}\nconst NOT_URI_FRAGMENT = /\\/|:/;\nconst URI_PATTERN = /^(?:[a-z][a-z0-9+\\-.]*:)(?:\\/?\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\\.[a-z0-9\\-._~!$&'()*+,;=:]+)\\]|(?:(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d\\d?)|(?:[a-z0-9\\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\\d*)?(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\\/(?:(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\\/(?:[a-z0-9\\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\\?(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i;\nfunction uri(str) {\n    return NOT_URI_FRAGMENT.test(str) && URI_PATTERN.test(str);\n}\nconst Z_ANCHOR = /[^\\\\]\\\\Z/;\nfunction regex(str) {\n    if (Z_ANCHOR.test(str))\n        return false;\n    try {\n        new RegExp(str, 'u');\n        return true;\n    }\n    catch (e) {\n        return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/format.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutputFormat: () => (/* reexport safe */ _types_js__WEBPACK_IMPORTED_MODULE_4__.OutputFormat),\n/* harmony export */   Validator: () => (/* reexport safe */ _validator_js__WEBPACK_IMPORTED_MODULE_7__.Validator),\n/* harmony export */   deepCompareStrict: () => (/* reexport safe */ _deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__.deepCompareStrict),\n/* harmony export */   dereference: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.dereference),\n/* harmony export */   encodePointer: () => (/* reexport safe */ _pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer),\n/* harmony export */   escapePointer: () => (/* reexport safe */ _pointer_js__WEBPACK_IMPORTED_MODULE_3__.escapePointer),\n/* harmony export */   format: () => (/* reexport safe */ _format_js__WEBPACK_IMPORTED_MODULE_2__.format),\n/* harmony export */   ignoredKeyword: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.ignoredKeyword),\n/* harmony export */   initialBaseURI: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.initialBaseURI),\n/* harmony export */   schemaArrayKeyword: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.schemaArrayKeyword),\n/* harmony export */   schemaKeyword: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.schemaKeyword),\n/* harmony export */   schemaMapKeyword: () => (/* reexport safe */ _dereference_js__WEBPACK_IMPORTED_MODULE_1__.schemaMapKeyword),\n/* harmony export */   ucs2length: () => (/* reexport safe */ _ucs2_length_js__WEBPACK_IMPORTED_MODULE_5__.ucs2length),\n/* harmony export */   validate: () => (/* reexport safe */ _validate_js__WEBPACK_IMPORTED_MODULE_6__.validate)\n/* harmony export */ });\n/* harmony import */ var _deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deep-compare-strict.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js\");\n/* harmony import */ var _dereference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js\");\n/* harmony import */ var _format_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./format.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/format.js\");\n/* harmony import */ var _pointer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js\");\n/* harmony import */ var _types_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./types.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/types.js\");\n/* harmony import */ var _ucs2_length_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ucs2-length.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js\");\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validate.js\");\n/* harmony import */ var _validator_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./validator.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validator.js\");\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBeUM7QUFDUjtBQUNMO0FBQ0M7QUFDRjtBQUNNO0FBQ0g7QUFDQyIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXEBjZndvcmtlclxcanNvbi1zY2hlbWFcXGRpc3RcXGVzbVxcaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0ICogZnJvbSAnLi9kZWVwLWNvbXBhcmUtc3RyaWN0LmpzJztcbmV4cG9ydCAqIGZyb20gJy4vZGVyZWZlcmVuY2UuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9mb3JtYXQuanMnO1xuZXhwb3J0ICogZnJvbSAnLi9wb2ludGVyLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdHlwZXMuanMnO1xuZXhwb3J0ICogZnJvbSAnLi91Y3MyLWxlbmd0aC5qcyc7XG5leHBvcnQgKiBmcm9tICcuL3ZhbGlkYXRlLmpzJztcbmV4cG9ydCAqIGZyb20gJy4vdmFsaWRhdG9yLmpzJztcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js":
/*!****************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/pointer.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   encodePointer: () => (/* binding */ encodePointer),\n/* harmony export */   escapePointer: () => (/* binding */ escapePointer)\n/* harmony export */ });\nfunction encodePointer(p) {\n    return encodeURI(escapePointer(p));\n}\nfunction escapePointer(p) {\n    return p.replace(/~/g, '~0').replace(/\\//g, '~1');\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL3BvaW50ZXIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDTztBQUNQO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxAY2Z3b3JrZXJcXGpzb24tc2NoZW1hXFxkaXN0XFxlc21cXHBvaW50ZXIuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGZ1bmN0aW9uIGVuY29kZVBvaW50ZXIocCkge1xuICAgIHJldHVybiBlbmNvZGVVUkkoZXNjYXBlUG9pbnRlcihwKSk7XG59XG5leHBvcnQgZnVuY3Rpb24gZXNjYXBlUG9pbnRlcihwKSB7XG4gICAgcmV0dXJuIHAucmVwbGFjZSgvfi9nLCAnfjAnKS5yZXBsYWNlKC9cXC8vZywgJ34xJyk7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/types.js":
/*!**************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/types.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OutputFormat: () => (/* binding */ OutputFormat)\n/* harmony export */ });\nvar OutputFormat;\n(function (OutputFormat) {\n    OutputFormat[OutputFormat[\"Flag\"] = 1] = \"Flag\";\n    OutputFormat[OutputFormat[\"Basic\"] = 2] = \"Basic\";\n    OutputFormat[OutputFormat[\"Detailed\"] = 4] = \"Detailed\";\n})(OutputFormat || (OutputFormat = {}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL3R5cGVzLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQyxvQ0FBb0MiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxAY2Z3b3JrZXJcXGpzb24tc2NoZW1hXFxkaXN0XFxlc21cXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCB2YXIgT3V0cHV0Rm9ybWF0O1xuKGZ1bmN0aW9uIChPdXRwdXRGb3JtYXQpIHtcbiAgICBPdXRwdXRGb3JtYXRbT3V0cHV0Rm9ybWF0W1wiRmxhZ1wiXSA9IDFdID0gXCJGbGFnXCI7XG4gICAgT3V0cHV0Rm9ybWF0W091dHB1dEZvcm1hdFtcIkJhc2ljXCJdID0gMl0gPSBcIkJhc2ljXCI7XG4gICAgT3V0cHV0Rm9ybWF0W091dHB1dEZvcm1hdFtcIkRldGFpbGVkXCJdID0gNF0gPSBcIkRldGFpbGVkXCI7XG59KShPdXRwdXRGb3JtYXQgfHwgKE91dHB1dEZvcm1hdCA9IHt9KSk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/types.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js":
/*!********************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ucs2length: () => (/* binding */ ucs2length)\n/* harmony export */ });\nfunction ucs2length(s) {\n    let result = 0;\n    let length = s.length;\n    let index = 0;\n    let charCode;\n    while (index < length) {\n        result++;\n        charCode = s.charCodeAt(index++);\n        if (charCode >= 0xd800 && charCode <= 0xdbff && index < length) {\n            charCode = s.charCodeAt(index);\n            if ((charCode & 0xfc00) == 0xdc00) {\n                index++;\n            }\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL3VjczItbGVuZ3RoLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcQGNmd29ya2VyXFxqc29uLXNjaGVtYVxcZGlzdFxcZXNtXFx1Y3MyLWxlbmd0aC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZnVuY3Rpb24gdWNzMmxlbmd0aChzKSB7XG4gICAgbGV0IHJlc3VsdCA9IDA7XG4gICAgbGV0IGxlbmd0aCA9IHMubGVuZ3RoO1xuICAgIGxldCBpbmRleCA9IDA7XG4gICAgbGV0IGNoYXJDb2RlO1xuICAgIHdoaWxlIChpbmRleCA8IGxlbmd0aCkge1xuICAgICAgICByZXN1bHQrKztcbiAgICAgICAgY2hhckNvZGUgPSBzLmNoYXJDb2RlQXQoaW5kZXgrKyk7XG4gICAgICAgIGlmIChjaGFyQ29kZSA+PSAweGQ4MDAgJiYgY2hhckNvZGUgPD0gMHhkYmZmICYmIGluZGV4IDwgbGVuZ3RoKSB7XG4gICAgICAgICAgICBjaGFyQ29kZSA9IHMuY2hhckNvZGVBdChpbmRleCk7XG4gICAgICAgICAgICBpZiAoKGNoYXJDb2RlICYgMHhmYzAwKSA9PSAweGRjMDApIHtcbiAgICAgICAgICAgICAgICBpbmRleCsrO1xuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybiByZXN1bHQ7XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validate.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/validate.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   validate: () => (/* binding */ validate)\n/* harmony export */ });\n/* harmony import */ var _deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./deep-compare-strict.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/deep-compare-strict.js\");\n/* harmony import */ var _dereference_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js\");\n/* harmony import */ var _format_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./format.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/format.js\");\n/* harmony import */ var _pointer_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pointer.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/pointer.js\");\n/* harmony import */ var _ucs2_length_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ucs2-length.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/ucs2-length.js\");\n\n\n\n\n\nfunction validate(instance, schema, draft = '2019-09', lookup = (0,_dereference_js__WEBPACK_IMPORTED_MODULE_1__.dereference)(schema), shortCircuit = true, recursiveAnchor = null, instanceLocation = '#', schemaLocation = '#', evaluated = Object.create(null)) {\n    if (schema === true) {\n        return { valid: true, errors: [] };\n    }\n    if (schema === false) {\n        return {\n            valid: false,\n            errors: [\n                {\n                    instanceLocation,\n                    keyword: 'false',\n                    keywordLocation: instanceLocation,\n                    error: 'False boolean schema.'\n                }\n            ]\n        };\n    }\n    const rawInstanceType = typeof instance;\n    let instanceType;\n    switch (rawInstanceType) {\n        case 'boolean':\n        case 'number':\n        case 'string':\n            instanceType = rawInstanceType;\n            break;\n        case 'object':\n            if (instance === null) {\n                instanceType = 'null';\n            }\n            else if (Array.isArray(instance)) {\n                instanceType = 'array';\n            }\n            else {\n                instanceType = 'object';\n            }\n            break;\n        default:\n            throw new Error(`Instances of \"${rawInstanceType}\" type are not supported.`);\n    }\n    const { $ref, $recursiveRef, $recursiveAnchor, type: $type, const: $const, enum: $enum, required: $required, not: $not, anyOf: $anyOf, allOf: $allOf, oneOf: $oneOf, if: $if, then: $then, else: $else, format: $format, properties: $properties, patternProperties: $patternProperties, additionalProperties: $additionalProperties, unevaluatedProperties: $unevaluatedProperties, minProperties: $minProperties, maxProperties: $maxProperties, propertyNames: $propertyNames, dependentRequired: $dependentRequired, dependentSchemas: $dependentSchemas, dependencies: $dependencies, prefixItems: $prefixItems, items: $items, additionalItems: $additionalItems, unevaluatedItems: $unevaluatedItems, contains: $contains, minContains: $minContains, maxContains: $maxContains, minItems: $minItems, maxItems: $maxItems, uniqueItems: $uniqueItems, minimum: $minimum, maximum: $maximum, exclusiveMinimum: $exclusiveMinimum, exclusiveMaximum: $exclusiveMaximum, multipleOf: $multipleOf, minLength: $minLength, maxLength: $maxLength, pattern: $pattern, __absolute_ref__, __absolute_recursive_ref__ } = schema;\n    const errors = [];\n    if ($recursiveAnchor === true && recursiveAnchor === null) {\n        recursiveAnchor = schema;\n    }\n    if ($recursiveRef === '#') {\n        const refSchema = recursiveAnchor === null\n            ? lookup[__absolute_recursive_ref__]\n            : recursiveAnchor;\n        const keywordLocation = `${schemaLocation}/$recursiveRef`;\n        const result = validate(instance, recursiveAnchor === null ? schema : recursiveAnchor, draft, lookup, shortCircuit, refSchema, instanceLocation, keywordLocation, evaluated);\n        if (!result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: '$recursiveRef',\n                keywordLocation,\n                error: 'A subschema had errors.'\n            }, ...result.errors);\n        }\n    }\n    if ($ref !== undefined) {\n        const uri = __absolute_ref__ || $ref;\n        const refSchema = lookup[uri];\n        if (refSchema === undefined) {\n            let message = `Unresolved $ref \"${$ref}\".`;\n            if (__absolute_ref__ && __absolute_ref__ !== $ref) {\n                message += `  Absolute URI \"${__absolute_ref__}\".`;\n            }\n            message += `\\nKnown schemas:\\n- ${Object.keys(lookup).join('\\n- ')}`;\n            throw new Error(message);\n        }\n        const keywordLocation = `${schemaLocation}/$ref`;\n        const result = validate(instance, refSchema, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation, evaluated);\n        if (!result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: '$ref',\n                keywordLocation,\n                error: 'A subschema had errors.'\n            }, ...result.errors);\n        }\n        if (draft === '4' || draft === '7') {\n            return { valid: errors.length === 0, errors };\n        }\n    }\n    if (Array.isArray($type)) {\n        let length = $type.length;\n        let valid = false;\n        for (let i = 0; i < length; i++) {\n            if (instanceType === $type[i] ||\n                ($type[i] === 'integer' &&\n                    instanceType === 'number' &&\n                    instance % 1 === 0 &&\n                    instance === instance)) {\n                valid = true;\n                break;\n            }\n        }\n        if (!valid) {\n            errors.push({\n                instanceLocation,\n                keyword: 'type',\n                keywordLocation: `${schemaLocation}/type`,\n                error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type.join('\", \"')}\".`\n            });\n        }\n    }\n    else if ($type === 'integer') {\n        if (instanceType !== 'number' || instance % 1 || instance !== instance) {\n            errors.push({\n                instanceLocation,\n                keyword: 'type',\n                keywordLocation: `${schemaLocation}/type`,\n                error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type}\".`\n            });\n        }\n    }\n    else if ($type !== undefined && instanceType !== $type) {\n        errors.push({\n            instanceLocation,\n            keyword: 'type',\n            keywordLocation: `${schemaLocation}/type`,\n            error: `Instance type \"${instanceType}\" is invalid. Expected \"${$type}\".`\n        });\n    }\n    if ($const !== undefined) {\n        if (instanceType === 'object' || instanceType === 'array') {\n            if (!(0,_deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__.deepCompareStrict)(instance, $const)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'const',\n                    keywordLocation: `${schemaLocation}/const`,\n                    error: `Instance does not match ${JSON.stringify($const)}.`\n                });\n            }\n        }\n        else if (instance !== $const) {\n            errors.push({\n                instanceLocation,\n                keyword: 'const',\n                keywordLocation: `${schemaLocation}/const`,\n                error: `Instance does not match ${JSON.stringify($const)}.`\n            });\n        }\n    }\n    if ($enum !== undefined) {\n        if (instanceType === 'object' || instanceType === 'array') {\n            if (!$enum.some(value => (0,_deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__.deepCompareStrict)(instance, value))) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'enum',\n                    keywordLocation: `${schemaLocation}/enum`,\n                    error: `Instance does not match any of ${JSON.stringify($enum)}.`\n                });\n            }\n        }\n        else if (!$enum.some(value => instance === value)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'enum',\n                keywordLocation: `${schemaLocation}/enum`,\n                error: `Instance does not match any of ${JSON.stringify($enum)}.`\n            });\n        }\n    }\n    if ($not !== undefined) {\n        const keywordLocation = `${schemaLocation}/not`;\n        const result = validate(instance, $not, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation);\n        if (result.valid) {\n            errors.push({\n                instanceLocation,\n                keyword: 'not',\n                keywordLocation,\n                error: 'Instance matched \"not\" schema.'\n            });\n        }\n    }\n    let subEvaluateds = [];\n    if ($anyOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/anyOf`;\n        const errorsLength = errors.length;\n        let anyValid = false;\n        for (let i = 0; i < $anyOf.length; i++) {\n            const subSchema = $anyOf[i];\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            anyValid = anyValid || result.valid;\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n        }\n        if (anyValid) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'anyOf',\n                keywordLocation,\n                error: 'Instance does not match any subschemas.'\n            });\n        }\n    }\n    if ($allOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/allOf`;\n        const errorsLength = errors.length;\n        let allValid = true;\n        for (let i = 0; i < $allOf.length; i++) {\n            const subSchema = $allOf[i];\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            allValid = allValid && result.valid;\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n        }\n        if (allValid) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'allOf',\n                keywordLocation,\n                error: `Instance does not match every subschema.`\n            });\n        }\n    }\n    if ($oneOf !== undefined) {\n        const keywordLocation = `${schemaLocation}/oneOf`;\n        const errorsLength = errors.length;\n        const matches = $oneOf.filter((subSchema, i) => {\n            const subEvaluated = Object.create(evaluated);\n            const result = validate(instance, subSchema, draft, lookup, shortCircuit, $recursiveAnchor === true ? recursiveAnchor : null, instanceLocation, `${keywordLocation}/${i}`, subEvaluated);\n            errors.push(...result.errors);\n            if (result.valid) {\n                subEvaluateds.push(subEvaluated);\n            }\n            return result.valid;\n        }).length;\n        if (matches === 1) {\n            errors.length = errorsLength;\n        }\n        else {\n            errors.splice(errorsLength, 0, {\n                instanceLocation,\n                keyword: 'oneOf',\n                keywordLocation,\n                error: `Instance does not match exactly one subschema (${matches} matches).`\n            });\n        }\n    }\n    if (instanceType === 'object' || instanceType === 'array') {\n        Object.assign(evaluated, ...subEvaluateds);\n    }\n    if ($if !== undefined) {\n        const keywordLocation = `${schemaLocation}/if`;\n        const conditionResult = validate(instance, $if, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, keywordLocation, evaluated).valid;\n        if (conditionResult) {\n            if ($then !== undefined) {\n                const thenResult = validate(instance, $then, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${schemaLocation}/then`, evaluated);\n                if (!thenResult.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'if',\n                        keywordLocation,\n                        error: `Instance does not match \"then\" schema.`\n                    }, ...thenResult.errors);\n                }\n            }\n        }\n        else if ($else !== undefined) {\n            const elseResult = validate(instance, $else, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${schemaLocation}/else`, evaluated);\n            if (!elseResult.valid) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'if',\n                    keywordLocation,\n                    error: `Instance does not match \"else\" schema.`\n                }, ...elseResult.errors);\n            }\n        }\n    }\n    if (instanceType === 'object') {\n        if ($required !== undefined) {\n            for (const key of $required) {\n                if (!(key in instance)) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'required',\n                        keywordLocation: `${schemaLocation}/required`,\n                        error: `Instance does not have required property \"${key}\".`\n                    });\n                }\n            }\n        }\n        const keys = Object.keys(instance);\n        if ($minProperties !== undefined && keys.length < $minProperties) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minProperties',\n                keywordLocation: `${schemaLocation}/minProperties`,\n                error: `Instance does not have at least ${$minProperties} properties.`\n            });\n        }\n        if ($maxProperties !== undefined && keys.length > $maxProperties) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxProperties',\n                keywordLocation: `${schemaLocation}/maxProperties`,\n                error: `Instance does not have at least ${$maxProperties} properties.`\n            });\n        }\n        if ($propertyNames !== undefined) {\n            const keywordLocation = `${schemaLocation}/propertyNames`;\n            for (const key in instance) {\n                const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                const result = validate(key, $propertyNames, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                if (!result.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'propertyNames',\n                        keywordLocation,\n                        error: `Property name \"${key}\" does not match schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        if ($dependentRequired !== undefined) {\n            const keywordLocation = `${schemaLocation}/dependantRequired`;\n            for (const key in $dependentRequired) {\n                if (key in instance) {\n                    const required = $dependentRequired[key];\n                    for (const dependantKey of required) {\n                        if (!(dependantKey in instance)) {\n                            errors.push({\n                                instanceLocation,\n                                keyword: 'dependentRequired',\n                                keywordLocation,\n                                error: `Instance has \"${key}\" but does not have \"${dependantKey}\".`\n                            });\n                        }\n                    }\n                }\n            }\n        }\n        if ($dependentSchemas !== undefined) {\n            for (const key in $dependentSchemas) {\n                const keywordLocation = `${schemaLocation}/dependentSchemas`;\n                if (key in instance) {\n                    const result = validate(instance, $dependentSchemas[key], draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${keywordLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`, evaluated);\n                    if (!result.valid) {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'dependentSchemas',\n                            keywordLocation,\n                            error: `Instance has \"${key}\" but does not match dependant schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if ($dependencies !== undefined) {\n            const keywordLocation = `${schemaLocation}/dependencies`;\n            for (const key in $dependencies) {\n                if (key in instance) {\n                    const propsOrSchema = $dependencies[key];\n                    if (Array.isArray(propsOrSchema)) {\n                        for (const dependantKey of propsOrSchema) {\n                            if (!(dependantKey in instance)) {\n                                errors.push({\n                                    instanceLocation,\n                                    keyword: 'dependencies',\n                                    keywordLocation,\n                                    error: `Instance has \"${key}\" but does not have \"${dependantKey}\".`\n                                });\n                            }\n                        }\n                    }\n                    else {\n                        const result = validate(instance, propsOrSchema, draft, lookup, shortCircuit, recursiveAnchor, instanceLocation, `${keywordLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`);\n                        if (!result.valid) {\n                            errors.push({\n                                instanceLocation,\n                                keyword: 'dependencies',\n                                keywordLocation,\n                                error: `Instance has \"${key}\" but does not match dependant schema.`\n                            }, ...result.errors);\n                        }\n                    }\n                }\n            }\n        }\n        const thisEvaluated = Object.create(null);\n        let stop = false;\n        if ($properties !== undefined) {\n            const keywordLocation = `${schemaLocation}/properties`;\n            for (const key in $properties) {\n                if (!(key in instance)) {\n                    continue;\n                }\n                const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                const result = validate(instance[key], $properties[key], draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, `${keywordLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`);\n                if (result.valid) {\n                    evaluated[key] = thisEvaluated[key] = true;\n                }\n                else {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'properties',\n                        keywordLocation,\n                        error: `Property \"${key}\" does not match schema.`\n                    }, ...result.errors);\n                    if (stop)\n                        break;\n                }\n            }\n        }\n        if (!stop && $patternProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/patternProperties`;\n            for (const pattern in $patternProperties) {\n                const regex = new RegExp(pattern, 'u');\n                const subSchema = $patternProperties[pattern];\n                for (const key in instance) {\n                    if (!regex.test(key)) {\n                        continue;\n                    }\n                    const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                    const result = validate(instance[key], subSchema, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, `${keywordLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(pattern)}`);\n                    if (result.valid) {\n                        evaluated[key] = thisEvaluated[key] = true;\n                    }\n                    else {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'patternProperties',\n                            keywordLocation,\n                            error: `Property \"${key}\" matches pattern \"${pattern}\" but does not match associated schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if (!stop && $additionalProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/additionalProperties`;\n            for (const key in instance) {\n                if (thisEvaluated[key]) {\n                    continue;\n                }\n                const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                const result = validate(instance[key], $additionalProperties, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                if (result.valid) {\n                    evaluated[key] = true;\n                }\n                else {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'additionalProperties',\n                        keywordLocation,\n                        error: `Property \"${key}\" does not match additional properties schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        else if (!stop && $unevaluatedProperties !== undefined) {\n            const keywordLocation = `${schemaLocation}/unevaluatedProperties`;\n            for (const key in instance) {\n                if (!evaluated[key]) {\n                    const subInstancePointer = `${instanceLocation}/${(0,_pointer_js__WEBPACK_IMPORTED_MODULE_3__.encodePointer)(key)}`;\n                    const result = validate(instance[key], $unevaluatedProperties, draft, lookup, shortCircuit, recursiveAnchor, subInstancePointer, keywordLocation);\n                    if (result.valid) {\n                        evaluated[key] = true;\n                    }\n                    else {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'unevaluatedProperties',\n                            keywordLocation,\n                            error: `Property \"${key}\" does not match unevaluated properties schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n    }\n    else if (instanceType === 'array') {\n        if ($maxItems !== undefined && instance.length > $maxItems) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxItems',\n                keywordLocation: `${schemaLocation}/maxItems`,\n                error: `Array has too many items (${instance.length} > ${$maxItems}).`\n            });\n        }\n        if ($minItems !== undefined && instance.length < $minItems) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minItems',\n                keywordLocation: `${schemaLocation}/minItems`,\n                error: `Array has too few items (${instance.length} < ${$minItems}).`\n            });\n        }\n        const length = instance.length;\n        let i = 0;\n        let stop = false;\n        if ($prefixItems !== undefined) {\n            const keywordLocation = `${schemaLocation}/prefixItems`;\n            const length2 = Math.min($prefixItems.length, length);\n            for (; i < length2; i++) {\n                const result = validate(instance[i], $prefixItems[i], draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, `${keywordLocation}/${i}`);\n                evaluated[i] = true;\n                if (!result.valid) {\n                    stop = shortCircuit;\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'prefixItems',\n                        keywordLocation,\n                        error: `Items did not match schema.`\n                    }, ...result.errors);\n                    if (stop)\n                        break;\n                }\n            }\n        }\n        if ($items !== undefined) {\n            const keywordLocation = `${schemaLocation}/items`;\n            if (Array.isArray($items)) {\n                const length2 = Math.min($items.length, length);\n                for (; i < length2; i++) {\n                    const result = validate(instance[i], $items[i], draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, `${keywordLocation}/${i}`);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'items',\n                            keywordLocation,\n                            error: `Items did not match schema.`\n                        }, ...result.errors);\n                        if (stop)\n                            break;\n                    }\n                }\n            }\n            else {\n                for (; i < length; i++) {\n                    const result = validate(instance[i], $items, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'items',\n                            keywordLocation,\n                            error: `Items did not match schema.`\n                        }, ...result.errors);\n                        if (stop)\n                            break;\n                    }\n                }\n            }\n            if (!stop && $additionalItems !== undefined) {\n                const keywordLocation = `${schemaLocation}/additionalItems`;\n                for (; i < length; i++) {\n                    const result = validate(instance[i], $additionalItems, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                    evaluated[i] = true;\n                    if (!result.valid) {\n                        stop = shortCircuit;\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'additionalItems',\n                            keywordLocation,\n                            error: `Items did not match additional items schema.`\n                        }, ...result.errors);\n                    }\n                }\n            }\n        }\n        if ($contains !== undefined) {\n            if (length === 0 && $minContains === undefined) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'contains',\n                    keywordLocation: `${schemaLocation}/contains`,\n                    error: `Array is empty. It must contain at least one item matching the schema.`\n                });\n            }\n            else if ($minContains !== undefined && length < $minContains) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minContains',\n                    keywordLocation: `${schemaLocation}/minContains`,\n                    error: `Array has less items (${length}) than minContains (${$minContains}).`\n                });\n            }\n            else {\n                const keywordLocation = `${schemaLocation}/contains`;\n                const errorsLength = errors.length;\n                let contained = 0;\n                for (let j = 0; j < length; j++) {\n                    const result = validate(instance[j], $contains, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${j}`, keywordLocation);\n                    if (result.valid) {\n                        evaluated[j] = true;\n                        contained++;\n                    }\n                    else {\n                        errors.push(...result.errors);\n                    }\n                }\n                if (contained >= ($minContains || 0)) {\n                    errors.length = errorsLength;\n                }\n                if ($minContains === undefined &&\n                    $maxContains === undefined &&\n                    contained === 0) {\n                    errors.splice(errorsLength, 0, {\n                        instanceLocation,\n                        keyword: 'contains',\n                        keywordLocation,\n                        error: `Array does not contain item matching schema.`\n                    });\n                }\n                else if ($minContains !== undefined && contained < $minContains) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'minContains',\n                        keywordLocation: `${schemaLocation}/minContains`,\n                        error: `Array must contain at least ${$minContains} items matching schema. Only ${contained} items were found.`\n                    });\n                }\n                else if ($maxContains !== undefined && contained > $maxContains) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'maxContains',\n                        keywordLocation: `${schemaLocation}/maxContains`,\n                        error: `Array may contain at most ${$maxContains} items matching schema. ${contained} items were found.`\n                    });\n                }\n            }\n        }\n        if (!stop && $unevaluatedItems !== undefined) {\n            const keywordLocation = `${schemaLocation}/unevaluatedItems`;\n            for (i; i < length; i++) {\n                if (evaluated[i]) {\n                    continue;\n                }\n                const result = validate(instance[i], $unevaluatedItems, draft, lookup, shortCircuit, recursiveAnchor, `${instanceLocation}/${i}`, keywordLocation);\n                evaluated[i] = true;\n                if (!result.valid) {\n                    errors.push({\n                        instanceLocation,\n                        keyword: 'unevaluatedItems',\n                        keywordLocation,\n                        error: `Items did not match unevaluated items schema.`\n                    }, ...result.errors);\n                }\n            }\n        }\n        if ($uniqueItems) {\n            for (let j = 0; j < length; j++) {\n                const a = instance[j];\n                const ao = typeof a === 'object' && a !== null;\n                for (let k = 0; k < length; k++) {\n                    if (j === k) {\n                        continue;\n                    }\n                    const b = instance[k];\n                    const bo = typeof b === 'object' && b !== null;\n                    if (a === b || (ao && bo && (0,_deep_compare_strict_js__WEBPACK_IMPORTED_MODULE_0__.deepCompareStrict)(a, b))) {\n                        errors.push({\n                            instanceLocation,\n                            keyword: 'uniqueItems',\n                            keywordLocation: `${schemaLocation}/uniqueItems`,\n                            error: `Duplicate items at indexes ${j} and ${k}.`\n                        });\n                        j = Number.MAX_SAFE_INTEGER;\n                        k = Number.MAX_SAFE_INTEGER;\n                    }\n                }\n            }\n        }\n    }\n    else if (instanceType === 'number') {\n        if (draft === '4') {\n            if ($minimum !== undefined &&\n                (($exclusiveMinimum === true && instance <= $minimum) ||\n                    instance < $minimum)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minimum',\n                    keywordLocation: `${schemaLocation}/minimum`,\n                    error: `${instance} is less than ${$exclusiveMinimum ? 'or equal to ' : ''} ${$minimum}.`\n                });\n            }\n            if ($maximum !== undefined &&\n                (($exclusiveMaximum === true && instance >= $maximum) ||\n                    instance > $maximum)) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'maximum',\n                    keywordLocation: `${schemaLocation}/maximum`,\n                    error: `${instance} is greater than ${$exclusiveMaximum ? 'or equal to ' : ''} ${$maximum}.`\n                });\n            }\n        }\n        else {\n            if ($minimum !== undefined && instance < $minimum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'minimum',\n                    keywordLocation: `${schemaLocation}/minimum`,\n                    error: `${instance} is less than ${$minimum}.`\n                });\n            }\n            if ($maximum !== undefined && instance > $maximum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'maximum',\n                    keywordLocation: `${schemaLocation}/maximum`,\n                    error: `${instance} is greater than ${$maximum}.`\n                });\n            }\n            if ($exclusiveMinimum !== undefined && instance <= $exclusiveMinimum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'exclusiveMinimum',\n                    keywordLocation: `${schemaLocation}/exclusiveMinimum`,\n                    error: `${instance} is less than ${$exclusiveMinimum}.`\n                });\n            }\n            if ($exclusiveMaximum !== undefined && instance >= $exclusiveMaximum) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'exclusiveMaximum',\n                    keywordLocation: `${schemaLocation}/exclusiveMaximum`,\n                    error: `${instance} is greater than or equal to ${$exclusiveMaximum}.`\n                });\n            }\n        }\n        if ($multipleOf !== undefined) {\n            const remainder = instance % $multipleOf;\n            if (Math.abs(0 - remainder) >= 1.1920929e-7 &&\n                Math.abs($multipleOf - remainder) >= 1.1920929e-7) {\n                errors.push({\n                    instanceLocation,\n                    keyword: 'multipleOf',\n                    keywordLocation: `${schemaLocation}/multipleOf`,\n                    error: `${instance} is not a multiple of ${$multipleOf}.`\n                });\n            }\n        }\n    }\n    else if (instanceType === 'string') {\n        const length = $minLength === undefined && $maxLength === undefined\n            ? 0\n            : (0,_ucs2_length_js__WEBPACK_IMPORTED_MODULE_4__.ucs2length)(instance);\n        if ($minLength !== undefined && length < $minLength) {\n            errors.push({\n                instanceLocation,\n                keyword: 'minLength',\n                keywordLocation: `${schemaLocation}/minLength`,\n                error: `String is too short (${length} < ${$minLength}).`\n            });\n        }\n        if ($maxLength !== undefined && length > $maxLength) {\n            errors.push({\n                instanceLocation,\n                keyword: 'maxLength',\n                keywordLocation: `${schemaLocation}/maxLength`,\n                error: `String is too long (${length} > ${$maxLength}).`\n            });\n        }\n        if ($pattern !== undefined && !new RegExp($pattern, 'u').test(instance)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'pattern',\n                keywordLocation: `${schemaLocation}/pattern`,\n                error: `String does not match pattern.`\n            });\n        }\n        if ($format !== undefined &&\n            _format_js__WEBPACK_IMPORTED_MODULE_2__.format[$format] &&\n            !_format_js__WEBPACK_IMPORTED_MODULE_2__.format[$format](instance)) {\n            errors.push({\n                instanceLocation,\n                keyword: 'format',\n                keywordLocation: `${schemaLocation}/format`,\n                error: `String does not match format \"${$format}\".`\n            });\n        }\n    }\n    return { valid: errors.length === 0, errors };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validator.js":
/*!******************************************************************!*\
  !*** ./node_modules/@cfworker/json-schema/dist/esm/validator.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Validator: () => (/* binding */ Validator)\n/* harmony export */ });\n/* harmony import */ var _dereference_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./dereference.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/dereference.js\");\n/* harmony import */ var _validate_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./validate.js */ \"(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validate.js\");\n\n\nclass Validator {\n    schema;\n    draft;\n    shortCircuit;\n    lookup;\n    constructor(schema, draft = '2019-09', shortCircuit = true) {\n        this.schema = schema;\n        this.draft = draft;\n        this.shortCircuit = shortCircuit;\n        this.lookup = (0,_dereference_js__WEBPACK_IMPORTED_MODULE_0__.dereference)(schema);\n    }\n    validate(instance) {\n        return (0,_validate_js__WEBPACK_IMPORTED_MODULE_1__.validate)(instance, this.schema, this.draft, this.lookup, this.shortCircuit);\n    }\n    addSchema(schema, id) {\n        if (id) {\n            schema = { ...schema, $id: id };\n        }\n        (0,_dereference_js__WEBPACK_IMPORTED_MODULE_0__.dereference)(schema, this.lookup);\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGNmd29ya2VyL2pzb24tc2NoZW1hL2Rpc3QvZXNtL3ZhbGlkYXRvci5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBK0M7QUFDTjtBQUNsQztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0IsNERBQVc7QUFDakM7QUFDQTtBQUNBLGVBQWUsc0RBQVE7QUFDdkI7QUFDQTtBQUNBO0FBQ0EsdUJBQXVCO0FBQ3ZCO0FBQ0EsUUFBUSw0REFBVztBQUNuQjtBQUNBIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcQGNmd29ya2VyXFxqc29uLXNjaGVtYVxcZGlzdFxcZXNtXFx2YWxpZGF0b3IuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgZGVyZWZlcmVuY2UgfSBmcm9tICcuL2RlcmVmZXJlbmNlLmpzJztcbmltcG9ydCB7IHZhbGlkYXRlIH0gZnJvbSAnLi92YWxpZGF0ZS5qcyc7XG5leHBvcnQgY2xhc3MgVmFsaWRhdG9yIHtcbiAgICBzY2hlbWE7XG4gICAgZHJhZnQ7XG4gICAgc2hvcnRDaXJjdWl0O1xuICAgIGxvb2t1cDtcbiAgICBjb25zdHJ1Y3RvcihzY2hlbWEsIGRyYWZ0ID0gJzIwMTktMDknLCBzaG9ydENpcmN1aXQgPSB0cnVlKSB7XG4gICAgICAgIHRoaXMuc2NoZW1hID0gc2NoZW1hO1xuICAgICAgICB0aGlzLmRyYWZ0ID0gZHJhZnQ7XG4gICAgICAgIHRoaXMuc2hvcnRDaXJjdWl0ID0gc2hvcnRDaXJjdWl0O1xuICAgICAgICB0aGlzLmxvb2t1cCA9IGRlcmVmZXJlbmNlKHNjaGVtYSk7XG4gICAgfVxuICAgIHZhbGlkYXRlKGluc3RhbmNlKSB7XG4gICAgICAgIHJldHVybiB2YWxpZGF0ZShpbnN0YW5jZSwgdGhpcy5zY2hlbWEsIHRoaXMuZHJhZnQsIHRoaXMubG9va3VwLCB0aGlzLnNob3J0Q2lyY3VpdCk7XG4gICAgfVxuICAgIGFkZFNjaGVtYShzY2hlbWEsIGlkKSB7XG4gICAgICAgIGlmIChpZCkge1xuICAgICAgICAgICAgc2NoZW1hID0geyAuLi5zY2hlbWEsICRpZDogaWQgfTtcbiAgICAgICAgfVxuICAgICAgICBkZXJlZmVyZW5jZShzY2hlbWEsIHRoaXMubG9va3VwKTtcbiAgICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@cfworker/json-schema/dist/esm/validator.js\n");

/***/ })

};
;