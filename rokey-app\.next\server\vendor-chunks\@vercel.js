"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@vercel";
exports.ids = ["vendor-chunks/@vercel"];
exports.modules = {

/***/ "(rsc)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights)
/* harmony export */ });
/* harmony import */ var react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react-server-dom-webpack/server.edge */ "(rsc)/./node_modules/next/dist/server/route-modules/app-page/vendored/rsc/react-server-dom-webpack-server-edge.js");

const SpeedInsights = (0,react_server_dom_webpack_server_edge__WEBPACK_IMPORTED_MODULE_0__.registerClientReference)(
function() { throw new Error("Attempted to call SpeedInsights() from the server but SpeedInsights is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component."); },
"C:\\RoKey App\\rokey-app\\node_modules\\@vercel\\speed-insights\\dist\\next\\index.mjs",
"SpeedInsights",
);

/***/ }),

/***/ "(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs":
/*!*****************************************************************!*\
  !*** ./node_modules/@vercel/speed-insights/dist/next/index.mjs ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SpeedInsights: () => (/* binding */ SpeedInsights2)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var next_navigation_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation.js */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ SpeedInsights auto */ // src/nextjs/index.tsx\n\n// src/react/index.tsx\n\n// package.json\nvar name = \"@vercel/speed-insights\";\nvar version = \"1.2.0\";\n// src/queue.ts\nvar initQueue = ()=>{\n    if (window.si) return;\n    window.si = function a(...params) {\n        (window.siq = window.siq || []).push(params);\n    };\n};\n// src/utils.ts\nfunction isBrowser() {\n    return \"undefined\" !== \"undefined\";\n}\nfunction detectEnvironment() {\n    try {\n        const env = \"development\";\n        if (env === \"development\" || env === \"test\") {\n            return \"development\";\n        }\n    } catch (e) {}\n    return \"production\";\n}\nfunction isDevelopment() {\n    return detectEnvironment() === \"development\";\n}\nfunction computeRoute(pathname, pathParams) {\n    if (!pathname || !pathParams) {\n        return pathname;\n    }\n    let result = pathname;\n    try {\n        const entries = Object.entries(pathParams);\n        for (const [key, value] of entries){\n            if (!Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value);\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[${key}]`);\n                }\n            }\n        }\n        for (const [key, value] of entries){\n            if (Array.isArray(value)) {\n                const matcher = turnValueToRegExp(value.join(\"/\"));\n                if (matcher.test(result)) {\n                    result = result.replace(matcher, `/[...${key}]`);\n                }\n            }\n        }\n        return result;\n    } catch (e) {\n        return pathname;\n    }\n}\nfunction turnValueToRegExp(value) {\n    return new RegExp(`/${escapeRegExp(value)}(?=[/?#]|$)`);\n}\nfunction escapeRegExp(string) {\n    return string.replace(/[.*+?^${}()|[\\]\\\\]/g, \"\\\\$&\");\n}\nfunction getScriptSrc(props) {\n    if (props.scriptSrc) {\n        return props.scriptSrc;\n    }\n    if (isDevelopment()) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.debug.js\";\n    }\n    if (props.dsn) {\n        return \"https://va.vercel-scripts.com/v1/speed-insights/script.js\";\n    }\n    if (props.basePath) {\n        return `${props.basePath}/speed-insights/script.js`;\n    }\n    return \"/_vercel/speed-insights/script.js\";\n}\n// src/generic.ts\nfunction injectSpeedInsights(props = {}) {\n    var _a;\n    if (!isBrowser() || props.route === null) return null;\n    initQueue();\n    const src = getScriptSrc(props);\n    if (document.head.querySelector(`script[src*=\"${src}\"]`)) return null;\n    if (props.beforeSend) {\n        (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n    }\n    const script = document.createElement(\"script\");\n    script.src = src;\n    script.defer = true;\n    script.dataset.sdkn = name + (props.framework ? `/${props.framework}` : \"\");\n    script.dataset.sdkv = version;\n    if (props.sampleRate) {\n        script.dataset.sampleRate = props.sampleRate.toString();\n    }\n    if (props.route) {\n        script.dataset.route = props.route;\n    }\n    if (props.endpoint) {\n        script.dataset.endpoint = props.endpoint;\n    } else if (props.basePath) {\n        script.dataset.endpoint = `${props.basePath}/speed-insights/vitals`;\n    }\n    if (props.dsn) {\n        script.dataset.dsn = props.dsn;\n    }\n    if (isDevelopment() && props.debug === false) {\n        script.dataset.debug = \"false\";\n    }\n    script.onerror = ()=>{\n        console.log(`[Vercel Speed Insights] Failed to load script from ${src}. Please check if any content blockers are enabled and try again.`);\n    };\n    document.head.appendChild(script);\n    return {\n        setRoute: (route)=>{\n            script.dataset.route = route ?? void 0;\n        }\n    };\n}\n// src/react/utils.ts\nfunction getBasePath() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.REACT_APP_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/react/index.tsx\nfunction SpeedInsights(props) {\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SpeedInsights.useEffect\": ()=>{\n            var _a;\n            if (props.beforeSend) {\n                (_a = window.si) == null ? void 0 : _a.call(window, \"beforeSend\", props.beforeSend);\n            }\n        }\n    }[\"SpeedInsights.useEffect\"], [\n        props.beforeSend\n    ]);\n    const setScriptRoute = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"SpeedInsights.useEffect\": ()=>{\n            if (!setScriptRoute.current) {\n                const script = injectSpeedInsights({\n                    framework: props.framework ?? \"react\",\n                    basePath: props.basePath ?? getBasePath(),\n                    ...props\n                });\n                if (script) {\n                    setScriptRoute.current = script.setRoute;\n                }\n            } else if (props.route) {\n                setScriptRoute.current(props.route);\n            }\n        }\n    }[\"SpeedInsights.useEffect\"], [\n        props.route\n    ]);\n    return null;\n}\n// src/nextjs/utils.ts\n\nvar useRoute = ()=>{\n    const params = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useParams)();\n    const searchParams = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.useSearchParams)() || new URLSearchParams();\n    const path = (0,next_navigation_js__WEBPACK_IMPORTED_MODULE_1__.usePathname)();\n    if (!params) {\n        return null;\n    }\n    const finalParams = Object.keys(params).length ? params : Object.fromEntries(searchParams.entries());\n    return computeRoute(path, finalParams);\n};\nfunction getBasePath2() {\n    if (typeof process === \"undefined\" || typeof process.env === \"undefined\") {\n        return void 0;\n    }\n    return process.env.NEXT_PUBLIC_VERCEL_OBSERVABILITY_BASEPATH;\n}\n// src/nextjs/index.tsx\nfunction SpeedInsightsComponent(props) {\n    const route = useRoute();\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsights, {\n        route,\n        ...props,\n        framework: \"next\",\n        basePath: getBasePath2()\n    });\n}\nfunction SpeedInsights2(props) {\n    return /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n        fallback: null\n    }, /* @__PURE__ */ /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(SpeedInsightsComponent, {\n        ...props\n    }));\n}\n //# sourceMappingURL=index.mjs.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvQHZlcmNlbC9zcGVlZC1pbnNpZ2h0cy9kaXN0L25leHQvaW5kZXgubWpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFFZ0M7O0FDQUU7O0FDRGhDLFdBQVE7QUFDUixjQUFXOztBQ0ZOLElBQU0sWUFBWTtJQUV2QixJQUFJLE9BQU8sR0FBSTtJQUVmLE9BQU8sS0FBSyxTQUFTLEtBQUssUUFBYztRQUNyQyxRQUFPLE1BQU0sT0FBTyxPQUFPLEVBQUMsRUFBRyxLQUFLLE1BQU07SUFDN0M7QUFDRjs7QUNMTyxTQUFTLFlBQXFCO0lBQ25DLE9BQU8sT0FBTyxTQUFXO0FBQzNCO0FBRUEsU0FBUztJQUNQLElBQUk7UUFDRixNQUFNLE1BQU0sUUFBUSxJQUFJO1FBQ3hCLElBQUksUUFBUSxpQkFBaUIsUUFBUSxRQUFRO1lBQzNDLE9BQU87UUFDVDtJQUNGLFNBQVMsR0FBRyxDQUVaO0lBQ0EsT0FBTztBQUNUO0FBTU8sU0FBUyxnQkFBeUI7SUFDdkMsT0FBTyxrQkFBa0IsTUFBTTtBQUNqQztBQUVPLFNBQVMsYUFDZCxVQUNBLFlBQ2U7SUFDZixJQUFJLENBQUMsWUFBWSxDQUFDLFlBQVk7UUFDNUIsT0FBTztJQUNUO0lBRUEsSUFBSSxTQUFTO0lBQ2IsSUFBSTtRQUNGLE1BQU0sVUFBVSxPQUFPLFFBQVEsVUFBVTtRQUV6QyxXQUFXLENBQUMsS0FBSyxLQUFLLEtBQUssUUFBUztZQUNsQyxJQUFJLENBQUMsTUFBTSxRQUFRLEtBQUssR0FBRztnQkFDekIsTUFBTSxVQUFVLGtCQUFrQixLQUFLO2dCQUN2QyxJQUFJLFFBQVEsS0FBSyxNQUFNLEdBQUc7b0JBQ3hCLFNBQVMsT0FBTyxRQUFRLFNBQVMsS0FBSyxHQUFHLEdBQUc7Z0JBQzlDO1lBQ0Y7UUFDRjtRQUVBLFdBQVcsQ0FBQyxLQUFLLEtBQUssS0FBSyxRQUFTO1lBQ2xDLElBQUksTUFBTSxRQUFRLEtBQUssR0FBRztnQkFDeEIsTUFBTSxVQUFVLGtCQUFrQixNQUFNLEtBQUssR0FBRyxDQUFDO2dCQUNqRCxJQUFJLFFBQVEsS0FBSyxNQUFNLEdBQUc7b0JBQ3hCLFNBQVMsT0FBTyxRQUFRLFNBQVMsUUFBUSxHQUFHLEdBQUc7Z0JBQ2pEO1lBQ0Y7UUFDRjtRQUNBLE9BQU87SUFDVCxTQUFTLEdBQUc7UUFDVixPQUFPO0lBQ1Q7QUFDRjtBQUVBLFNBQVMsa0JBQWtCLE9BQXVCO0lBQ2hELE9BQU8sSUFBSSxPQUFPLElBQUksYUFBYSxLQUFLLENBQUMsYUFBYTtBQUN4RDtBQUVBLFNBQVMsYUFBYSxRQUF3QjtJQUM1QyxPQUFPLE9BQU8sUUFBUSx1QkFBdUIsTUFBTTtBQUNyRDtBQUVPLFNBQVMsYUFDZCxPQUNRO0lBQ1IsSUFBSSxNQUFNLFdBQVc7UUFDbkIsT0FBTyxNQUFNO0lBQ2Y7SUFDQSxJQUFJLGNBQWMsR0FBRztRQUNuQixPQUFPO0lBQ1Q7SUFDQSxJQUFJLE1BQU0sS0FBSztRQUNiLE9BQU87SUFDVDtJQUNBLElBQUksTUFBTSxVQUFVO1FBQ2xCLE9BQU8sR0FBRyxNQUFNLFFBQVE7SUFDMUI7SUFDQSxPQUFPO0FBQ1Q7O0FDdkVBLFNBQVMsb0JBQ1AsUUFHSSxDQUFDLEdBR0U7SUFyQlQ7SUF1QkUsSUFBSSxDQUFDLFVBQVUsS0FBSyxNQUFNLFVBQVUsS0FBTSxRQUFPO0lBRWpELFVBQVU7SUFFVixNQUFNLE1BQU0sYUFBYSxLQUFLO0lBRTlCLElBQUksU0FBUyxLQUFLLGNBQWMsZ0JBQWdCLEdBQUcsSUFBSSxFQUFHLFFBQU87SUFFakUsSUFBSSxNQUFNLFlBQVk7UUFDcEIsYUFBTyxPQUFQLGdDQUFZLGNBQWMsTUFBTTtJQUNsQztJQUVBLE1BQU0sU0FBUyxTQUFTLGNBQWMsUUFBUTtJQUM5QyxPQUFPLE1BQU07SUFDYixPQUFPLFFBQVE7SUFDZixPQUFPLFFBQVEsT0FDYixRQUFlLE1BQU0sWUFBWSxJQUFJLE1BQU0sU0FBUyxLQUFLO0lBQzNELE9BQU8sUUFBUSxPQUFPO0lBRXRCLElBQUksTUFBTSxZQUFZO1FBQ3BCLE9BQU8sUUFBUSxhQUFhLE1BQU0sV0FBVyxTQUFTO0lBQ3hEO0lBQ0EsSUFBSSxNQUFNLE9BQU87UUFDZixPQUFPLFFBQVEsUUFBUSxNQUFNO0lBQy9CO0lBQ0EsSUFBSSxNQUFNLFVBQVU7UUFDbEIsT0FBTyxRQUFRLFdBQVcsTUFBTTtJQUNsQyxXQUFXLE1BQU0sVUFBVTtRQUN6QixPQUFPLFFBQVEsV0FBVyxHQUFHLE1BQU0sUUFBUTtJQUM3QztJQUNBLElBQUksTUFBTSxLQUFLO1FBQ2IsT0FBTyxRQUFRLE1BQU0sTUFBTTtJQUM3QjtJQUNBLElBQUksY0FBYyxLQUFLLE1BQU0sVUFBVSxPQUFPO1FBQzVDLE9BQU8sUUFBUSxRQUFRO0lBQ3pCO0lBRUEsT0FBTyxVQUFVO1FBRWYsUUFBUSxJQUNOLHNEQUFzRCxHQUFHO0lBRTdEO0lBRUEsU0FBUyxLQUFLLFlBQVksTUFBTTtJQUVoQyxPQUFPO1FBQ0wsVUFBVSxDQUFDO1lBQ1QsT0FBTyxRQUFRLFFBQVEsU0FBUztRQUNsQztJQUNGO0FBQ0Y7O0FDMUVPLFNBQVMsY0FBa0M7SUFLaEQsSUFBSSxPQUFPLFlBQVksZUFBZSxPQUFPLFFBQVEsUUFBUSxhQUFhO1FBQ3hFLE9BQU87SUFDVDtJQUNBLE9BQU8sUUFBUSxJQUFJO0FBQ3JCOztBTEZPLFNBQVMsY0FDZCxPQUlvQjtJQUNwQixnREFBUzttQ0FBQztZQWJaO1lBY0ksSUFBSSxNQUFNLFlBQVk7Z0JBQ3BCLGFBQU8sT0FBUCxnQ0FBWSxjQUFjLE1BQU07WUFDbEM7UUFDRjtrQ0FBRztRQUFDLE1BQU0sVUFBVTtLQUFDO0lBRXJCLE1BQU0saUJBQWlCLDZDQUFNLENBQWtDLElBQUk7SUFDbkUsZ0RBQVM7bUNBQUM7WUFDUixJQUFJLENBQUMsZUFBZSxTQUFTO2dCQUMzQixNQUFNLFNBQVMsb0JBQW9CO29CQUNqQyxXQUFXLE1BQU0sYUFBYTtvQkFDOUIsVUFBVSxNQUFNLFlBQVksWUFBWTtvQkFDeEMsR0FBRztnQkFDTCxDQUFDO2dCQUNELElBQUksUUFBUTtvQkFDVixlQUFlLFVBQVUsT0FBTztnQkFDbEM7WUFDRixXQUFXLE1BQU0sT0FBTztnQkFDdEIsZUFBZSxRQUFRLE1BQU0sS0FBSztZQUNwQztRQUNGO2tDQUFHO1FBQUMsTUFBTSxLQUFLO0tBQUM7SUFFaEIsT0FBTztBQUNUOztBTWxDd0Q7QUFHakQsSUFBTSxXQUFXO0lBQ3RCLE1BQU0sU0FBUyw2REFBUyxDQUFDO0lBQ3pCLE1BQU0sZUFBZSxtRUFBZSxDQUFDLEtBQUssSUFBSSxnQkFBZ0I7SUFDOUQsTUFBTSxPQUFPLCtEQUFXLENBQUM7SUFFekIsSUFBSSxDQUFDLFFBQVE7UUFDWCxPQUFPO0lBQ1Q7SUFFQSxNQUFNLGNBQWMsT0FBTyxLQUFLLE1BQU0sRUFBRSxTQUNwQyxTQUNBLE9BQU8sWUFBWSxhQUFhLFFBQVEsQ0FBQztJQUM3QyxPQUFPLGFBQWEsTUFBTSxXQUFXO0FBQ3ZDO0FBRU8sU0FBU0EsZUFBa0M7SUFLaEQsSUFBSSxPQUFPLFlBQVksZUFBZSxPQUFPLFFBQVEsUUFBUSxhQUFhO1FBQ3hFLE9BQU87SUFDVDtJQUNBLE9BQU8sUUFBUSxJQUFJO0FBQ3JCOztBUHBCQSxTQUFTLHVCQUF1QixPQUFrQztJQUNoRSxNQUFNLFFBQVEsU0FBUztJQUV2QixPQUNFLCtFQUFDO1FBQ0M7UUFDQyxHQUFHO1FBQ0osV0FBVTtRQUNWLFVBQVVBLGFBQVk7SUFBQTtBQUc1QjtBQUVPLFNBQVNDLGVBQWMsT0FBb0I7SUFFaEQsT0FDRSwrRUFBQywyQ0FBUSxFQUFSO1FBQVMsVUFBVTtJQUFBLEdBQ2xCLCtFQUFDO1FBQXdCLEdBQUc7SUFBQSxDQUFPLENBQ3JDO0FBRUoiLCJzb3VyY2VzIjpbIkM6XFxzcmNcXG5leHRqc1xcaW5kZXgudHN4IiwiQzpcXHNyY1xccmVhY3RcXGluZGV4LnRzeCIsIkM6XFxwYWNrYWdlLmpzb24iLCJDOlxcc3JjXFxxdWV1ZS50cyIsIkM6XFxzcmNcXHV0aWxzLnRzIiwiQzpcXHNyY1xcZ2VuZXJpYy50cyIsIkM6XFxzcmNcXHJlYWN0XFx1dGlscy50cyIsIkM6XFxzcmNcXG5leHRqc1xcdXRpbHMudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QsIHsgU3VzcGVuc2UgfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyBTcGVlZEluc2lnaHRzIGFzIFNwZWVkSW5zaWdodHNTY3JpcHQgfSBmcm9tICcuLi9yZWFjdCc7XG5pbXBvcnQgdHlwZSB7IFNwZWVkSW5zaWdodHNQcm9wcyB9IGZyb20gJy4uL3R5cGVzJztcbmltcG9ydCB7IGdldEJhc2VQYXRoLCB1c2VSb3V0ZSB9IGZyb20gJy4vdXRpbHMnO1xuXG50eXBlIFByb3BzID0gT21pdDxTcGVlZEluc2lnaHRzUHJvcHMsICdyb3V0ZSc+O1xuXG5mdW5jdGlvbiBTcGVlZEluc2lnaHRzQ29tcG9uZW50KHByb3BzOiBQcm9wcyk6IFJlYWN0LlJlYWN0RWxlbWVudCB7XG4gIGNvbnN0IHJvdXRlID0gdXNlUm91dGUoKTtcblxuICByZXR1cm4gKFxuICAgIDxTcGVlZEluc2lnaHRzU2NyaXB0XG4gICAgICByb3V0ZT17cm91dGV9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgICBmcmFtZXdvcms9XCJuZXh0XCJcbiAgICAgIGJhc2VQYXRoPXtnZXRCYXNlUGF0aCgpfVxuICAgIC8+XG4gICk7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBTcGVlZEluc2lnaHRzKHByb3BzOiBQcm9wcyk6IG51bGwge1xuICAvLyBCZWNhdXNlIG9mIGluY29tcGF0aWJsZSB0eXBlcyBiZXR3ZWVuIFJlYWN0Tm9kZSBpbiBSZWFjdCAxOSBhbmQgUmVhY3QgMTggd2UgcmV0dXJuIG51bGwgKHdoaWNoIGlzIGFsc28gd2hhdCB3ZSByZW5kZXIpXG4gIHJldHVybiAoXG4gICAgPFN1c3BlbnNlIGZhbGxiYWNrPXtudWxsfT5cbiAgICAgIDxTcGVlZEluc2lnaHRzQ29tcG9uZW50IHsuLi5wcm9wc30gLz5cbiAgICA8L1N1c3BlbnNlPlxuICApIGFzIG5ldmVyO1xufVxuIiwiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgeyB1c2VFZmZlY3QsIHVzZVJlZiB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB0eXBlIHsgU3BlZWRJbnNpZ2h0c1Byb3BzIH0gZnJvbSAnLi4vdHlwZXMnO1xuaW1wb3J0IHsgY29tcHV0ZVJvdXRlLCBpbmplY3RTcGVlZEluc2lnaHRzIH0gZnJvbSAnLi4vZ2VuZXJpYyc7XG5pbXBvcnQgeyBnZXRCYXNlUGF0aCB9IGZyb20gJy4vdXRpbHMnO1xuXG5leHBvcnQgZnVuY3Rpb24gU3BlZWRJbnNpZ2h0cyhcbiAgcHJvcHM6IFNwZWVkSW5zaWdodHNQcm9wcyAmIHtcbiAgICBmcmFtZXdvcms/OiBzdHJpbmc7XG4gICAgYmFzZVBhdGg/OiBzdHJpbmc7XG4gIH0sXG4pOiBKU1guRWxlbWVudCB8IG51bGwge1xuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChwcm9wcy5iZWZvcmVTZW5kKSB7XG4gICAgICB3aW5kb3cuc2k/LignYmVmb3JlU2VuZCcsIHByb3BzLmJlZm9yZVNlbmQpO1xuICAgIH1cbiAgfSwgW3Byb3BzLmJlZm9yZVNlbmRdKTtcblxuICBjb25zdCBzZXRTY3JpcHRSb3V0ZSA9IHVzZVJlZjwoKHBhdGg6IHN0cmluZykgPT4gdm9pZCkgfCBudWxsPihudWxsKTtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIXNldFNjcmlwdFJvdXRlLmN1cnJlbnQpIHtcbiAgICAgIGNvbnN0IHNjcmlwdCA9IGluamVjdFNwZWVkSW5zaWdodHMoe1xuICAgICAgICBmcmFtZXdvcms6IHByb3BzLmZyYW1ld29yayA/PyAncmVhY3QnLFxuICAgICAgICBiYXNlUGF0aDogcHJvcHMuYmFzZVBhdGggPz8gZ2V0QmFzZVBhdGgoKSxcbiAgICAgICAgLi4ucHJvcHMsXG4gICAgICB9KTtcbiAgICAgIGlmIChzY3JpcHQpIHtcbiAgICAgICAgc2V0U2NyaXB0Um91dGUuY3VycmVudCA9IHNjcmlwdC5zZXRSb3V0ZTtcbiAgICAgIH1cbiAgICB9IGVsc2UgaWYgKHByb3BzLnJvdXRlKSB7XG4gICAgICBzZXRTY3JpcHRSb3V0ZS5jdXJyZW50KHByb3BzLnJvdXRlKTtcbiAgICB9XG4gIH0sIFtwcm9wcy5yb3V0ZV0pO1xuXG4gIHJldHVybiBudWxsO1xufVxuXG5leHBvcnQgeyBjb21wdXRlUm91dGUgfTtcbiIsIntcbiAgXCJuYW1lXCI6IFwiQHZlcmNlbC9zcGVlZC1pbnNpZ2h0c1wiLFxuICBcInZlcnNpb25cIjogXCIxLjIuMFwiLFxuICBcImRlc2NyaXB0aW9uXCI6IFwiU3BlZWQgSW5zaWdodHMgaXMgYSB0b29sIGZvciBtZWFzdXJpbmcgd2ViIHBlcmZvcm1hbmNlIGFuZCBwcm92aWRpbmcgc3VnZ2VzdGlvbnMgZm9yIGltcHJvdmVtZW50LlwiLFxuICBcImtleXdvcmRzXCI6IFtcbiAgICBcInNwZWVkLWluc2lnaHRzXCIsXG4gICAgXCJ2ZXJjZWxcIlxuICBdLFxuICBcInJlcG9zaXRvcnlcIjoge1xuICAgIFwidXJsXCI6IFwiZ2l0aHViOnZlcmNlbC9zcGVlZC1pbnNpZ2h0c1wiLFxuICAgIFwiZGlyZWN0b3J5XCI6IFwicGFja2FnZXMvd2ViXCJcbiAgfSxcbiAgXCJsaWNlbnNlXCI6IFwiQXBhY2hlLTIuMFwiLFxuICBcImV4cG9ydHNcIjoge1xuICAgIFwiLi9wYWNrYWdlLmpzb25cIjogXCIuL3BhY2thZ2UuanNvblwiLFxuICAgIFwiLlwiOiB7XG4gICAgICBcImJyb3dzZXJcIjogXCIuL2Rpc3QvaW5kZXgubWpzXCIsXG4gICAgICBcImltcG9ydFwiOiBcIi4vZGlzdC9pbmRleC5tanNcIixcbiAgICAgIFwicmVxdWlyZVwiOiBcIi4vZGlzdC9pbmRleC5qc1wiXG4gICAgfSxcbiAgICBcIi4vYXN0cm9cIjoge1xuICAgICAgXCJpbXBvcnRcIjogXCIuL2Rpc3QvYXN0cm8vY29tcG9uZW50LnRzXCJcbiAgICB9LFxuICAgIFwiLi9uZXh0XCI6IHtcbiAgICAgIFwiYnJvd3NlclwiOiBcIi4vZGlzdC9uZXh0L2luZGV4Lm1qc1wiLFxuICAgICAgXCJpbXBvcnRcIjogXCIuL2Rpc3QvbmV4dC9pbmRleC5tanNcIixcbiAgICAgIFwicmVxdWlyZVwiOiBcIi4vZGlzdC9uZXh0L2luZGV4LmpzXCJcbiAgICB9LFxuICAgIFwiLi9udXh0XCI6IHtcbiAgICAgIFwiYnJvd3NlclwiOiBcIi4vZGlzdC9udXh0L2luZGV4Lm1qc1wiLFxuICAgICAgXCJpbXBvcnRcIjogXCIuL2Rpc3QvbnV4dC9pbmRleC5tanNcIixcbiAgICAgIFwicmVxdWlyZVwiOiBcIi4vZGlzdC9udXh0L2luZGV4LmpzXCJcbiAgICB9LFxuICAgIFwiLi9yZWFjdFwiOiB7XG4gICAgICBcImJyb3dzZXJcIjogXCIuL2Rpc3QvcmVhY3QvaW5kZXgubWpzXCIsXG4gICAgICBcImltcG9ydFwiOiBcIi4vZGlzdC9yZWFjdC9pbmRleC5tanNcIixcbiAgICAgIFwicmVxdWlyZVwiOiBcIi4vZGlzdC9yZWFjdC9pbmRleC5qc1wiXG4gICAgfSxcbiAgICBcIi4vcmVtaXhcIjoge1xuICAgICAgXCJicm93c2VyXCI6IFwiLi9kaXN0L3JlbWl4L2luZGV4Lm1qc1wiLFxuICAgICAgXCJpbXBvcnRcIjogXCIuL2Rpc3QvcmVtaXgvaW5kZXgubWpzXCIsXG4gICAgICBcInJlcXVpcmVcIjogXCIuL2Rpc3QvcmVtaXgvaW5kZXguanNcIlxuICAgIH0sXG4gICAgXCIuL3N2ZWx0ZWtpdFwiOiB7XG4gICAgICBcInN2ZWx0ZVwiOiBcIi4vZGlzdC9zdmVsdGVraXQvaW5kZXgubWpzXCIsXG4gICAgICBcInR5cGVzXCI6IFwiLi9kaXN0L3N2ZWx0ZWtpdC9pbmRleC5kLnRzXCJcbiAgICB9LFxuICAgIFwiLi92dWVcIjoge1xuICAgICAgXCJicm93c2VyXCI6IFwiLi9kaXN0L3Z1ZS9pbmRleC5tanNcIixcbiAgICAgIFwiaW1wb3J0XCI6IFwiLi9kaXN0L3Z1ZS9pbmRleC5tanNcIixcbiAgICAgIFwicmVxdWlyZVwiOiBcIi4vZGlzdC92dWUvaW5kZXguanNcIlxuICAgIH1cbiAgfSxcbiAgXCJtYWluXCI6IFwiLi9kaXN0L2luZGV4LmpzXCIsXG4gIFwidHlwZXNcIjogXCIuL2Rpc3QvaW5kZXguZC50c1wiLFxuICBcInR5cGVzVmVyc2lvbnNcIjoge1xuICAgIFwiKlwiOiB7XG4gICAgICBcIipcIjogW1xuICAgICAgICBcImRpc3QvaW5kZXguZC50c1wiXG4gICAgICBdLFxuICAgICAgXCJyZWFjdFwiOiBbXG4gICAgICAgIFwiZGlzdC9yZWFjdC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcIm5leHRcIjogW1xuICAgICAgICBcImRpc3QvbmV4dC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcIm51eHRcIjogW1xuICAgICAgICBcImRpc3QvbnV4dC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcInJlbWl4XCI6IFtcbiAgICAgICAgXCJkaXN0L3JlbWl4L2luZGV4LmQudHNcIlxuICAgICAgXSxcbiAgICAgIFwic3ZlbHRla2l0XCI6IFtcbiAgICAgICAgXCJkaXN0L3N2ZWx0ZWtpdC9pbmRleC5kLnRzXCJcbiAgICAgIF0sXG4gICAgICBcInZ1ZVwiOiBbXG4gICAgICAgIFwiZGlzdC92dWUvaW5kZXguZC50c1wiXG4gICAgICBdXG4gICAgfVxuICB9LFxuICBcInNjcmlwdHNcIjoge1xuICAgIFwiYnVpbGRcIjogXCJ0c3VwICYmIHBucG0gY29weS1hc3Ryb1wiLFxuICAgIFwiY29weS1hc3Ryb1wiOiBcImNwIC1SIHNyYy9hc3RybyBkaXN0L1wiLFxuICAgIFwiZGV2XCI6IFwicG5wbSBjb3B5LWFzdHJvICYmIHRzdXAgLS13YXRjaFwiLFxuICAgIFwicG9zdGluc3RhbGxcIjogXCJub2RlIHNjcmlwdHMvcG9zdGluc3RhbGwubWpzXCIsXG4gICAgXCJsaW50XCI6IFwiZXNsaW50IC5cIixcbiAgICBcImxpbnQtZml4XCI6IFwiZXNsaW50IC4gLS1maXhcIixcbiAgICBcInRlc3RcIjogXCJ2aXRlc3RcIixcbiAgICBcInR5cGUtY2hlY2tcIjogXCJ0c2MgLS1ub0VtaXRcIlxuICB9LFxuICBcImRldkRlcGVuZGVuY2llc1wiOiB7XG4gICAgXCJAcmVtaXgtcnVuL3JlYWN0XCI6IFwiXjIuMTQuMFwiLFxuICAgIFwiQHN2ZWx0ZWpzL2tpdFwiOiBcIl4yLjguMVwiLFxuICAgIFwiQHN3Yy9jb3JlXCI6IFwiXjEuOS4yXCIsXG4gICAgXCJAdGVzdGluZy1saWJyYXJ5L2plc3QtZG9tXCI6IFwiXjYuNi4zXCIsXG4gICAgXCJAdGVzdGluZy1saWJyYXJ5L3JlYWN0XCI6IFwiXjE2LjAuMVwiLFxuICAgIFwiQHR5cGVzL25vZGVcIjogXCJeMjIuOS4xXCIsXG4gICAgXCJAdHlwZXMvcmVhY3RcIjogXCJeMTguMy4xMlwiLFxuICAgIFwiY29weWZpbGVzXCI6IFwiXjIuNC4xXCIsXG4gICAgXCJqc2RvbVwiOiBcIl4yNS4wLjFcIixcbiAgICBcIm5leHRcIjogXCJeMTQuMC40XCIsXG4gICAgXCJyZWFjdFwiOiBcIl4xOC4zLjFcIixcbiAgICBcInJlYWN0LWRvbVwiOiBcIl4xOC4zLjFcIixcbiAgICBcInN2ZWx0ZVwiOiBcIl41LjIuN1wiLFxuICAgIFwidHN1cFwiOiBcIjguMy41XCIsXG4gICAgXCJ2aXRlc3RcIjogXCJeMi4xLjVcIixcbiAgICBcInZ1ZVwiOiBcIl4zLjUuMTNcIixcbiAgICBcInZ1ZS1yb3V0ZXJcIjogXCJeNC40LjVcIlxuICB9LFxuICBcInBlZXJEZXBlbmRlbmNpZXNcIjoge1xuICAgIFwiQHN2ZWx0ZWpzL2tpdFwiOiBcIl4xIHx8IF4yXCIsXG4gICAgXCJuZXh0XCI6IFwiPj0gMTNcIixcbiAgICBcInJlYWN0XCI6IFwiXjE4IHx8IF4xOSB8fCBeMTkuMC4wLXJjXCIsXG4gICAgXCJzdmVsdGVcIjogXCI+PSA0XCIsXG4gICAgXCJ2dWVcIjogXCJeM1wiLFxuICAgIFwidnVlLXJvdXRlclwiOiBcIl40XCJcbiAgfSxcbiAgXCJwZWVyRGVwZW5kZW5jaWVzTWV0YVwiOiB7XG4gICAgXCJAc3ZlbHRlanMva2l0XCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJuZXh0XCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJyZWFjdFwiOiB7XG4gICAgICBcIm9wdGlvbmFsXCI6IHRydWVcbiAgICB9LFxuICAgIFwic3ZlbHRlXCI6IHtcbiAgICAgIFwib3B0aW9uYWxcIjogdHJ1ZVxuICAgIH0sXG4gICAgXCJ2dWVcIjoge1xuICAgICAgXCJvcHRpb25hbFwiOiB0cnVlXG4gICAgfSxcbiAgICBcInZ1ZS1yb3V0ZXJcIjoge1xuICAgICAgXCJvcHRpb25hbFwiOiB0cnVlXG4gICAgfVxuICB9XG59XG4iLCJleHBvcnQgY29uc3QgaW5pdFF1ZXVlID0gKCk6IHZvaWQgPT4ge1xuICAvLyBpbml0aWFsaXplIHZhIHVudGlsIHNjcmlwdCBpcyBsb2FkZWRcbiAgaWYgKHdpbmRvdy5zaSkgcmV0dXJuO1xuXG4gIHdpbmRvdy5zaSA9IGZ1bmN0aW9uIGEoLi4ucGFyYW1zKTogdm9pZCB7XG4gICAgKHdpbmRvdy5zaXEgPSB3aW5kb3cuc2lxIHx8IFtdKS5wdXNoKHBhcmFtcyk7XG4gIH07XG59O1xuIiwiaW1wb3J0IHR5cGUgeyBTcGVlZEluc2lnaHRzUHJvcHMgfSBmcm9tICcuL3R5cGVzJztcblxuZXhwb3J0IGZ1bmN0aW9uIGlzQnJvd3NlcigpOiBib29sZWFuIHtcbiAgcmV0dXJuIHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnO1xufVxuXG5mdW5jdGlvbiBkZXRlY3RFbnZpcm9ubWVudCgpOiAnZGV2ZWxvcG1lbnQnIHwgJ3Byb2R1Y3Rpb24nIHtcbiAgdHJ5IHtcbiAgICBjb25zdCBlbnYgPSBwcm9jZXNzLmVudi5OT0RFX0VOVjtcbiAgICBpZiAoZW52ID09PSAnZGV2ZWxvcG1lbnQnIHx8IGVudiA9PT0gJ3Rlc3QnKSB7XG4gICAgICByZXR1cm4gJ2RldmVsb3BtZW50JztcbiAgICB9XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICAvLyBkbyBub3RoaW5nLCB0aGlzIGlzIG9rYXlcbiAgfVxuICByZXR1cm4gJ3Byb2R1Y3Rpb24nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNQcm9kdWN0aW9uKCk6IGJvb2xlYW4ge1xuICByZXR1cm4gZGV0ZWN0RW52aXJvbm1lbnQoKSA9PT0gJ3Byb2R1Y3Rpb24nO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gaXNEZXZlbG9wbWVudCgpOiBib29sZWFuIHtcbiAgcmV0dXJuIGRldGVjdEVudmlyb25tZW50KCkgPT09ICdkZXZlbG9wbWVudCc7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBjb21wdXRlUm91dGUoXG4gIHBhdGhuYW1lOiBzdHJpbmcgfCBudWxsLFxuICBwYXRoUGFyYW1zOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmcgfCBzdHJpbmdbXT4gfCBudWxsLFxuKTogc3RyaW5nIHwgbnVsbCB7XG4gIGlmICghcGF0aG5hbWUgfHwgIXBhdGhQYXJhbXMpIHtcbiAgICByZXR1cm4gcGF0aG5hbWU7XG4gIH1cblxuICBsZXQgcmVzdWx0ID0gcGF0aG5hbWU7XG4gIHRyeSB7XG4gICAgY29uc3QgZW50cmllcyA9IE9iamVjdC5lbnRyaWVzKHBhdGhQYXJhbXMpO1xuICAgIC8vIHNpbXBsZSBrZXlzIG11c3QgYmUgaGFuZGxlZCBmaXJzdFxuICAgIGZvciAoY29uc3QgW2tleSwgdmFsdWVdIG9mIGVudHJpZXMpIHtcbiAgICAgIGlmICghQXJyYXkuaXNBcnJheSh2YWx1ZSkpIHtcbiAgICAgICAgY29uc3QgbWF0Y2hlciA9IHR1cm5WYWx1ZVRvUmVnRXhwKHZhbHVlKTtcbiAgICAgICAgaWYgKG1hdGNoZXIudGVzdChyZXN1bHQpKSB7XG4gICAgICAgICAgcmVzdWx0ID0gcmVzdWx0LnJlcGxhY2UobWF0Y2hlciwgYC9bJHtrZXl9XWApO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICAgIC8vIGFycmF5IHZhbHVlcyBuZXh0XG4gICAgZm9yIChjb25zdCBba2V5LCB2YWx1ZV0gb2YgZW50cmllcykge1xuICAgICAgaWYgKEFycmF5LmlzQXJyYXkodmFsdWUpKSB7XG4gICAgICAgIGNvbnN0IG1hdGNoZXIgPSB0dXJuVmFsdWVUb1JlZ0V4cCh2YWx1ZS5qb2luKCcvJykpO1xuICAgICAgICBpZiAobWF0Y2hlci50ZXN0KHJlc3VsdCkpIHtcbiAgICAgICAgICByZXN1bHQgPSByZXN1bHQucmVwbGFjZShtYXRjaGVyLCBgL1suLi4ke2tleX1dYCk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfSBjYXRjaCAoZSkge1xuICAgIHJldHVybiBwYXRobmFtZTtcbiAgfVxufVxuXG5mdW5jdGlvbiB0dXJuVmFsdWVUb1JlZ0V4cCh2YWx1ZTogc3RyaW5nKTogUmVnRXhwIHtcbiAgcmV0dXJuIG5ldyBSZWdFeHAoYC8ke2VzY2FwZVJlZ0V4cCh2YWx1ZSl9KD89Wy8/I118JClgKTtcbn1cblxuZnVuY3Rpb24gZXNjYXBlUmVnRXhwKHN0cmluZzogc3RyaW5nKTogc3RyaW5nIHtcbiAgcmV0dXJuIHN0cmluZy5yZXBsYWNlKC9bLiorP14ke30oKXxbXFxdXFxcXF0vZywgJ1xcXFwkJicpO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gZ2V0U2NyaXB0U3JjKFxuICBwcm9wczogU3BlZWRJbnNpZ2h0c1Byb3BzICYgeyBiYXNlUGF0aD86IHN0cmluZyB9LFxuKTogc3RyaW5nIHtcbiAgaWYgKHByb3BzLnNjcmlwdFNyYykge1xuICAgIHJldHVybiBwcm9wcy5zY3JpcHRTcmM7XG4gIH1cbiAgaWYgKGlzRGV2ZWxvcG1lbnQoKSkge1xuICAgIHJldHVybiAnaHR0cHM6Ly92YS52ZXJjZWwtc2NyaXB0cy5jb20vdjEvc3BlZWQtaW5zaWdodHMvc2NyaXB0LmRlYnVnLmpzJztcbiAgfVxuICBpZiAocHJvcHMuZHNuKSB7XG4gICAgcmV0dXJuICdodHRwczovL3ZhLnZlcmNlbC1zY3JpcHRzLmNvbS92MS9zcGVlZC1pbnNpZ2h0cy9zY3JpcHQuanMnO1xuICB9XG4gIGlmIChwcm9wcy5iYXNlUGF0aCkge1xuICAgIHJldHVybiBgJHtwcm9wcy5iYXNlUGF0aH0vc3BlZWQtaW5zaWdodHMvc2NyaXB0LmpzYDtcbiAgfVxuICByZXR1cm4gJy9fdmVyY2VsL3NwZWVkLWluc2lnaHRzL3NjcmlwdC5qcyc7XG59XG4iLCJpbXBvcnQgeyBuYW1lIGFzIHBhY2thZ2VOYW1lLCB2ZXJzaW9uIH0gZnJvbSAnLi4vcGFja2FnZS5qc29uJztcbmltcG9ydCB7IGluaXRRdWV1ZSB9IGZyb20gJy4vcXVldWUnO1xuaW1wb3J0IHR5cGUgeyBTcGVlZEluc2lnaHRzUHJvcHMgfSBmcm9tICcuL3R5cGVzJztcbmltcG9ydCB7IGNvbXB1dGVSb3V0ZSwgZ2V0U2NyaXB0U3JjLCBpc0Jyb3dzZXIsIGlzRGV2ZWxvcG1lbnQgfSBmcm9tICcuL3V0aWxzJztcblxuLyoqXG4gKiBJbmplY3RzIHRoZSBWZXJjZWwgU3BlZWQgSW5zaWdodHMgc2NyaXB0IGludG8gdGhlIHBhZ2UgaGVhZCBhbmQgc3RhcnRzIHRyYWNraW5nIHBhZ2Ugdmlld3MuIFJlYWQgbW9yZSBpbiBvdXIgW2RvY3VtZW50YXRpb25dKGh0dHBzOi8vdmVyY2VsLmNvbS9kb2NzL3NwZWVkLWluc2lnaHRzKS5cbiAqIEBwYXJhbSBbcHJvcHNdIC0gU3BlZWQgSW5zaWdodHMgb3B0aW9ucy5cbiAqIEBwYXJhbSBbcHJvcHMuZGVidWddIC0gV2hldGhlciB0byBlbmFibGUgZGVidWcgbG9nZ2luZyBpbiBkZXZlbG9wbWVudC4gRGVmYXVsdHMgdG8gYHRydWVgLlxuICogQHBhcmFtIFtwcm9wcy5iZWZvcmVTZW5kXSAtIEEgbWlkZGxld2FyZSBmdW5jdGlvbiB0byBtb2RpZnkgZXZlbnRzIGJlZm9yZSB0aGV5IGFyZSBzZW50LiBTaG91bGQgcmV0dXJuIHRoZSBldmVudCBvYmplY3Qgb3IgYG51bGxgIHRvIGNhbmNlbCB0aGUgZXZlbnQuXG4gKiBAcGFyYW0gW3Byb3BzLnNhbXBsZVJhdGVdIC0gV2hlbiBzZXR0aW5nIHRvIDAuNSwgNTAlIG9mIHRoZSBldmVudHMgd2lsbCBiZSBzZW50IHRvIFZlcmNlbCBTcGVlZCBJbnNpZ2h0cy4gRGVmYXVsdHMgdG8gYDFgLlxuICogQHBhcmFtIFtwcm9wcy5yb3V0ZV0gLSBUaGUgZHluYW1pYyByb3V0ZSBvZiB0aGUgcGFnZS5cbiAqIEBwYXJhbSBbcHJvcHMuZHNuXSAtIFRoZSBEU04gb2YgdGhlIHByb2plY3QgdG8gc2VuZCBldmVudHMgdG8uIE9ubHkgcmVxdWlyZWQgd2hlbiBzZWxmLWhvc3RpbmcuXG4gKi9cbmZ1bmN0aW9uIGluamVjdFNwZWVkSW5zaWdodHMoXG4gIHByb3BzOiBTcGVlZEluc2lnaHRzUHJvcHMgJiB7XG4gICAgZnJhbWV3b3JrPzogc3RyaW5nO1xuICAgIGJhc2VQYXRoPzogc3RyaW5nO1xuICB9ID0ge30sXG4pOiB7XG4gIHNldFJvdXRlOiAocm91dGU6IHN0cmluZyB8IG51bGwpID0+IHZvaWQ7XG59IHwgbnVsbCB7XG4gIC8vIFdoZW4gcm91dGUgaXMgbnVsbCwgaXQgbWVhbnMgdGhhdCBwYWdlcyByb3V0ZXIgaXMgbm90IHJlYWR5IHlldC4gV2lsbCByZXNvbHZlIHNvb25cbiAgaWYgKCFpc0Jyb3dzZXIoKSB8fCBwcm9wcy5yb3V0ZSA9PT0gbnVsbCkgcmV0dXJuIG51bGw7XG5cbiAgaW5pdFF1ZXVlKCk7XG5cbiAgY29uc3Qgc3JjID0gZ2V0U2NyaXB0U3JjKHByb3BzKTtcblxuICBpZiAoZG9jdW1lbnQuaGVhZC5xdWVyeVNlbGVjdG9yKGBzY3JpcHRbc3JjKj1cIiR7c3JjfVwiXWApKSByZXR1cm4gbnVsbDtcblxuICBpZiAocHJvcHMuYmVmb3JlU2VuZCkge1xuICAgIHdpbmRvdy5zaT8uKCdiZWZvcmVTZW5kJywgcHJvcHMuYmVmb3JlU2VuZCk7XG4gIH1cblxuICBjb25zdCBzY3JpcHQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdzY3JpcHQnKTtcbiAgc2NyaXB0LnNyYyA9IHNyYztcbiAgc2NyaXB0LmRlZmVyID0gdHJ1ZTtcbiAgc2NyaXB0LmRhdGFzZXQuc2RrbiA9XG4gICAgcGFja2FnZU5hbWUgKyAocHJvcHMuZnJhbWV3b3JrID8gYC8ke3Byb3BzLmZyYW1ld29ya31gIDogJycpO1xuICBzY3JpcHQuZGF0YXNldC5zZGt2ID0gdmVyc2lvbjtcblxuICBpZiAocHJvcHMuc2FtcGxlUmF0ZSkge1xuICAgIHNjcmlwdC5kYXRhc2V0LnNhbXBsZVJhdGUgPSBwcm9wcy5zYW1wbGVSYXRlLnRvU3RyaW5nKCk7XG4gIH1cbiAgaWYgKHByb3BzLnJvdXRlKSB7XG4gICAgc2NyaXB0LmRhdGFzZXQucm91dGUgPSBwcm9wcy5yb3V0ZTtcbiAgfVxuICBpZiAocHJvcHMuZW5kcG9pbnQpIHtcbiAgICBzY3JpcHQuZGF0YXNldC5lbmRwb2ludCA9IHByb3BzLmVuZHBvaW50O1xuICB9IGVsc2UgaWYgKHByb3BzLmJhc2VQYXRoKSB7XG4gICAgc2NyaXB0LmRhdGFzZXQuZW5kcG9pbnQgPSBgJHtwcm9wcy5iYXNlUGF0aH0vc3BlZWQtaW5zaWdodHMvdml0YWxzYDtcbiAgfVxuICBpZiAocHJvcHMuZHNuKSB7XG4gICAgc2NyaXB0LmRhdGFzZXQuZHNuID0gcHJvcHMuZHNuO1xuICB9XG4gIGlmIChpc0RldmVsb3BtZW50KCkgJiYgcHJvcHMuZGVidWcgPT09IGZhbHNlKSB7XG4gICAgc2NyaXB0LmRhdGFzZXQuZGVidWcgPSAnZmFsc2UnO1xuICB9XG5cbiAgc2NyaXB0Lm9uZXJyb3IgPSAoKTogdm9pZCA9PiB7XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLWNvbnNvbGUgLS0gTG9nZ2luZyBpcyBva2F5IGhlcmVcbiAgICBjb25zb2xlLmxvZyhcbiAgICAgIGBbVmVyY2VsIFNwZWVkIEluc2lnaHRzXSBGYWlsZWQgdG8gbG9hZCBzY3JpcHQgZnJvbSAke3NyY30uIFBsZWFzZSBjaGVjayBpZiBhbnkgY29udGVudCBibG9ja2VycyBhcmUgZW5hYmxlZCBhbmQgdHJ5IGFnYWluLmAsXG4gICAgKTtcbiAgfTtcblxuICBkb2N1bWVudC5oZWFkLmFwcGVuZENoaWxkKHNjcmlwdCk7XG5cbiAgcmV0dXJuIHtcbiAgICBzZXRSb3V0ZTogKHJvdXRlOiBzdHJpbmcgfCBudWxsKTogdm9pZCA9PiB7XG4gICAgICBzY3JpcHQuZGF0YXNldC5yb3V0ZSA9IHJvdXRlID8/IHVuZGVmaW5lZDtcbiAgICB9LFxuICB9O1xufVxuXG5leHBvcnQgeyBpbmplY3RTcGVlZEluc2lnaHRzLCBjb21wdXRlUm91dGUgfTtcbmV4cG9ydCB0eXBlIHsgU3BlZWRJbnNpZ2h0c1Byb3BzIH07XG5cbi8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBpbXBvcnQvbm8tZGVmYXVsdC1leHBvcnQgLS0gQWxsb3cgZGVmYXVsdCBleHBvcnRcbmV4cG9ydCBkZWZhdWx0IHtcbiAgaW5qZWN0U3BlZWRJbnNpZ2h0cyxcbiAgY29tcHV0ZVJvdXRlLFxufTtcbiIsImV4cG9ydCBmdW5jdGlvbiBnZXRCYXNlUGF0aCgpOiBzdHJpbmcgfCB1bmRlZmluZWQge1xuICAvLyAhISBpbXBvcnRhbnQgISFcbiAgLy8gZG8gbm90IGFjY2VzcyBlbnYgdmFyaWFibGVzIHVzaW5nIHByb2Nlc3MuZW52W3Zhcm5hbWVdXG4gIC8vIHNvbWUgYnVuZGxlcyB3b24ndCByZXBsYWNlIHRoZSB2YWx1ZSBhdCBidWlsZCB0aW1lLlxuICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgQHR5cGVzY3JpcHQtZXNsaW50L3ByZWZlci1vcHRpb25hbC1jaGFpbiAtLSB3ZSBjYW4ndCB1c2Ugb3B0aW9ubmFsIGhlcmUsIGl0J2xsIGJyZWFrIGlmIHByb2Nlc3MgZG9lcyBub3QgZXhpc3QuXG4gIGlmICh0eXBlb2YgcHJvY2VzcyA9PT0gJ3VuZGVmaW5lZCcgfHwgdHlwZW9mIHByb2Nlc3MuZW52ID09PSAndW5kZWZpbmVkJykge1xuICAgIHJldHVybiB1bmRlZmluZWQ7XG4gIH1cbiAgcmV0dXJuIHByb2Nlc3MuZW52LlJFQUNUX0FQUF9WRVJDRUxfT0JTRVJWQUJJTElUWV9CQVNFUEFUSDtcbn1cbiIsIid1c2UgY2xpZW50Jztcbi8qIGVzbGludC1kaXNhYmxlIEB0eXBlc2NyaXB0LWVzbGludC9uby11bm5lY2Vzc2FyeS1jb25kaXRpb24gLS0gY2FuIGJlIGVtcHR5IGluIHBhZ2VzIHJvdXRlciAqL1xuaW1wb3J0IHsgdXNlUGFyYW1zLCB1c2VQYXRobmFtZSwgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uLmpzJztcbmltcG9ydCB7IGNvbXB1dGVSb3V0ZSB9IGZyb20gJy4uL3V0aWxzJztcblxuZXhwb3J0IGNvbnN0IHVzZVJvdXRlID0gKCk6IHN0cmluZyB8IG51bGwgPT4ge1xuICBjb25zdCBwYXJhbXMgPSB1c2VQYXJhbXMoKTtcbiAgY29uc3Qgc2VhcmNoUGFyYW1zID0gdXNlU2VhcmNoUGFyYW1zKCkgfHwgbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICBjb25zdCBwYXRoID0gdXNlUGF0aG5hbWUoKTtcbiAgLy8gVW50aWwgd2UgaGF2ZSByb3V0ZSBwYXJhbWV0ZXJzLCB3ZSBkb24ndCBjb21wdXRlIHRoZSByb3V0ZVxuICBpZiAoIXBhcmFtcykge1xuICAgIHJldHVybiBudWxsO1xuICB9XG4gIC8vIGluIE5leHQuanNAMTMsIHVzZVBhcmFtcygpIGNvdWxkIHJldHVybiBhbiBlbXB0eSBvYmplY3QgZm9yIHBhZ2VzIHJvdXRlciwgYW5kIHdlIGRlZmF1bHQgdG8gc2VhcmNoUGFyYW1zLlxuICBjb25zdCBmaW5hbFBhcmFtcyA9IE9iamVjdC5rZXlzKHBhcmFtcykubGVuZ3RoXG4gICAgPyBwYXJhbXNcbiAgICA6IE9iamVjdC5mcm9tRW50cmllcyhzZWFyY2hQYXJhbXMuZW50cmllcygpKTtcbiAgcmV0dXJuIGNvbXB1dGVSb3V0ZShwYXRoLCBmaW5hbFBhcmFtcyk7XG59O1xuXG5leHBvcnQgZnVuY3Rpb24gZ2V0QmFzZVBhdGgoKTogc3RyaW5nIHwgdW5kZWZpbmVkIHtcbiAgLy8gISEgaW1wb3J0YW50ICEhXG4gIC8vIGRvIG5vdCBhY2Nlc3MgZW52IHZhcmlhYmxlcyB1c2luZyBwcm9jZXNzLmVudlt2YXJuYW1lXVxuICAvLyBzb21lIGJ1bmRsZXMgd29uJ3QgcmVwbGFjZSB0aGUgdmFsdWUgYXQgYnVpbGQgdGltZS5cbiAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIEB0eXBlc2NyaXB0LWVzbGludC9wcmVmZXItb3B0aW9uYWwtY2hhaW4gLS0gd2UgY2FuJ3QgdXNlIG9wdGlvbm5hbCBoZXJlLCBpdCdsbCBicmVhayBpZiBwcm9jZXNzIGRvZXMgbm90IGV4aXN0LlxuICBpZiAodHlwZW9mIHByb2Nlc3MgPT09ICd1bmRlZmluZWQnIHx8IHR5cGVvZiBwcm9jZXNzLmVudiA9PT0gJ3VuZGVmaW5lZCcpIHtcbiAgICByZXR1cm4gdW5kZWZpbmVkO1xuICB9XG4gIHJldHVybiBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19WRVJDRUxfT0JTRVJWQUJJTElUWV9CQVNFUEFUSDtcbn1cbiJdLCJuYW1lcyI6WyJnZXRCYXNlUGF0aCIsIlNwZWVkSW5zaWdodHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@vercel/speed-insights/dist/next/index.mjs\n");

/***/ })

};
;