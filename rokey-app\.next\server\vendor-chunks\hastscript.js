"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hastscript";
exports.ids = ["vendor-chunks/hastscript"];
exports.modules = {

/***/ "(ssr)/./node_modules/hastscript/factory.js":
/*!********************************************!*\
  !*** ./node_modules/hastscript/factory.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar find = __webpack_require__(/*! property-information/find */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/find.js\")\nvar normalize = __webpack_require__(/*! property-information/normalize */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js\")\nvar parseSelector = __webpack_require__(/*! hast-util-parse-selector */ \"(ssr)/./node_modules/hast-util-parse-selector/index.js\")\nvar spaces = (__webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/hastscript/node_modules/space-separated-tokens/index.js\").parse)\nvar commas = (__webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/hastscript/node_modules/comma-separated-tokens/index.js\").parse)\n\nmodule.exports = factory\n\nvar own = {}.hasOwnProperty\n\nfunction factory(schema, defaultTagName, caseSensitive) {\n  var adjust = caseSensitive ? createAdjustMap(caseSensitive) : null\n\n  return h\n\n  // Hyperscript compatible DSL for creating virtual hast trees.\n  function h(selector, properties) {\n    var node = parseSelector(selector, defaultTagName)\n    var children = Array.prototype.slice.call(arguments, 2)\n    var name = node.tagName.toLowerCase()\n    var property\n\n    node.tagName = adjust && own.call(adjust, name) ? adjust[name] : name\n\n    if (properties && isChildren(properties, node)) {\n      children.unshift(properties)\n      properties = null\n    }\n\n    if (properties) {\n      for (property in properties) {\n        addProperty(node.properties, property, properties[property])\n      }\n    }\n\n    addChild(node.children, children)\n\n    if (node.tagName === 'template') {\n      node.content = {type: 'root', children: node.children}\n      node.children = []\n    }\n\n    return node\n  }\n\n  function addProperty(properties, key, value) {\n    var info\n    var property\n    var result\n\n    // Ignore nullish and NaN values.\n    if (value === null || value === undefined || value !== value) {\n      return\n    }\n\n    info = find(schema, key)\n    property = info.property\n    result = value\n\n    // Handle list values.\n    if (typeof result === 'string') {\n      if (info.spaceSeparated) {\n        result = spaces(result)\n      } else if (info.commaSeparated) {\n        result = commas(result)\n      } else if (info.commaOrSpaceSeparated) {\n        result = spaces(commas(result).join(' '))\n      }\n    }\n\n    // Accept `object` on style.\n    if (property === 'style' && typeof value !== 'string') {\n      result = style(result)\n    }\n\n    // Class-names (which can be added both on the `selector` and here).\n    if (property === 'className' && properties.className) {\n      result = properties.className.concat(result)\n    }\n\n    properties[property] = parsePrimitives(info, property, result)\n  }\n}\n\nfunction isChildren(value, node) {\n  return (\n    typeof value === 'string' ||\n    'length' in value ||\n    isNode(node.tagName, value)\n  )\n}\n\nfunction isNode(tagName, value) {\n  var type = value.type\n\n  if (tagName === 'input' || !type || typeof type !== 'string') {\n    return false\n  }\n\n  if (typeof value.children === 'object' && 'length' in value.children) {\n    return true\n  }\n\n  type = type.toLowerCase()\n\n  if (tagName === 'button') {\n    return (\n      type !== 'menu' &&\n      type !== 'submit' &&\n      type !== 'reset' &&\n      type !== 'button'\n    )\n  }\n\n  return 'value' in value\n}\n\nfunction addChild(nodes, value) {\n  var index\n  var length\n\n  if (typeof value === 'string' || typeof value === 'number') {\n    nodes.push({type: 'text', value: String(value)})\n    return\n  }\n\n  if (typeof value === 'object' && 'length' in value) {\n    index = -1\n    length = value.length\n\n    while (++index < length) {\n      addChild(nodes, value[index])\n    }\n\n    return\n  }\n\n  if (typeof value !== 'object' || !('type' in value)) {\n    throw new Error('Expected node, nodes, or string, got `' + value + '`')\n  }\n\n  nodes.push(value)\n}\n\n// Parse a (list of) primitives.\nfunction parsePrimitives(info, name, value) {\n  var index\n  var length\n  var result\n\n  if (typeof value !== 'object' || !('length' in value)) {\n    return parsePrimitive(info, name, value)\n  }\n\n  length = value.length\n  index = -1\n  result = []\n\n  while (++index < length) {\n    result[index] = parsePrimitive(info, name, value[index])\n  }\n\n  return result\n}\n\n// Parse a single primitives.\nfunction parsePrimitive(info, name, value) {\n  var result = value\n\n  if (info.number || info.positiveNumber) {\n    if (!isNaN(result) && result !== '') {\n      result = Number(result)\n    }\n  } else if (info.boolean || info.overloadedBoolean) {\n    // Accept `boolean` and `string`.\n    if (\n      typeof result === 'string' &&\n      (result === '' || normalize(value) === normalize(name))\n    ) {\n      result = true\n    }\n  }\n\n  return result\n}\n\nfunction style(value) {\n  var result = []\n  var key\n\n  for (key in value) {\n    result.push([key, value[key]].join(': '))\n  }\n\n  return result.join('; ')\n}\n\nfunction createAdjustMap(values) {\n  var length = values.length\n  var index = -1\n  var result = {}\n  var value\n\n  while (++index < length) {\n    value = values[index]\n    result[value.toLowerCase()] = value\n  }\n\n  return result\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/factory.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/html.js":
/*!*****************************************!*\
  !*** ./node_modules/hastscript/html.js ***!
  \*****************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar schema = __webpack_require__(/*! property-information/html */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/html.js\")\nvar factory = __webpack_require__(/*! ./factory */ \"(ssr)/./node_modules/hastscript/factory.js\")\n\nvar html = factory(schema, 'div')\nhtml.displayName = 'html'\n\nmodule.exports = html\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9odG1sLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQyw0R0FBMkI7QUFDaEQsY0FBYyxtQkFBTyxDQUFDLDZEQUFXOztBQUVqQztBQUNBOztBQUVBIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcaHRtbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIHNjaGVtYSA9IHJlcXVpcmUoJ3Byb3BlcnR5LWluZm9ybWF0aW9uL2h0bWwnKVxudmFyIGZhY3RvcnkgPSByZXF1aXJlKCcuL2ZhY3RvcnknKVxuXG52YXIgaHRtbCA9IGZhY3Rvcnkoc2NoZW1hLCAnZGl2Jylcbmh0bWwuZGlzcGxheU5hbWUgPSAnaHRtbCdcblxubW9kdWxlLmV4cG9ydHMgPSBodG1sXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/index.js":
/*!******************************************!*\
  !*** ./node_modules/hastscript/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nmodule.exports = __webpack_require__(/*! ./html */ \"(ssr)/./node_modules/hastscript/html.js\")\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWiw2RkFBa0MiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2h0bWwnKVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/comma-separated-tokens/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/comma-separated-tokens/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nexports.parse = parse\nexports.stringify = stringify\n\nvar comma = ','\nvar space = ' '\nvar empty = ''\n\n// Parse comma-separated tokens to an array.\nfunction parse(value) {\n  var values = []\n  var input = String(value || empty)\n  var index = input.indexOf(comma)\n  var lastIndex = 0\n  var end = false\n  var val\n\n  while (!end) {\n    if (index === -1) {\n      index = input.length\n      end = true\n    }\n\n    val = input.slice(lastIndex, index).trim()\n\n    if (val || !end) {\n      values.push(val)\n    }\n\n    lastIndex = index + 1\n    index = input.indexOf(comma, lastIndex)\n  }\n\n  return values\n}\n\n// Compile an array to comma-separated tokens.\n// `options.padLeft` (default: `true`) pads a space left of each token, and\n// `options.padRight` (default: `false`) pads a space to the right of each token.\nfunction stringify(values, options) {\n  var settings = options || {}\n  var left = settings.padLeft === false ? empty : space\n  var right = settings.padRight ? space : empty\n\n  // Ensure the last empty entry is seen.\n  if (values[values.length - 1] === empty) {\n    values = values.concat(empty)\n  }\n\n  return values.join(right + comma + left).trim()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/comma-separated-tokens/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/find.js":
/*!***************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/find.js ***!
  \***************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar normalize = __webpack_require__(/*! ./normalize */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js\")\nvar DefinedInfo = __webpack_require__(/*! ./lib/util/defined-info */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js\")\nvar Info = __webpack_require__(/*! ./lib/util/info */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/info.js\")\n\nvar data = 'data'\n\nmodule.exports = find\n\nvar valid = /^data[-\\w.:]+$/i\nvar dash = /-[a-z]/g\nvar cap = /[A-Z]/g\n\nfunction find(schema, value) {\n  var normal = normalize(value)\n  var prop = value\n  var Type = Info\n\n  if (normal in schema.normal) {\n    return schema.property[schema.normal[normal]]\n  }\n\n  if (normal.length > 4 && normal.slice(0, 4) === data && valid.test(value)) {\n    // Attribute or property.\n    if (value.charAt(4) === '-') {\n      prop = datasetToProperty(value)\n    } else {\n      value = datasetToAttribute(value)\n    }\n\n    Type = DefinedInfo\n  }\n\n  return new Type(prop, value)\n}\n\nfunction datasetToProperty(attribute) {\n  var value = attribute.slice(5).replace(dash, camelcase)\n  return data + value.charAt(0).toUpperCase() + value.slice(1)\n}\n\nfunction datasetToAttribute(property) {\n  var value = property.slice(4)\n\n  if (dash.test(value)) {\n    return property\n  }\n\n  value = value.replace(cap, kebab)\n\n  if (value.charAt(0) !== '-') {\n    value = '-' + value\n  }\n\n  return data + value\n}\n\nfunction kebab($0) {\n  return '-' + $0.toLowerCase()\n}\n\nfunction camelcase($0) {\n  return $0.charAt(1).toUpperCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/find.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/html.js":
/*!***************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/html.js ***!
  \***************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar merge = __webpack_require__(/*! ./lib/util/merge */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/merge.js\")\nvar xlink = __webpack_require__(/*! ./lib/xlink */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xlink.js\")\nvar xml = __webpack_require__(/*! ./lib/xml */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xml.js\")\nvar xmlns = __webpack_require__(/*! ./lib/xmlns */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xmlns.js\")\nvar aria = __webpack_require__(/*! ./lib/aria */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/aria.js\")\nvar html = __webpack_require__(/*! ./lib/html */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/html.js\")\n\nmodule.exports = merge([xml, xlink, xmlns, aria, html])\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vaHRtbC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixZQUFZLG1CQUFPLENBQUMsNkdBQWtCO0FBQ3RDLFlBQVksbUJBQU8sQ0FBQyxtR0FBYTtBQUNqQyxVQUFVLG1CQUFPLENBQUMsK0ZBQVc7QUFDN0IsWUFBWSxtQkFBTyxDQUFDLG1HQUFhO0FBQ2pDLFdBQVcsbUJBQU8sQ0FBQyxpR0FBWTtBQUMvQixXQUFXLG1CQUFPLENBQUMsaUdBQVk7O0FBRS9CIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcaHRtbC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIG1lcmdlID0gcmVxdWlyZSgnLi9saWIvdXRpbC9tZXJnZScpXG52YXIgeGxpbmsgPSByZXF1aXJlKCcuL2xpYi94bGluaycpXG52YXIgeG1sID0gcmVxdWlyZSgnLi9saWIveG1sJylcbnZhciB4bWxucyA9IHJlcXVpcmUoJy4vbGliL3htbG5zJylcbnZhciBhcmlhID0gcmVxdWlyZSgnLi9saWIvYXJpYScpXG52YXIgaHRtbCA9IHJlcXVpcmUoJy4vbGliL2h0bWwnKVxuXG5tb2R1bGUuZXhwb3J0cyA9IG1lcmdlKFt4bWwsIHhsaW5rLCB4bWxucywgYXJpYSwgaHRtbF0pXG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/aria.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/aria.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar types = __webpack_require__(/*! ./util/types */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js\")\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\n\nvar booleanish = types.booleanish\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\n\nmodule.exports = create({\n  transform: ariaTransform,\n  properties: {\n    ariaActiveDescendant: null,\n    ariaAtomic: booleanish,\n    ariaAutoComplete: null,\n    ariaBusy: booleanish,\n    ariaChecked: booleanish,\n    ariaColCount: number,\n    ariaColIndex: number,\n    ariaColSpan: number,\n    ariaControls: spaceSeparated,\n    ariaCurrent: null,\n    ariaDescribedBy: spaceSeparated,\n    ariaDetails: null,\n    ariaDisabled: booleanish,\n    ariaDropEffect: spaceSeparated,\n    ariaErrorMessage: null,\n    ariaExpanded: booleanish,\n    ariaFlowTo: spaceSeparated,\n    ariaGrabbed: booleanish,\n    ariaHasPopup: null,\n    ariaHidden: booleanish,\n    ariaInvalid: null,\n    ariaKeyShortcuts: null,\n    ariaLabel: null,\n    ariaLabelledBy: spaceSeparated,\n    ariaLevel: number,\n    ariaLive: null,\n    ariaModal: booleanish,\n    ariaMultiLine: booleanish,\n    ariaMultiSelectable: booleanish,\n    ariaOrientation: null,\n    ariaOwns: spaceSeparated,\n    ariaPlaceholder: null,\n    ariaPosInSet: number,\n    ariaPressed: booleanish,\n    ariaReadOnly: booleanish,\n    ariaRelevant: null,\n    ariaRequired: booleanish,\n    ariaRoleDescription: spaceSeparated,\n    ariaRowCount: number,\n    ariaRowIndex: number,\n    ariaRowSpan: number,\n    ariaSelected: booleanish,\n    ariaSetSize: number,\n    ariaSort: null,\n    ariaValueMax: number,\n    ariaValueMin: number,\n    ariaValueNow: number,\n    ariaValueText: null,\n    role: null\n  }\n})\n\nfunction ariaTransform(_, prop) {\n  return prop === 'role' ? prop : 'aria-' + prop.slice(4).toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/aria.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/html.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/html.js ***!
  \*******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar types = __webpack_require__(/*! ./util/types */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js\")\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\nvar caseInsensitiveTransform = __webpack_require__(/*! ./util/case-insensitive-transform */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js\")\n\nvar boolean = types.boolean\nvar overloadedBoolean = types.overloadedBoolean\nvar booleanish = types.booleanish\nvar number = types.number\nvar spaceSeparated = types.spaceSeparated\nvar commaSeparated = types.commaSeparated\n\nmodule.exports = create({\n  space: 'html',\n  attributes: {\n    acceptcharset: 'accept-charset',\n    classname: 'class',\n    htmlfor: 'for',\n    httpequiv: 'http-equiv'\n  },\n  transform: caseInsensitiveTransform,\n  mustUseProperty: ['checked', 'multiple', 'muted', 'selected'],\n  properties: {\n    // Standard Properties.\n    abbr: null,\n    accept: commaSeparated,\n    acceptCharset: spaceSeparated,\n    accessKey: spaceSeparated,\n    action: null,\n    allow: null,\n    allowFullScreen: boolean,\n    allowPaymentRequest: boolean,\n    allowUserMedia: boolean,\n    alt: null,\n    as: null,\n    async: boolean,\n    autoCapitalize: null,\n    autoComplete: spaceSeparated,\n    autoFocus: boolean,\n    autoPlay: boolean,\n    capture: boolean,\n    charSet: null,\n    checked: boolean,\n    cite: null,\n    className: spaceSeparated,\n    cols: number,\n    colSpan: null,\n    content: null,\n    contentEditable: booleanish,\n    controls: boolean,\n    controlsList: spaceSeparated,\n    coords: number | commaSeparated,\n    crossOrigin: null,\n    data: null,\n    dateTime: null,\n    decoding: null,\n    default: boolean,\n    defer: boolean,\n    dir: null,\n    dirName: null,\n    disabled: boolean,\n    download: overloadedBoolean,\n    draggable: booleanish,\n    encType: null,\n    enterKeyHint: null,\n    form: null,\n    formAction: null,\n    formEncType: null,\n    formMethod: null,\n    formNoValidate: boolean,\n    formTarget: null,\n    headers: spaceSeparated,\n    height: number,\n    hidden: boolean,\n    high: number,\n    href: null,\n    hrefLang: null,\n    htmlFor: spaceSeparated,\n    httpEquiv: spaceSeparated,\n    id: null,\n    imageSizes: null,\n    imageSrcSet: commaSeparated,\n    inputMode: null,\n    integrity: null,\n    is: null,\n    isMap: boolean,\n    itemId: null,\n    itemProp: spaceSeparated,\n    itemRef: spaceSeparated,\n    itemScope: boolean,\n    itemType: spaceSeparated,\n    kind: null,\n    label: null,\n    lang: null,\n    language: null,\n    list: null,\n    loading: null,\n    loop: boolean,\n    low: number,\n    manifest: null,\n    max: null,\n    maxLength: number,\n    media: null,\n    method: null,\n    min: null,\n    minLength: number,\n    multiple: boolean,\n    muted: boolean,\n    name: null,\n    nonce: null,\n    noModule: boolean,\n    noValidate: boolean,\n    onAbort: null,\n    onAfterPrint: null,\n    onAuxClick: null,\n    onBeforePrint: null,\n    onBeforeUnload: null,\n    onBlur: null,\n    onCancel: null,\n    onCanPlay: null,\n    onCanPlayThrough: null,\n    onChange: null,\n    onClick: null,\n    onClose: null,\n    onContextMenu: null,\n    onCopy: null,\n    onCueChange: null,\n    onCut: null,\n    onDblClick: null,\n    onDrag: null,\n    onDragEnd: null,\n    onDragEnter: null,\n    onDragExit: null,\n    onDragLeave: null,\n    onDragOver: null,\n    onDragStart: null,\n    onDrop: null,\n    onDurationChange: null,\n    onEmptied: null,\n    onEnded: null,\n    onError: null,\n    onFocus: null,\n    onFormData: null,\n    onHashChange: null,\n    onInput: null,\n    onInvalid: null,\n    onKeyDown: null,\n    onKeyPress: null,\n    onKeyUp: null,\n    onLanguageChange: null,\n    onLoad: null,\n    onLoadedData: null,\n    onLoadedMetadata: null,\n    onLoadEnd: null,\n    onLoadStart: null,\n    onMessage: null,\n    onMessageError: null,\n    onMouseDown: null,\n    onMouseEnter: null,\n    onMouseLeave: null,\n    onMouseMove: null,\n    onMouseOut: null,\n    onMouseOver: null,\n    onMouseUp: null,\n    onOffline: null,\n    onOnline: null,\n    onPageHide: null,\n    onPageShow: null,\n    onPaste: null,\n    onPause: null,\n    onPlay: null,\n    onPlaying: null,\n    onPopState: null,\n    onProgress: null,\n    onRateChange: null,\n    onRejectionHandled: null,\n    onReset: null,\n    onResize: null,\n    onScroll: null,\n    onSecurityPolicyViolation: null,\n    onSeeked: null,\n    onSeeking: null,\n    onSelect: null,\n    onSlotChange: null,\n    onStalled: null,\n    onStorage: null,\n    onSubmit: null,\n    onSuspend: null,\n    onTimeUpdate: null,\n    onToggle: null,\n    onUnhandledRejection: null,\n    onUnload: null,\n    onVolumeChange: null,\n    onWaiting: null,\n    onWheel: null,\n    open: boolean,\n    optimum: number,\n    pattern: null,\n    ping: spaceSeparated,\n    placeholder: null,\n    playsInline: boolean,\n    poster: null,\n    preload: null,\n    readOnly: boolean,\n    referrerPolicy: null,\n    rel: spaceSeparated,\n    required: boolean,\n    reversed: boolean,\n    rows: number,\n    rowSpan: number,\n    sandbox: spaceSeparated,\n    scope: null,\n    scoped: boolean,\n    seamless: boolean,\n    selected: boolean,\n    shape: null,\n    size: number,\n    sizes: null,\n    slot: null,\n    span: number,\n    spellCheck: booleanish,\n    src: null,\n    srcDoc: null,\n    srcLang: null,\n    srcSet: commaSeparated,\n    start: number,\n    step: null,\n    style: null,\n    tabIndex: number,\n    target: null,\n    title: null,\n    translate: null,\n    type: null,\n    typeMustMatch: boolean,\n    useMap: null,\n    value: booleanish,\n    width: number,\n    wrap: null,\n\n    // Legacy.\n    // See: https://html.spec.whatwg.org/#other-elements,-attributes-and-apis\n    align: null, // Several. Use CSS `text-align` instead,\n    aLink: null, // `<body>`. Use CSS `a:active {color}` instead\n    archive: spaceSeparated, // `<object>`. List of URIs to archives\n    axis: null, // `<td>` and `<th>`. Use `scope` on `<th>`\n    background: null, // `<body>`. Use CSS `background-image` instead\n    bgColor: null, // `<body>` and table elements. Use CSS `background-color` instead\n    border: number, // `<table>`. Use CSS `border-width` instead,\n    borderColor: null, // `<table>`. Use CSS `border-color` instead,\n    bottomMargin: number, // `<body>`\n    cellPadding: null, // `<table>`\n    cellSpacing: null, // `<table>`\n    char: null, // Several table elements. When `align=char`, sets the character to align on\n    charOff: null, // Several table elements. When `char`, offsets the alignment\n    classId: null, // `<object>`\n    clear: null, // `<br>`. Use CSS `clear` instead\n    code: null, // `<object>`\n    codeBase: null, // `<object>`\n    codeType: null, // `<object>`\n    color: null, // `<font>` and `<hr>`. Use CSS instead\n    compact: boolean, // Lists. Use CSS to reduce space between items instead\n    declare: boolean, // `<object>`\n    event: null, // `<script>`\n    face: null, // `<font>`. Use CSS instead\n    frame: null, // `<table>`\n    frameBorder: null, // `<iframe>`. Use CSS `border` instead\n    hSpace: number, // `<img>` and `<object>`\n    leftMargin: number, // `<body>`\n    link: null, // `<body>`. Use CSS `a:link {color: *}` instead\n    longDesc: null, // `<frame>`, `<iframe>`, and `<img>`. Use an `<a>`\n    lowSrc: null, // `<img>`. Use a `<picture>`\n    marginHeight: number, // `<body>`\n    marginWidth: number, // `<body>`\n    noResize: boolean, // `<frame>`\n    noHref: boolean, // `<area>`. Use no href instead of an explicit `nohref`\n    noShade: boolean, // `<hr>`. Use background-color and height instead of borders\n    noWrap: boolean, // `<td>` and `<th>`\n    object: null, // `<applet>`\n    profile: null, // `<head>`\n    prompt: null, // `<isindex>`\n    rev: null, // `<link>`\n    rightMargin: number, // `<body>`\n    rules: null, // `<table>`\n    scheme: null, // `<meta>`\n    scrolling: booleanish, // `<frame>`. Use overflow in the child context\n    standby: null, // `<object>`\n    summary: null, // `<table>`\n    text: null, // `<body>`. Use CSS `color` instead\n    topMargin: number, // `<body>`\n    valueType: null, // `<param>`\n    version: null, // `<html>`. Use a doctype.\n    vAlign: null, // Several. Use CSS `vertical-align` instead\n    vLink: null, // `<body>`. Use CSS `a:visited {color}` instead\n    vSpace: number, // `<img>` and `<object>`\n\n    // Non-standard Properties.\n    allowTransparency: null,\n    autoCorrect: null,\n    autoSave: null,\n    disablePictureInPicture: boolean,\n    disableRemotePlayback: boolean,\n    prefix: null,\n    property: null,\n    results: number,\n    security: null,\n    unselectable: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js":
/*!**********************************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js ***!
  \**********************************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar caseSensitiveTransform = __webpack_require__(/*! ./case-sensitive-transform */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-sensitive-transform.js\")\n\nmodule.exports = caseInsensitiveTransform\n\nfunction caseInsensitiveTransform(attributes, property) {\n  return caseSensitiveTransform(attributes, property.toLowerCase())\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1pbnNlbnNpdGl2ZS10cmFuc2Zvcm0uanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosNkJBQTZCLG1CQUFPLENBQUMsMElBQTRCOztBQUVqRTs7QUFFQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHV0aWxcXGNhc2UtaW5zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgY2FzZVNlbnNpdGl2ZVRyYW5zZm9ybSA9IHJlcXVpcmUoJy4vY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtJylcblxubW9kdWxlLmV4cG9ydHMgPSBjYXNlSW5zZW5zaXRpdmVUcmFuc2Zvcm1cblxuZnVuY3Rpb24gY2FzZUluc2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIHByb3BlcnR5KSB7XG4gIHJldHVybiBjYXNlU2Vuc2l0aXZlVHJhbnNmb3JtKGF0dHJpYnV0ZXMsIHByb3BlcnR5LnRvTG93ZXJDYXNlKCkpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-sensitive-transform.js":
/*!********************************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/case-sensitive-transform.js ***!
  \********************************************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = caseSensitiveTransform\n\nfunction caseSensitiveTransform(attributes, attribute) {\n  return attribute in attributes ? attributes[attribute] : attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcY2FzZS1zZW5zaXRpdmUtdHJhbnNmb3JtLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG5tb2R1bGUuZXhwb3J0cyA9IGNhc2VTZW5zaXRpdmVUcmFuc2Zvcm1cblxuZnVuY3Rpb24gY2FzZVNlbnNpdGl2ZVRyYW5zZm9ybShhdHRyaWJ1dGVzLCBhdHRyaWJ1dGUpIHtcbiAgcmV0dXJuIGF0dHJpYnV0ZSBpbiBhdHRyaWJ1dGVzID8gYXR0cmlidXRlc1thdHRyaWJ1dGVdIDogYXR0cmlidXRlXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-sensitive-transform.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/create.js ***!
  \**************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar normalize = __webpack_require__(/*! ../../normalize */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js\")\nvar Schema = __webpack_require__(/*! ./schema */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/schema.js\")\nvar DefinedInfo = __webpack_require__(/*! ./defined-info */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js\")\n\nmodule.exports = create\n\nfunction create(definition) {\n  var space = definition.space\n  var mustUseProperty = definition.mustUseProperty || []\n  var attributes = definition.attributes || {}\n  var props = definition.properties\n  var transform = definition.transform\n  var property = {}\n  var normal = {}\n  var prop\n  var info\n\n  for (prop in props) {\n    info = new DefinedInfo(\n      prop,\n      transform(attributes, prop),\n      props[prop],\n      space\n    )\n\n    if (mustUseProperty.indexOf(prop) !== -1) {\n      info.mustUseProperty = true\n    }\n\n    property[prop] = info\n\n    normal[normalize(prop)] = prop\n    normal[normalize(info.attribute)] = prop\n  }\n\n  return new Schema(property, normal, space)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvY3JlYXRlLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGdCQUFnQixtQkFBTyxDQUFDLHVHQUFpQjtBQUN6QyxhQUFhLG1CQUFPLENBQUMsc0dBQVU7QUFDL0Isa0JBQWtCLG1CQUFPLENBQUMsa0hBQWdCOztBQUUxQzs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHV0aWxcXGNyZWF0ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIG5vcm1hbGl6ZSA9IHJlcXVpcmUoJy4uLy4uL25vcm1hbGl6ZScpXG52YXIgU2NoZW1hID0gcmVxdWlyZSgnLi9zY2hlbWEnKVxudmFyIERlZmluZWRJbmZvID0gcmVxdWlyZSgnLi9kZWZpbmVkLWluZm8nKVxuXG5tb2R1bGUuZXhwb3J0cyA9IGNyZWF0ZVxuXG5mdW5jdGlvbiBjcmVhdGUoZGVmaW5pdGlvbikge1xuICB2YXIgc3BhY2UgPSBkZWZpbml0aW9uLnNwYWNlXG4gIHZhciBtdXN0VXNlUHJvcGVydHkgPSBkZWZpbml0aW9uLm11c3RVc2VQcm9wZXJ0eSB8fCBbXVxuICB2YXIgYXR0cmlidXRlcyA9IGRlZmluaXRpb24uYXR0cmlidXRlcyB8fCB7fVxuICB2YXIgcHJvcHMgPSBkZWZpbml0aW9uLnByb3BlcnRpZXNcbiAgdmFyIHRyYW5zZm9ybSA9IGRlZmluaXRpb24udHJhbnNmb3JtXG4gIHZhciBwcm9wZXJ0eSA9IHt9XG4gIHZhciBub3JtYWwgPSB7fVxuICB2YXIgcHJvcFxuICB2YXIgaW5mb1xuXG4gIGZvciAocHJvcCBpbiBwcm9wcykge1xuICAgIGluZm8gPSBuZXcgRGVmaW5lZEluZm8oXG4gICAgICBwcm9wLFxuICAgICAgdHJhbnNmb3JtKGF0dHJpYnV0ZXMsIHByb3ApLFxuICAgICAgcHJvcHNbcHJvcF0sXG4gICAgICBzcGFjZVxuICAgIClcblxuICAgIGlmIChtdXN0VXNlUHJvcGVydHkuaW5kZXhPZihwcm9wKSAhPT0gLTEpIHtcbiAgICAgIGluZm8ubXVzdFVzZVByb3BlcnR5ID0gdHJ1ZVxuICAgIH1cblxuICAgIHByb3BlcnR5W3Byb3BdID0gaW5mb1xuXG4gICAgbm9ybWFsW25vcm1hbGl6ZShwcm9wKV0gPSBwcm9wXG4gICAgbm9ybWFsW25vcm1hbGl6ZShpbmZvLmF0dHJpYnV0ZSldID0gcHJvcFxuICB9XG5cbiAgcmV0dXJuIG5ldyBTY2hlbWEocHJvcGVydHksIG5vcm1hbCwgc3BhY2UpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js":
/*!********************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js ***!
  \********************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar Info = __webpack_require__(/*! ./info */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/info.js\")\nvar types = __webpack_require__(/*! ./types */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js\")\n\nmodule.exports = DefinedInfo\n\nDefinedInfo.prototype = new Info()\nDefinedInfo.prototype.defined = true\n\nvar checks = [\n  'boolean',\n  'booleanish',\n  'overloadedBoolean',\n  'number',\n  'commaSeparated',\n  'spaceSeparated',\n  'commaOrSpaceSeparated'\n]\nvar checksLength = checks.length\n\nfunction DefinedInfo(property, attribute, mask, space) {\n  var index = -1\n  var check\n\n  mark(this, 'space', space)\n\n  Info.call(this, property, attribute)\n\n  while (++index < checksLength) {\n    check = checks[index]\n    mark(this, check, (mask & types[check]) === types[check])\n  }\n}\n\nfunction mark(values, key, value) {\n  if (value) {\n    values[key] = value\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvZGVmaW5lZC1pbmZvLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLFdBQVcsbUJBQU8sQ0FBQyxrR0FBUTtBQUMzQixZQUFZLG1CQUFPLENBQUMsb0dBQVM7O0FBRTdCOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcZGVmaW5lZC1pbmZvLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgSW5mbyA9IHJlcXVpcmUoJy4vaW5mbycpXG52YXIgdHlwZXMgPSByZXF1aXJlKCcuL3R5cGVzJylcblxubW9kdWxlLmV4cG9ydHMgPSBEZWZpbmVkSW5mb1xuXG5EZWZpbmVkSW5mby5wcm90b3R5cGUgPSBuZXcgSW5mbygpXG5EZWZpbmVkSW5mby5wcm90b3R5cGUuZGVmaW5lZCA9IHRydWVcblxudmFyIGNoZWNrcyA9IFtcbiAgJ2Jvb2xlYW4nLFxuICAnYm9vbGVhbmlzaCcsXG4gICdvdmVybG9hZGVkQm9vbGVhbicsXG4gICdudW1iZXInLFxuICAnY29tbWFTZXBhcmF0ZWQnLFxuICAnc3BhY2VTZXBhcmF0ZWQnLFxuICAnY29tbWFPclNwYWNlU2VwYXJhdGVkJ1xuXVxudmFyIGNoZWNrc0xlbmd0aCA9IGNoZWNrcy5sZW5ndGhcblxuZnVuY3Rpb24gRGVmaW5lZEluZm8ocHJvcGVydHksIGF0dHJpYnV0ZSwgbWFzaywgc3BhY2UpIHtcbiAgdmFyIGluZGV4ID0gLTFcbiAgdmFyIGNoZWNrXG5cbiAgbWFyayh0aGlzLCAnc3BhY2UnLCBzcGFjZSlcblxuICBJbmZvLmNhbGwodGhpcywgcHJvcGVydHksIGF0dHJpYnV0ZSlcblxuICB3aGlsZSAoKytpbmRleCA8IGNoZWNrc0xlbmd0aCkge1xuICAgIGNoZWNrID0gY2hlY2tzW2luZGV4XVxuICAgIG1hcmsodGhpcywgY2hlY2ssIChtYXNrICYgdHlwZXNbY2hlY2tdKSA9PT0gdHlwZXNbY2hlY2tdKVxuICB9XG59XG5cbmZ1bmN0aW9uIG1hcmsodmFsdWVzLCBrZXksIHZhbHVlKSB7XG4gIGlmICh2YWx1ZSkge1xuICAgIHZhbHVlc1trZXldID0gdmFsdWVcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/defined-info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/info.js":
/*!************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/info.js ***!
  \************************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = Info\n\nvar proto = Info.prototype\n\nproto.space = null\nproto.attribute = null\nproto.property = null\nproto.boolean = false\nproto.booleanish = false\nproto.overloadedBoolean = false\nproto.number = false\nproto.commaSeparated = false\nproto.spaceSeparated = false\nproto.commaOrSpaceSeparated = false\nproto.mustUseProperty = false\nproto.defined = false\n\nfunction Info(property, attribute) {\n  this.property = property\n  this.attribute = attribute\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvaW5mby5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWjs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHV0aWxcXGluZm8uanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbm1vZHVsZS5leHBvcnRzID0gSW5mb1xuXG52YXIgcHJvdG8gPSBJbmZvLnByb3RvdHlwZVxuXG5wcm90by5zcGFjZSA9IG51bGxcbnByb3RvLmF0dHJpYnV0ZSA9IG51bGxcbnByb3RvLnByb3BlcnR5ID0gbnVsbFxucHJvdG8uYm9vbGVhbiA9IGZhbHNlXG5wcm90by5ib29sZWFuaXNoID0gZmFsc2VcbnByb3RvLm92ZXJsb2FkZWRCb29sZWFuID0gZmFsc2VcbnByb3RvLm51bWJlciA9IGZhbHNlXG5wcm90by5jb21tYVNlcGFyYXRlZCA9IGZhbHNlXG5wcm90by5zcGFjZVNlcGFyYXRlZCA9IGZhbHNlXG5wcm90by5jb21tYU9yU3BhY2VTZXBhcmF0ZWQgPSBmYWxzZVxucHJvdG8ubXVzdFVzZVByb3BlcnR5ID0gZmFsc2VcbnByb3RvLmRlZmluZWQgPSBmYWxzZVxuXG5mdW5jdGlvbiBJbmZvKHByb3BlcnR5LCBhdHRyaWJ1dGUpIHtcbiAgdGhpcy5wcm9wZXJ0eSA9IHByb3BlcnR5XG4gIHRoaXMuYXR0cmlidXRlID0gYXR0cmlidXRlXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/info.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/merge.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/merge.js ***!
  \*************************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar xtend = __webpack_require__(/*! xtend */ \"(ssr)/./node_modules/xtend/immutable.js\")\nvar Schema = __webpack_require__(/*! ./schema */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/schema.js\")\n\nmodule.exports = merge\n\nfunction merge(definitions) {\n  var length = definitions.length\n  var property = []\n  var normal = []\n  var index = -1\n  var info\n  var space\n\n  while (++index < length) {\n    info = definitions[index]\n    property.push(info.property)\n    normal.push(info.normal)\n    space = info.space\n  }\n\n  return new Schema(\n    xtend.apply(null, property),\n    xtend.apply(null, normal),\n    space\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvbWVyZ2UuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVosWUFBWSxtQkFBTyxDQUFDLHNEQUFPO0FBQzNCLGFBQWEsbUJBQU8sQ0FBQyxzR0FBVTs7QUFFL0I7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxcdXRpbFxcbWVyZ2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnXG5cbnZhciB4dGVuZCA9IHJlcXVpcmUoJ3h0ZW5kJylcbnZhciBTY2hlbWEgPSByZXF1aXJlKCcuL3NjaGVtYScpXG5cbm1vZHVsZS5leHBvcnRzID0gbWVyZ2VcblxuZnVuY3Rpb24gbWVyZ2UoZGVmaW5pdGlvbnMpIHtcbiAgdmFyIGxlbmd0aCA9IGRlZmluaXRpb25zLmxlbmd0aFxuICB2YXIgcHJvcGVydHkgPSBbXVxuICB2YXIgbm9ybWFsID0gW11cbiAgdmFyIGluZGV4ID0gLTFcbiAgdmFyIGluZm9cbiAgdmFyIHNwYWNlXG5cbiAgd2hpbGUgKCsraW5kZXggPCBsZW5ndGgpIHtcbiAgICBpbmZvID0gZGVmaW5pdGlvbnNbaW5kZXhdXG4gICAgcHJvcGVydHkucHVzaChpbmZvLnByb3BlcnR5KVxuICAgIG5vcm1hbC5wdXNoKGluZm8ubm9ybWFsKVxuICAgIHNwYWNlID0gaW5mby5zcGFjZVxuICB9XG5cbiAgcmV0dXJuIG5ldyBTY2hlbWEoXG4gICAgeHRlbmQuYXBwbHkobnVsbCwgcHJvcGVydHkpLFxuICAgIHh0ZW5kLmFwcGx5KG51bGwsIG5vcm1hbCksXG4gICAgc3BhY2VcbiAgKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/merge.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/schema.js":
/*!**************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/schema.js ***!
  \**************************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = Schema\n\nvar proto = Schema.prototype\n\nproto.space = null\nproto.normal = {}\nproto.property = {}\n\nfunction Schema(property, normal, space) {\n  this.property = property\n  this.normal = normal\n\n  if (space) {\n    this.space = space\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvc2NoZW1hLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHV0aWxcXHNjaGVtYS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBTY2hlbWFcblxudmFyIHByb3RvID0gU2NoZW1hLnByb3RvdHlwZVxuXG5wcm90by5zcGFjZSA9IG51bGxcbnByb3RvLm5vcm1hbCA9IHt9XG5wcm90by5wcm9wZXJ0eSA9IHt9XG5cbmZ1bmN0aW9uIFNjaGVtYShwcm9wZXJ0eSwgbm9ybWFsLCBzcGFjZSkge1xuICB0aGlzLnByb3BlcnR5ID0gcHJvcGVydHlcbiAgdGhpcy5ub3JtYWwgPSBub3JtYWxcblxuICBpZiAoc3BhY2UpIHtcbiAgICB0aGlzLnNwYWNlID0gc3BhY2VcbiAgfVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/schema.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js":
/*!*************************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/util/types.js ***!
  \*************************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nvar powers = 0\n\nexports.boolean = increment()\nexports.booleanish = increment()\nexports.overloadedBoolean = increment()\nexports.number = increment()\nexports.spaceSeparated = increment()\nexports.commaSeparated = increment()\nexports.commaOrSpaceSeparated = increment()\n\nfunction increment() {\n  return Math.pow(2, ++powers)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3V0aWwvdHlwZXMuanMiLCJtYXBwaW5ncyI6IkFBQVk7O0FBRVo7O0FBRUEsZUFBZTtBQUNmLGtCQUFrQjtBQUNsQix5QkFBeUI7QUFDekIsY0FBYztBQUNkLHNCQUFzQjtBQUN0QixzQkFBc0I7QUFDdEIsNkJBQTZCOztBQUU3QjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHV0aWxcXHR5cGVzLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgcG93ZXJzID0gMFxuXG5leHBvcnRzLmJvb2xlYW4gPSBpbmNyZW1lbnQoKVxuZXhwb3J0cy5ib29sZWFuaXNoID0gaW5jcmVtZW50KClcbmV4cG9ydHMub3ZlcmxvYWRlZEJvb2xlYW4gPSBpbmNyZW1lbnQoKVxuZXhwb3J0cy5udW1iZXIgPSBpbmNyZW1lbnQoKVxuZXhwb3J0cy5zcGFjZVNlcGFyYXRlZCA9IGluY3JlbWVudCgpXG5leHBvcnRzLmNvbW1hU2VwYXJhdGVkID0gaW5jcmVtZW50KClcbmV4cG9ydHMuY29tbWFPclNwYWNlU2VwYXJhdGVkID0gaW5jcmVtZW50KClcblxuZnVuY3Rpb24gaW5jcmVtZW50KCkge1xuICByZXR1cm4gTWF0aC5wb3coMiwgKytwb3dlcnMpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xlink.js":
/*!********************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/xlink.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\n\nmodule.exports = create({\n  space: 'xlink',\n  transform: xlinkTransform,\n  properties: {\n    xLinkActuate: null,\n    xLinkArcRole: null,\n    xLinkHref: null,\n    xLinkRole: null,\n    xLinkShow: null,\n    xLinkTitle: null,\n    xLinkType: null\n  }\n})\n\nfunction xlinkTransform(_, prop) {\n  return 'xlink:' + prop.slice(5).toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3hsaW5rLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQywyR0FBZTs7QUFFcEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsQ0FBQzs7QUFFRDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxoYXN0c2NyaXB0XFxub2RlX21vZHVsZXNcXHByb3BlcnR5LWluZm9ybWF0aW9uXFxsaWJcXHhsaW5rLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgY3JlYXRlID0gcmVxdWlyZSgnLi91dGlsL2NyZWF0ZScpXG5cbm1vZHVsZS5leHBvcnRzID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bGluaycsXG4gIHRyYW5zZm9ybTogeGxpbmtUcmFuc2Zvcm0sXG4gIHByb3BlcnRpZXM6IHtcbiAgICB4TGlua0FjdHVhdGU6IG51bGwsXG4gICAgeExpbmtBcmNSb2xlOiBudWxsLFxuICAgIHhMaW5rSHJlZjogbnVsbCxcbiAgICB4TGlua1JvbGU6IG51bGwsXG4gICAgeExpbmtTaG93OiBudWxsLFxuICAgIHhMaW5rVGl0bGU6IG51bGwsXG4gICAgeExpbmtUeXBlOiBudWxsXG4gIH1cbn0pXG5cbmZ1bmN0aW9uIHhsaW5rVHJhbnNmb3JtKF8sIHByb3ApIHtcbiAgcmV0dXJuICd4bGluazonICsgcHJvcC5zbGljZSg1KS50b0xvd2VyQ2FzZSgpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xlink.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xml.js":
/*!******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/xml.js ***!
  \******************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\n\nmodule.exports = create({\n  space: 'xml',\n  transform: xmlTransform,\n  properties: {\n    xmlLang: null,\n    xmlBase: null,\n    xmlSpace: null\n  }\n})\n\nfunction xmlTransform(_, prop) {\n  return 'xml:' + prop.slice(3).toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixhQUFhLG1CQUFPLENBQUMsMkdBQWU7O0FBRXBDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDOztBQUVEO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXGxpYlxceG1sLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0J1xuXG52YXIgY3JlYXRlID0gcmVxdWlyZSgnLi91dGlsL2NyZWF0ZScpXG5cbm1vZHVsZS5leHBvcnRzID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bWwnLFxuICB0cmFuc2Zvcm06IHhtbFRyYW5zZm9ybSxcbiAgcHJvcGVydGllczoge1xuICAgIHhtbExhbmc6IG51bGwsXG4gICAgeG1sQmFzZTogbnVsbCxcbiAgICB4bWxTcGFjZTogbnVsbFxuICB9XG59KVxuXG5mdW5jdGlvbiB4bWxUcmFuc2Zvcm0oXywgcHJvcCkge1xuICByZXR1cm4gJ3htbDonICsgcHJvcC5zbGljZSgzKS50b0xvd2VyQ2FzZSgpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xml.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xmlns.js":
/*!********************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/lib/xmlns.js ***!
  \********************************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar create = __webpack_require__(/*! ./util/create */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/create.js\")\nvar caseInsensitiveTransform = __webpack_require__(/*! ./util/case-insensitive-transform */ \"(ssr)/./node_modules/hastscript/node_modules/property-information/lib/util/case-insensitive-transform.js\")\n\nmodule.exports = create({\n  space: 'xmlns',\n  attributes: {\n    xmlnsxlink: 'xmlns:xlink'\n  },\n  transform: caseInsensitiveTransform,\n  properties: {\n    xmlns: null,\n    xmlnsXLink: null\n  }\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbGliL3htbG5zLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaLGFBQWEsbUJBQU8sQ0FBQywyR0FBZTtBQUNwQywrQkFBK0IsbUJBQU8sQ0FBQyxtSkFBbUM7O0FBRTFFO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbm9kZV9tb2R1bGVzXFxwcm9wZXJ0eS1pbmZvcm1hdGlvblxcbGliXFx4bWxucy5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxudmFyIGNyZWF0ZSA9IHJlcXVpcmUoJy4vdXRpbC9jcmVhdGUnKVxudmFyIGNhc2VJbnNlbnNpdGl2ZVRyYW5zZm9ybSA9IHJlcXVpcmUoJy4vdXRpbC9jYXNlLWluc2Vuc2l0aXZlLXRyYW5zZm9ybScpXG5cbm1vZHVsZS5leHBvcnRzID0gY3JlYXRlKHtcbiAgc3BhY2U6ICd4bWxucycsXG4gIGF0dHJpYnV0ZXM6IHtcbiAgICB4bWxuc3hsaW5rOiAneG1sbnM6eGxpbmsnXG4gIH0sXG4gIHRyYW5zZm9ybTogY2FzZUluc2Vuc2l0aXZlVHJhbnNmb3JtLFxuICBwcm9wZXJ0aWVzOiB7XG4gICAgeG1sbnM6IG51bGwsXG4gICAgeG1sbnNYTGluazogbnVsbFxuICB9XG59KVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/lib/xmlns.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js":
/*!********************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/property-information/normalize.js ***!
  \********************************************************************************/
/***/ ((module) => {

eval("\n\nmodule.exports = normalize\n\nfunction normalize(value) {\n  return value.toLowerCase()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvcHJvcGVydHktaW5mb3JtYXRpb24vbm9ybWFsaXplLmpzIiwibWFwcGluZ3MiOiJBQUFZOztBQUVaOztBQUVBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxub2RlX21vZHVsZXNcXGhhc3RzY3JpcHRcXG5vZGVfbW9kdWxlc1xccHJvcGVydHktaW5mb3JtYXRpb25cXG5vcm1hbGl6ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxubW9kdWxlLmV4cG9ydHMgPSBub3JtYWxpemVcblxuZnVuY3Rpb24gbm9ybWFsaXplKHZhbHVlKSB7XG4gIHJldHVybiB2YWx1ZS50b0xvd2VyQ2FzZSgpXG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/property-information/normalize.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hastscript/node_modules/space-separated-tokens/index.js":
/*!******************************************************************************!*\
  !*** ./node_modules/hastscript/node_modules/space-separated-tokens/index.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\n\nexports.parse = parse\nexports.stringify = stringify\n\nvar empty = ''\nvar space = ' '\nvar whiteSpace = /[ \\t\\n\\r\\f]+/g\n\nfunction parse(value) {\n  var input = String(value || empty).trim()\n  return input === empty ? [] : input.split(whiteSpace)\n}\n\nfunction stringify(values) {\n  return values.join(space).trim()\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdHNjcmlwdC9ub2RlX21vZHVsZXMvc3BhY2Utc2VwYXJhdGVkLXRva2Vucy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBWTs7QUFFWixhQUFhO0FBQ2IsaUJBQWlCOztBQUVqQjtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyJDOlxcUm9LZXkgQXBwXFxyb2tleS1hcHBcXG5vZGVfbW9kdWxlc1xcaGFzdHNjcmlwdFxcbm9kZV9tb2R1bGVzXFxzcGFjZS1zZXBhcmF0ZWQtdG9rZW5zXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCdcblxuZXhwb3J0cy5wYXJzZSA9IHBhcnNlXG5leHBvcnRzLnN0cmluZ2lmeSA9IHN0cmluZ2lmeVxuXG52YXIgZW1wdHkgPSAnJ1xudmFyIHNwYWNlID0gJyAnXG52YXIgd2hpdGVTcGFjZSA9IC9bIFxcdFxcblxcclxcZl0rL2dcblxuZnVuY3Rpb24gcGFyc2UodmFsdWUpIHtcbiAgdmFyIGlucHV0ID0gU3RyaW5nKHZhbHVlIHx8IGVtcHR5KS50cmltKClcbiAgcmV0dXJuIGlucHV0ID09PSBlbXB0eSA/IFtdIDogaW5wdXQuc3BsaXQod2hpdGVTcGFjZSlcbn1cblxuZnVuY3Rpb24gc3RyaW5naWZ5KHZhbHVlcykge1xuICByZXR1cm4gdmFsdWVzLmpvaW4oc3BhY2UpLnRyaW0oKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hastscript/node_modules/space-separated-tokens/index.js\n");

/***/ })

};
;