"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-extension-gfm-table";
exports.ids = ["vendor-chunks/micromark-extension-gfm-table"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js":
/*!************************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   EditMap: () => (/* binding */ EditMap)\n/* harmony export */ });\n/**\n * @import {Event} from 'micromark-util-types'\n */\n\n// Port of `edit_map.rs` from `markdown-rs`.\n// This should move to `markdown-js` later.\n\n// Deal with several changes in events, batching them together.\n//\n// Preferably, changes should be kept to a minimum.\n// Sometimes, it’s needed to change the list of events, because parsing can be\n// messy, and it helps to expose a cleaner interface of events to the compiler\n// and other users.\n// It can also help to merge many adjacent similar events.\n// And, in other cases, it’s needed to parse subcontent: pass some events\n// through another tokenizer and inject the result.\n\n/**\n * @typedef {[number, number, Array<Event>]} Change\n * @typedef {[number, number, number]} Jump\n */\n\n/**\n * Tracks a bunch of edits.\n */\nclass EditMap {\n  /**\n   * Create a new edit map.\n   */\n  constructor() {\n    /**\n     * Record of changes.\n     *\n     * @type {Array<Change>}\n     */\n    this.map = []\n  }\n\n  /**\n   * Create an edit: a remove and/or add at a certain place.\n   *\n   * @param {number} index\n   * @param {number} remove\n   * @param {Array<Event>} add\n   * @returns {undefined}\n   */\n  add(index, remove, add) {\n    addImplementation(this, index, remove, add)\n  }\n\n  // To do: add this when moving to `micromark`.\n  // /**\n  //  * Create an edit: but insert `add` before existing additions.\n  //  *\n  //  * @param {number} index\n  //  * @param {number} remove\n  //  * @param {Array<Event>} add\n  //  * @returns {undefined}\n  //  */\n  // addBefore(index, remove, add) {\n  //   addImplementation(this, index, remove, add, true)\n  // }\n\n  /**\n   * Done, change the events.\n   *\n   * @param {Array<Event>} events\n   * @returns {undefined}\n   */\n  consume(events) {\n    this.map.sort(function (a, b) {\n      return a[0] - b[0]\n    })\n\n    /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n    if (this.map.length === 0) {\n      return\n    }\n\n    // To do: if links are added in events, like they are in `markdown-rs`,\n    // this is needed.\n    // // Calculate jumps: where items in the current list move to.\n    // /** @type {Array<Jump>} */\n    // const jumps = []\n    // let index = 0\n    // let addAcc = 0\n    // let removeAcc = 0\n    // while (index < this.map.length) {\n    //   const [at, remove, add] = this.map[index]\n    //   removeAcc += remove\n    //   addAcc += add.length\n    //   jumps.push([at, removeAcc, addAcc])\n    //   index += 1\n    // }\n    //\n    // . shiftLinks(events, jumps)\n\n    let index = this.map.length\n    /** @type {Array<Array<Event>>} */\n    const vecs = []\n    while (index > 0) {\n      index -= 1\n      vecs.push(\n        events.slice(this.map[index][0] + this.map[index][1]),\n        this.map[index][2]\n      )\n\n      // Truncate rest.\n      events.length = this.map[index][0]\n    }\n\n    vecs.push(events.slice())\n    events.length = 0\n\n    let slice = vecs.pop()\n\n    while (slice) {\n      for (const element of slice) {\n        events.push(element)\n      }\n\n      slice = vecs.pop()\n    }\n\n    // Truncate everything.\n    this.map.length = 0\n  }\n}\n\n/**\n * Create an edit.\n *\n * @param {EditMap} editMap\n * @param {number} at\n * @param {number} remove\n * @param {Array<Event>} add\n * @returns {undefined}\n */\nfunction addImplementation(editMap, at, remove, add) {\n  let index = 0\n\n  /* c8 ignore next 3 -- `resolve` is never called without tables, so without edits. */\n  if (remove === 0 && add.length === 0) {\n    return\n  }\n\n  while (index < editMap.map.length) {\n    if (editMap.map[index][0] === at) {\n      editMap.map[index][1] += remove\n\n      // To do: before not used by tables, use when moving to micromark.\n      // if (before) {\n      //   add.push(...editMap.map[index][2])\n      //   editMap.map[index][2] = add\n      // } else {\n      editMap.map[index][2].push(...add)\n      // }\n\n      return\n    }\n\n    index += 1\n  }\n\n  editMap.map.push([at, remove, add])\n}\n\n// /**\n//  * Shift `previous` and `next` links according to `jumps`.\n//  *\n//  * This fixes links in case there are events removed or added between them.\n//  *\n//  * @param {Array<Event>} events\n//  * @param {Array<Jump>} jumps\n//  */\n// function shiftLinks(events, jumps) {\n//   let jumpIndex = 0\n//   let index = 0\n//   let add = 0\n//   let rm = 0\n\n//   while (index < events.length) {\n//     const rmCurr = rm\n\n//     while (jumpIndex < jumps.length && jumps[jumpIndex][0] <= index) {\n//       add = jumps[jumpIndex][2]\n//       rm = jumps[jumpIndex][1]\n//       jumpIndex += 1\n//     }\n\n//     // Ignore items that will be removed.\n//     if (rm > rmCurr) {\n//       index += rm - rmCurr\n//     } else {\n//       // ?\n//       // if let Some(link) = &events[index].link {\n//       //     if let Some(next) = link.next {\n//       //         events[next].link.as_mut().unwrap().previous = Some(index + add - rm);\n//       //         while jumpIndex < jumps.len() && jumps[jumpIndex].0 <= next {\n//       //             add = jumps[jumpIndex].2;\n//       //             rm = jumps[jumpIndex].1;\n//       //             jumpIndex += 1;\n//       //         }\n//       //         events[index].link.as_mut().unwrap().next = Some(next + add - rm);\n//       //         index = next;\n//       //         continue;\n//       //     }\n//       // }\n//       index += 1\n//     }\n//   }\n// }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js":
/*!********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/html.js ***!
  \********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableHtml: () => (/* binding */ gfmTableHtml)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {HtmlExtension} from 'micromark-util-types'\n */\n\n\n\nconst alignment = {\n  none: '',\n  left: ' align=\"left\"',\n  right: ' align=\"right\"',\n  center: ' align=\"center\"'\n}\n\n// To do: micromark@5: use `infer` here, when all events are exposed.\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables when\n * serializing to HTML.\n *\n * @returns {HtmlExtension}\n *   Extension for `micromark` that can be passed in `htmlExtensions` to\n *   support GitHub tables when serializing to HTML.\n */\nfunction gfmTableHtml() {\n  return {\n    enter: {\n      table(token) {\n        const tableAlign = token._align\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `_align`')\n        this.lineEndingIfNeeded()\n        this.tag('<table>')\n        this.setData('tableAlign', tableAlign)\n      },\n      tableBody() {\n        this.tag('<tbody>')\n      },\n      tableData() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n        const align = alignment[tableAlign[tableColumn]]\n\n        if (align === undefined) {\n          // Capture results to ignore them.\n          this.buffer()\n        } else {\n          this.lineEndingIfNeeded()\n          this.tag('<td' + align + '>')\n        }\n      },\n      tableHead() {\n        this.lineEndingIfNeeded()\n        this.tag('<thead>')\n      },\n      tableHeader() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n        const align = alignment[tableAlign[tableColumn]]\n        this.lineEndingIfNeeded()\n        this.tag('<th' + align + '>')\n      },\n      tableRow() {\n        this.setData('tableColumn', 0)\n        this.lineEndingIfNeeded()\n        this.tag('<tr>')\n      }\n    },\n    exit: {\n      // Overwrite the default code text data handler to unescape escaped pipes when\n      // they are in tables.\n      codeTextData(token) {\n        let value = this.sliceSerialize(token)\n\n        if (this.getData('tableAlign')) {\n          value = value.replace(/\\\\([\\\\|])/g, replace)\n        }\n\n        this.raw(this.encode(value))\n      },\n      table() {\n        this.setData('tableAlign')\n        // Note: we don’t set `slurpAllLineEndings` anymore, in delimiter rows,\n        // but we do need to reset it to match a funky newline GH generates for\n        // list items combined with tables.\n        this.setData('slurpAllLineEndings')\n        this.lineEndingIfNeeded()\n        this.tag('</table>')\n      },\n      tableBody() {\n        this.lineEndingIfNeeded()\n        this.tag('</tbody>')\n      },\n      tableData() {\n        const tableAlign = this.getData('tableAlign')\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n        if (tableColumn in tableAlign) {\n          this.tag('</td>')\n          this.setData('tableColumn', tableColumn + 1)\n        } else {\n          // Stop capturing.\n          this.resume()\n        }\n      },\n      tableHead() {\n        this.lineEndingIfNeeded()\n        this.tag('</thead>')\n      },\n      tableHeader() {\n        const tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n        this.tag('</th>')\n        this.setData('tableColumn', tableColumn + 1)\n      },\n      tableRow() {\n        const tableAlign = this.getData('tableAlign')\n        let tableColumn = this.getData('tableColumn')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(tableAlign, 'expected `tableAlign`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(typeof tableColumn === 'number', 'expected `tableColumn`')\n\n        while (tableColumn < tableAlign.length) {\n          this.lineEndingIfNeeded()\n          this.tag('<td' + alignment[tableAlign[tableColumn]] + '></td>')\n          tableColumn++\n        }\n\n        this.setData('tableColumn', tableColumn)\n        this.lineEndingIfNeeded()\n        this.tag('</tr>')\n      }\n    }\n  }\n}\n\n/**\n * @param {string} $0\n * @param {string} $1\n * @returns {string}\n */\nfunction replace($0, $1) {\n  // Pipes work, backslashes don’t (but can’t escape pipes).\n  return $1 === '|' ? $1 : $0\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/html.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js":
/*!*********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/infer.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTableAlign: () => (/* binding */ gfmTableAlign)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/**\n * @import {Event} from 'micromark-util-types'\n */\n\n/**\n * @typedef {'center' | 'left' | 'none' | 'right'} Align\n */\n\n\n\n/**\n * Figure out the alignment of a GFM table.\n *\n * @param {Readonly<Array<Event>>} events\n *   List of events.\n * @param {number} index\n *   Table enter event.\n * @returns {Array<Align>}\n *   List of aligns.\n */\nfunction gfmTableAlign(events, index) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_0__.ok)(events[index][1].type === 'table', 'expected table')\n  let inDelimiterRow = false\n  /** @type {Array<Align>} */\n  const align = []\n\n  while (index < events.length) {\n    const event = events[index]\n\n    if (inDelimiterRow) {\n      if (event[0] === 'enter') {\n        // Start of alignment value: set a new column.\n        // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n        if (event[1].type === 'tableContent') {\n          align.push(\n            events[index + 1][1].type === 'tableDelimiterMarker'\n              ? 'left'\n              : 'none'\n          )\n        }\n      }\n      // Exits:\n      // End of alignment value: change the column.\n      // To do: `markdown-rs` uses `tableDelimiterCellValue`.\n      else if (event[1].type === 'tableContent') {\n        if (events[index - 1][1].type === 'tableDelimiterMarker') {\n          const alignIndex = align.length - 1\n\n          align[alignIndex] = align[alignIndex] === 'left' ? 'center' : 'right'\n        }\n      }\n      // Done!\n      else if (event[1].type === 'tableDelimiterRow') {\n        break\n      }\n    } else if (event[0] === 'enter' && event[1].type === 'tableDelimiterRow') {\n      inDelimiterRow = true\n    }\n\n    index += 1\n  }\n\n  return align\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js":
/*!**********************************************************************!*\
  !*** ./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js ***!
  \**********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   gfmTable: () => (/* binding */ gfmTable)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! micromark-factory-space */ \"(ssr)/./node_modules/micromark-factory-space/dev/index.js\");\n/* harmony import */ var micromark_util_character__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! micromark-util-character */ \"(ssr)/./node_modules/micromark-util-character/dev/index.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/types.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\");\n/* harmony import */ var micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! micromark-util-symbol */ \"(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\");\n/* harmony import */ var _edit_map_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./edit-map.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/edit-map.js\");\n/* harmony import */ var _infer_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./infer.js */ \"(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/infer.js\");\n/**\n * @import {Event, Extension, Point, Resolver, State, Token, TokenizeContext, Tokenizer} from 'micromark-util-types'\n */\n\n/**\n * @typedef {[number, number, number, number]} Range\n *   Cell info.\n *\n * @typedef {0 | 1 | 2 | 3} RowKind\n *   Where we are: `1` for head row, `2` for delimiter row, `3` for body row.\n */\n\n\n\n\n\n\n\n\n/**\n * Create an HTML extension for `micromark` to support GitHub tables syntax.\n *\n * @returns {Extension}\n *   Extension for `micromark` that can be passed in `extensions` to enable GFM\n *   table syntax.\n */\nfunction gfmTable() {\n  return {\n    flow: {\n      null: {name: 'table', tokenize: tokenizeTable, resolveAll: resolveTable}\n    }\n  }\n}\n\n/**\n * @this {TokenizeContext}\n * @type {Tokenizer}\n */\nfunction tokenizeTable(effects, ok, nok) {\n  const self = this\n  let size = 0\n  let sizeB = 0\n  /** @type {boolean | undefined} */\n  let seen\n\n  return start\n\n  /**\n   * Start of a GFM table.\n   *\n   * If there is a valid table row or table head before, then we try to parse\n   * another row.\n   * Otherwise, we try to parse a head.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   * @type {State}\n   */\n  function start(code) {\n    let index = self.events.length - 1\n\n    while (index > -1) {\n      const type = self.events[index][1].type\n      if (\n        type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding ||\n        // Note: markdown-rs uses `whitespace` instead of `linePrefix`\n        type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix\n      )\n        index--\n      else break\n    }\n\n    const tail = index > -1 ? self.events[index][1].type : null\n\n    const next =\n      tail === 'tableHead' || tail === 'tableRow' ? bodyRowStart : headRowBefore\n\n    // Don’t allow lazy body rows.\n    if (next === bodyRowStart && self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    return next(code)\n  }\n\n  /**\n   * Before table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBefore(code) {\n    effects.enter('tableHead')\n    effects.enter('tableRow')\n    return headRowStart(code)\n  }\n\n  /**\n   * Before table head row, after whitespace.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowStart(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      return headRowBreak(code)\n    }\n\n    // To do: micromark-js should let us parse our own whitespace in extensions,\n    // like `markdown-rs`:\n    //\n    // ```js\n    // // 4+ spaces.\n    // if (markdownSpace(code)) {\n    //   return nok(code)\n    // }\n    // ```\n\n    seen = true\n    // Count the first character, that isn’t a pipe, double.\n    sizeB += 1\n    return headRowBreak(code)\n  }\n\n  /**\n   * At break in table head row.\n   *\n   * ```markdown\n   * > | | a |\n   *     ^\n   *       ^\n   *         ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowBreak(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof) {\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      // If anything other than one pipe (ignoring whitespace) was used, it’s fine.\n      if (sizeB > 1) {\n        sizeB = 0\n        // To do: check if this works.\n        // Feel free to interrupt:\n        self.interrupt = true\n        effects.exit('tableRow')\n        effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n        effects.consume(code)\n        effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.lineEnding)\n        return headDelimiterStart\n      }\n\n      // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n      return nok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      // To do: check if this is fine.\n      // effects.attempt(State::Next(StateName::GfmTableHeadRowBreak), State::Nok)\n      // State::Retry(space_or_tab(tokenizer))\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, headRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    sizeB += 1\n\n    if (seen) {\n      seen = false\n      // Header cell count.\n      size += 1\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      // Whether a delimiter was seen.\n      seen = true\n      return headRowBreak\n    }\n\n    // Anything else is cell data.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n    return headRowData(code)\n  }\n\n  /**\n   * In table head row data.\n   *\n   * ```markdown\n   * > | | a |\n   *       ^\n   *   | | - |\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowData(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n      return headRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? headRowEscape : headRowData\n  }\n\n  /**\n   * In table head row escape.\n   *\n   * ```markdown\n   * > | | a\\-b |\n   *         ^\n   *   | | ---- |\n   *   | | c    |\n   * ```\n   *\n   * @type {State}\n   */\n  function headRowEscape(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.consume(code)\n      return headRowData\n    }\n\n    return headRowData(code)\n  }\n\n  /**\n   * Before delimiter row.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterStart(code) {\n    // Reset `interrupt`.\n    self.interrupt = false\n\n    // Note: in `markdown-rs`, we need to handle piercing here too.\n    if (self.parser.lazy[self.now().line]) {\n      return nok(code)\n    }\n\n    effects.enter('tableDelimiterRow')\n    // Track if we’ve seen a `:` or `|`.\n    seen = false\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(self.parser.constructs.disable.null, 'expected `disabled.null`')\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n        effects,\n        headDelimiterBefore,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.linePrefix,\n        self.parser.constructs.disable.null.includes('codeIndented')\n          ? undefined\n          : micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.tabSize\n      )(code)\n    }\n\n    return headDelimiterBefore(code)\n  }\n\n  /**\n   * Before delimiter row, after optional whitespace.\n   *\n   * Reused when a `|` is found later, to parse another cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *     ^\n   *   | | b |\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterBefore(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      return headDelimiterValueBefore(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      seen = true\n      // If we start with a pipe, we open a cell marker.\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return headDelimiterCellBefore\n    }\n\n    // More whitespace / empty row not allowed at start.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After `|`, before delimiter cell.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *      ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellBefore(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n        effects,\n        headDelimiterValueBefore,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterValueBefore(code)\n  }\n\n  /**\n   * Before delimiter cell value.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterValueBefore(code) {\n    // Align: left.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      sizeB += 1\n      seen = true\n\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterLeftAlignmentAfter\n    }\n\n    // Align: none.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      sizeB += 1\n      // To do: seems weird that this *isn’t* left aligned, but that state is used?\n      return headDelimiterLeftAlignmentAfter(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      return headDelimiterCellAfter(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * After delimiter cell left alignment marker.\n   *\n   * ```markdown\n   *   | | a  |\n   * > | | :- |\n   *        ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterLeftAlignmentAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.enter('tableDelimiterFiller')\n      return headDelimiterFiller(code)\n    }\n\n    // Anything else is not ok after the left-align colon.\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter cell filler.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | - |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterFiller(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.dash) {\n      effects.consume(code)\n      return headDelimiterFiller\n    }\n\n    // Align is `center` if it was `left`, `right` otherwise.\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.colon) {\n      seen = true\n      effects.exit('tableDelimiterFiller')\n      effects.enter('tableDelimiterMarker')\n      effects.consume(code)\n      effects.exit('tableDelimiterMarker')\n      return headDelimiterRightAlignmentAfter\n    }\n\n    effects.exit('tableDelimiterFiller')\n    return headDelimiterRightAlignmentAfter(code)\n  }\n\n  /**\n   * After delimiter cell right alignment marker.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterRightAlignmentAfter(code) {\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(\n        effects,\n        headDelimiterCellAfter,\n        micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace\n      )(code)\n    }\n\n    return headDelimiterCellAfter(code)\n  }\n\n  /**\n   * After delimiter cell.\n   *\n   * ```markdown\n   *   | |  a |\n   * > | | -: |\n   *          ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterCellAfter(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      return headDelimiterBefore(code)\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      // Exit when:\n      // * there was no `:` or `|` at all (it’s a thematic break or setext\n      //   underline instead)\n      // * the header cell count is not the delimiter cell count\n      if (!seen || size !== sizeB) {\n        return headDelimiterNok(code)\n      }\n\n      // Note: in markdown-rs`, a reset is needed here.\n      effects.exit('tableDelimiterRow')\n      effects.exit('tableHead')\n      // To do: in `markdown-rs`, resolvers need to be registered manually.\n      // effects.register_resolver(ResolveName::GfmTable)\n      return ok(code)\n    }\n\n    return headDelimiterNok(code)\n  }\n\n  /**\n   * In delimiter row, at a disallowed byte.\n   *\n   * ```markdown\n   *   | | a |\n   * > | | x |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function headDelimiterNok(code) {\n    // Note: in `markdown-rs`, we need to reset, in `micromark-js` we don‘t.\n    return nok(code)\n  }\n\n  /**\n   * Before table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowStart(code) {\n    // Note: in `markdown-rs` we need to manually take care of a prefix,\n    // but in `micromark-js` that is done for us, so if we’re here, we’re\n    // never at whitespace.\n    effects.enter('tableRow')\n    return bodyRowBreak(code)\n  }\n\n  /**\n   * At break in table body row.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *     ^\n   *       ^\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowBreak(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.enter('tableCellDivider')\n      effects.consume(code)\n      effects.exit('tableCellDivider')\n      return bodyRowBreak\n    }\n\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof || (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEnding)(code)) {\n      effects.exit('tableRow')\n      return ok(code)\n    }\n\n    if ((0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownSpace)(code)) {\n      return (0,micromark_factory_space__WEBPACK_IMPORTED_MODULE_3__.factorySpace)(effects, bodyRowBreak, micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.whitespace)(code)\n    }\n\n    // Anything else is cell content.\n    effects.enter(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n    return bodyRowData(code)\n  }\n\n  /**\n   * In table body row data.\n   *\n   * ```markdown\n   *   | | a |\n   *   | | - |\n   * > | | b |\n   *       ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowData(code) {\n    if (\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.eof ||\n      code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar ||\n      (0,micromark_util_character__WEBPACK_IMPORTED_MODULE_2__.markdownLineEndingOrSpace)(code)\n    ) {\n      effects.exit(micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data)\n      return bodyRowBreak(code)\n    }\n\n    effects.consume(code)\n    return code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash ? bodyRowEscape : bodyRowData\n  }\n\n  /**\n   * In table body row escape.\n   *\n   * ```markdown\n   *   | | a    |\n   *   | | ---- |\n   * > | | b\\-c |\n   *         ^\n   * ```\n   *\n   * @type {State}\n   */\n  function bodyRowEscape(code) {\n    if (code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.backslash || code === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_1__.codes.verticalBar) {\n      effects.consume(code)\n      return bodyRowData\n    }\n\n    return bodyRowData(code)\n  }\n}\n\n/** @type {Resolver} */\n\nfunction resolveTable(events, context) {\n  let index = -1\n  let inFirstCellAwaitingPipe = true\n  /** @type {RowKind} */\n  let rowKind = 0\n  /** @type {Range} */\n  let lastCell = [0, 0, 0, 0]\n  /** @type {Range} */\n  let cell = [0, 0, 0, 0]\n  let afterHeadAwaitingFirstBodyRow = false\n  let lastTableEnd = 0\n  /** @type {Token | undefined} */\n  let currentTable\n  /** @type {Token | undefined} */\n  let currentBody\n  /** @type {Token | undefined} */\n  let currentCell\n\n  const map = new _edit_map_js__WEBPACK_IMPORTED_MODULE_6__.EditMap()\n\n  while (++index < events.length) {\n    const event = events[index]\n    const token = event[1]\n\n    if (event[0] === 'enter') {\n      // Start of head.\n      if (token.type === 'tableHead') {\n        afterHeadAwaitingFirstBodyRow = false\n\n        // Inject previous (body end and) table end.\n        if (lastTableEnd !== 0) {\n          (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, 'there should be a table opening')\n          flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n          currentBody = undefined\n          lastTableEnd = 0\n        }\n\n        // Inject table start.\n        currentTable = {\n          type: 'table',\n          start: Object.assign({}, token.start),\n          // Note: correct end is set later.\n          end: Object.assign({}, token.end)\n        }\n        map.add(index, 0, [['enter', currentTable, context]])\n      } else if (\n        token.type === 'tableRow' ||\n        token.type === 'tableDelimiterRow'\n      ) {\n        inFirstCellAwaitingPipe = true\n        currentCell = undefined\n        lastCell = [0, 0, 0, 0]\n        cell = [0, index + 1, 0, 0]\n\n        // Inject table body start.\n        if (afterHeadAwaitingFirstBodyRow) {\n          afterHeadAwaitingFirstBodyRow = false\n          currentBody = {\n            type: 'tableBody',\n            start: Object.assign({}, token.start),\n            // Note: correct end is set later.\n            end: Object.assign({}, token.end)\n          }\n          map.add(index, 0, [['enter', currentBody, context]])\n        }\n\n        rowKind = token.type === 'tableDelimiterRow' ? 2 : currentBody ? 3 : 1\n      }\n      // Cell data.\n      else if (\n        rowKind &&\n        (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data ||\n          token.type === 'tableDelimiterMarker' ||\n          token.type === 'tableDelimiterFiller')\n      ) {\n        inFirstCellAwaitingPipe = false\n\n        // First value in cell.\n        if (cell[2] === 0) {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n            lastCell = [0, 0, 0, 0]\n          }\n\n          cell[2] = index\n        }\n      } else if (token.type === 'tableCellDivider') {\n        if (inFirstCellAwaitingPipe) {\n          inFirstCellAwaitingPipe = false\n        } else {\n          if (lastCell[1] !== 0) {\n            cell[0] = cell[1]\n            currentCell = flushCell(\n              map,\n              context,\n              lastCell,\n              rowKind,\n              undefined,\n              currentCell\n            )\n          }\n\n          lastCell = cell\n          cell = [lastCell[1], index, 0, 0]\n        }\n      }\n    }\n    // Exit events.\n    else if (token.type === 'tableHead') {\n      afterHeadAwaitingFirstBodyRow = true\n      lastTableEnd = index\n    } else if (\n      token.type === 'tableRow' ||\n      token.type === 'tableDelimiterRow'\n    ) {\n      lastTableEnd = index\n\n      if (lastCell[1] !== 0) {\n        cell[0] = cell[1]\n        currentCell = flushCell(\n          map,\n          context,\n          lastCell,\n          rowKind,\n          index,\n          currentCell\n        )\n      } else if (cell[1] !== 0) {\n        currentCell = flushCell(map, context, cell, rowKind, index, currentCell)\n      }\n\n      rowKind = 0\n    } else if (\n      rowKind &&\n      (token.type === micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.data ||\n        token.type === 'tableDelimiterMarker' ||\n        token.type === 'tableDelimiterFiller')\n    ) {\n      cell[3] = index\n    }\n  }\n\n  if (lastTableEnd !== 0) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(currentTable, 'expected table opening')\n    flushTableEnd(map, context, lastTableEnd, currentTable, currentBody)\n  }\n\n  map.consume(context.events)\n\n  // To do: move this into `html`, when events are exposed there.\n  // That’s what `markdown-rs` does.\n  // That needs updates to `mdast-util-gfm-table`.\n  index = -1\n  while (++index < context.events.length) {\n    const event = context.events[index]\n    if (event[0] === 'enter' && event[1].type === 'table') {\n      event[1]._align = (0,_infer_js__WEBPACK_IMPORTED_MODULE_7__.gfmTableAlign)(context.events, index)\n    }\n  }\n\n  return events\n}\n\n/**\n * Generate a cell.\n *\n * @param {EditMap} map\n * @param {Readonly<TokenizeContext>} context\n * @param {Readonly<Range>} range\n * @param {RowKind} rowKind\n * @param {number | undefined} rowEnd\n * @param {Token | undefined} previousCell\n * @returns {Token | undefined}\n */\n// eslint-disable-next-line max-params\nfunction flushCell(map, context, range, rowKind, rowEnd, previousCell) {\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCell' : 'tableCell'\n  const groupName =\n    rowKind === 1\n      ? 'tableHeader'\n      : rowKind === 2\n        ? 'tableDelimiter'\n        : 'tableData'\n  // `markdown-rs` uses:\n  // rowKind === 2 ? 'tableDelimiterCellValue' : 'tableCellText'\n  const valueName = 'tableContent'\n\n  // Insert an exit for the previous cell, if there is one.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //          ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[0] !== 0) {\n    (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(previousCell, 'expected previous cell enter')\n    previousCell.end = Object.assign({}, getPoint(context.events, range[0]))\n    map.add(range[0], 0, [['exit', previousCell, context]])\n  }\n\n  // Insert enter of this cell.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //           ^-- enter\n  //           ^^^^-- this cell\n  // ```\n  const now = getPoint(context.events, range[1])\n  previousCell = {\n    type: groupName,\n    start: Object.assign({}, now),\n    // Note: correct end is set later.\n    end: Object.assign({}, now)\n  }\n  map.add(range[1], 0, [['enter', previousCell, context]])\n\n  // Insert text start at first data start and end at last data end, and\n  // remove events between.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //            ^-- enter\n  //             ^-- exit\n  //           ^^^^-- this cell\n  // ```\n  if (range[2] !== 0) {\n    const relatedStart = getPoint(context.events, range[2])\n    const relatedEnd = getPoint(context.events, range[3])\n    /** @type {Token} */\n    const valueToken = {\n      type: valueName,\n      start: Object.assign({}, relatedStart),\n      end: Object.assign({}, relatedEnd)\n    }\n    map.add(range[2], 0, [['enter', valueToken, context]])\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(range[3] !== 0)\n\n    if (rowKind !== 2) {\n      // Fix positional info on remaining events\n      const start = context.events[range[2]]\n      const end = context.events[range[3]]\n      start[1].end = Object.assign({}, end[1].end)\n      start[1].type = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_0__.types.chunkText\n      start[1].contentType = micromark_util_symbol__WEBPACK_IMPORTED_MODULE_5__.constants.contentTypeText\n\n      // Remove if needed.\n      if (range[3] > range[2] + 1) {\n        const a = range[2] + 1\n        const b = range[3] - range[2] - 1\n        map.add(a, b, [])\n      }\n    }\n\n    map.add(range[3] + 1, 0, [['exit', valueToken, context]])\n  }\n\n  // Insert an exit for the last cell, if at the row end.\n  //\n  // ```markdown\n  // > | | aa | bb | cc |\n  //                    ^-- exit\n  //               ^^^^^^-- this cell (the last one contains two “between” parts)\n  // ```\n  if (rowEnd !== undefined) {\n    previousCell.end = Object.assign({}, getPoint(context.events, rowEnd))\n    map.add(rowEnd, 0, [['exit', previousCell, context]])\n    previousCell = undefined\n  }\n\n  return previousCell\n}\n\n/**\n * Generate table end (and table body end).\n *\n * @param {Readonly<EditMap>} map\n * @param {Readonly<TokenizeContext>} context\n * @param {number} index\n * @param {Token} table\n * @param {Token | undefined} tableBody\n */\n// eslint-disable-next-line max-params\nfunction flushTableEnd(map, context, index, table, tableBody) {\n  /** @type {Array<Event>} */\n  const exits = []\n  const related = getPoint(context.events, index)\n\n  if (tableBody) {\n    tableBody.end = Object.assign({}, related)\n    exits.push(['exit', tableBody, context])\n  }\n\n  table.end = Object.assign({}, related)\n  exits.push(['exit', table, context])\n\n  map.add(index + 1, 0, exits)\n}\n\n/**\n * @param {Readonly<Array<Event>>} events\n * @param {number} index\n * @returns {Readonly<Point>}\n */\nfunction getPoint(events, index) {\n  const event = events[index]\n  const side = event[0] === 'enter' ? 'start' : 'end'\n  return event[1][side]\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-extension-gfm-table/dev/lib/syntax.js\n");

/***/ })

};
;