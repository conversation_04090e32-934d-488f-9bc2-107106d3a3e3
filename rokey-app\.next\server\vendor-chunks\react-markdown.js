"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-markdown";
exports.ids = ["vendor-chunks/react-markdown"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-markdown/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/react-markdown/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Markdown: () => (/* binding */ Markdown),\n/* harmony export */   MarkdownAsync: () => (/* binding */ MarkdownAsync),\n/* harmony export */   MarkdownHooks: () => (/* binding */ MarkdownHooks),\n/* harmony export */   defaultUrlTransform: () => (/* binding */ defaultUrlTransform)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! hast-util-to-jsx-runtime */ \"(ssr)/./node_modules/hast-util-to-jsx-runtime/lib/index.js\");\n/* harmony import */ var html_url_attributes__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! html-url-attributes */ \"(ssr)/./node_modules/html-url-attributes/lib/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var remark_parse__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! remark-parse */ \"(ssr)/./node_modules/remark-parse/lib/index.js\");\n/* harmony import */ var remark_rehype__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! remark-rehype */ \"(ssr)/./node_modules/remark-rehype/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/unified/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var vfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! vfile */ \"(ssr)/./node_modules/vfile/lib/index.js\");\n/**\n * @import {Element, Nodes, Parents, Root} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {ComponentType, JSX, ReactElement, ReactNode} from 'react'\n * @import {Options as RemarkRehypeOptions} from 'remark-rehype'\n * @import {BuildVisitor} from 'unist-util-visit'\n * @import {PluggableList, Processor} from 'unified'\n */ /**\n * @callback AllowElement\n *   Filter elements.\n * @param {Readonly<Element>} element\n *   Element to check.\n * @param {number} index\n *   Index of `element` in `parent`.\n * @param {Readonly<Parents> | undefined} parent\n *   Parent of `element`.\n * @returns {boolean | null | undefined}\n *   Whether to allow `element` (default: `false`).\n */ /**\n * @typedef ExtraProps\n *   Extra fields we pass.\n * @property {Element | undefined} [node]\n *   passed when `passNode` is on.\n */ /**\n * @typedef {{\n *   [Key in keyof JSX.IntrinsicElements]?: ComponentType<JSX.IntrinsicElements[Key] & ExtraProps> | keyof JSX.IntrinsicElements\n * }} Components\n *   Map tag names to components.\n */ /**\n * @typedef Deprecation\n *   Deprecation.\n * @property {string} from\n *   Old field.\n * @property {string} id\n *   ID in readme.\n * @property {keyof Options} [to]\n *   New field.\n */ /**\n * @typedef Options\n *   Configuration.\n * @property {AllowElement | null | undefined} [allowElement]\n *   Filter elements (optional);\n *   `allowedElements` / `disallowedElements` is used first.\n * @property {ReadonlyArray<string> | null | undefined} [allowedElements]\n *   Tag names to allow (default: all tag names);\n *   cannot combine w/ `disallowedElements`.\n * @property {string | null | undefined} [children]\n *   Markdown.\n * @property {Components | null | undefined} [components]\n *   Map tag names to components.\n * @property {ReadonlyArray<string> | null | undefined} [disallowedElements]\n *   Tag names to disallow (default: `[]`);\n *   cannot combine w/ `allowedElements`.\n * @property {PluggableList | null | undefined} [rehypePlugins]\n *   List of rehype plugins to use.\n * @property {PluggableList | null | undefined} [remarkPlugins]\n *   List of remark plugins to use.\n * @property {Readonly<RemarkRehypeOptions> | null | undefined} [remarkRehypeOptions]\n *   Options to pass through to `remark-rehype`.\n * @property {boolean | null | undefined} [skipHtml=false]\n *   Ignore HTML in markdown completely (default: `false`).\n * @property {boolean | null | undefined} [unwrapDisallowed=false]\n *   Extract (unwrap) what’s in disallowed elements (default: `false`);\n *   normally when say `strong` is not allowed, it and it’s children are dropped,\n *   with `unwrapDisallowed` the element itself is replaced by its children.\n * @property {UrlTransform | null | undefined} [urlTransform]\n *   Change URLs (default: `defaultUrlTransform`)\n */ /**\n * @typedef HooksOptionsOnly\n *   Configuration specifically for {@linkcode MarkdownHooks}.\n * @property {ReactNode | null | undefined} [fallback]\n *   Content to render while the processor processing the markdown (optional).\n */ /**\n * @typedef {Options & HooksOptionsOnly} HooksOptions\n *   Configuration for {@linkcode MarkdownHooks};\n *   extends the regular {@linkcode Options} with a `fallback` prop.\n */ /**\n * @callback UrlTransform\n *   Transform all URLs.\n * @param {string} url\n *   URL.\n * @param {string} key\n *   Property name (example: `'href'`).\n * @param {Readonly<Element>} node\n *   Node.\n * @returns {string | null | undefined}\n *   Transformed URL (optional).\n */ \n\n\n\n\n\n\n\n\n\nconst changelog = 'https://github.com/remarkjs/react-markdown/blob/main/changelog.md';\n/** @type {PluggableList} */ const emptyPlugins = [];\n/** @type {Readonly<RemarkRehypeOptions>} */ const emptyRemarkRehypeOptions = {\n    allowDangerousHtml: true\n};\nconst safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i;\n// Mutable because we `delete` any time it’s used and a message is sent.\n/** @type {ReadonlyArray<Readonly<Deprecation>>} */ const deprecations = [\n    {\n        from: 'astPlugins',\n        id: 'remove-buggy-html-in-markdown-parser'\n    },\n    {\n        from: 'allowDangerousHtml',\n        id: 'remove-buggy-html-in-markdown-parser'\n    },\n    {\n        from: 'allowNode',\n        id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n        to: 'allowElement'\n    },\n    {\n        from: 'allowedTypes',\n        id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n        to: 'allowedElements'\n    },\n    {\n        from: 'className',\n        id: 'remove-classname'\n    },\n    {\n        from: 'disallowedTypes',\n        id: 'replace-allownode-allowedtypes-and-disallowedtypes',\n        to: 'disallowedElements'\n    },\n    {\n        from: 'escapeHtml',\n        id: 'remove-buggy-html-in-markdown-parser'\n    },\n    {\n        from: 'includeElementIndex',\n        id: '#remove-includeelementindex'\n    },\n    {\n        from: 'includeNodeIndex',\n        id: 'change-includenodeindex-to-includeelementindex'\n    },\n    {\n        from: 'linkTarget',\n        id: 'remove-linktarget'\n    },\n    {\n        from: 'plugins',\n        id: 'change-plugins-to-remarkplugins',\n        to: 'remarkPlugins'\n    },\n    {\n        from: 'rawSourcePos',\n        id: '#remove-rawsourcepos'\n    },\n    {\n        from: 'renderers',\n        id: 'change-renderers-to-components',\n        to: 'components'\n    },\n    {\n        from: 'source',\n        id: 'change-source-to-children',\n        to: 'children'\n    },\n    {\n        from: 'sourcePos',\n        id: '#remove-sourcepos'\n    },\n    {\n        from: 'transformImageUri',\n        id: '#add-urltransform',\n        to: 'urlTransform'\n    },\n    {\n        from: 'transformLinkUri',\n        id: '#add-urltransform',\n        to: 'urlTransform'\n    }\n];\n/**\n * Component to render markdown.\n *\n * This is a synchronous component.\n * When using async plugins,\n * see {@linkcode MarkdownAsync} or {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */ function Markdown(options) {\n    const processor = createProcessor(options);\n    const file = createFile(options);\n    return post(processor.runSync(processor.parse(file), file), options);\n}\n/**\n * Component to render markdown with support for async plugins\n * through async/await.\n *\n * Components returning promises are supported on the server.\n * For async support on the client,\n * see {@linkcode MarkdownHooks}.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Promise<ReactElement>}\n *   Promise to a React element.\n */ async function MarkdownAsync(options) {\n    const processor = createProcessor(options);\n    const file = createFile(options);\n    const tree = await processor.run(processor.parse(file), file);\n    return post(tree, options);\n}\n/**\n * Component to render markdown with support for async plugins through hooks.\n *\n * This uses `useEffect` and `useState` hooks.\n * Hooks run on the client and do not immediately render something.\n * For async support on the server,\n * see {@linkcode MarkdownAsync}.\n *\n * @param {Readonly<HooksOptions>} options\n *   Props.\n * @returns {ReactNode}\n *   React node.\n */ function MarkdownHooks(options) {\n    const processor = createProcessor(options);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(/** @type {Error | undefined} */ undefined);\n    const [tree, setTree] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(/** @type {Root | undefined} */ undefined);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkdownHooks.useEffect\": function() {\n            let cancelled = false;\n            const file = createFile(options);\n            processor.run(processor.parse(file), file, {\n                \"MarkdownHooks.useEffect\": function(error, tree) {\n                    if (!cancelled) {\n                        setError(error);\n                        setTree(tree);\n                    }\n                }\n            }[\"MarkdownHooks.useEffect\"]);\n            /**\n       * @returns {undefined}\n       *   Nothing.\n       */ return ({\n                \"MarkdownHooks.useEffect\": function() {\n                    cancelled = true;\n                }\n            })[\"MarkdownHooks.useEffect\"];\n        }\n    }[\"MarkdownHooks.useEffect\"], [\n        options.children,\n        options.rehypePlugins,\n        options.remarkPlugins,\n        options.remarkRehypeOptions\n    ]);\n    if (error) throw error;\n    return tree ? post(tree, options) : options.fallback;\n}\n/**\n * Set up the `unified` processor.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {Processor<MdastRoot, MdastRoot, Root, undefined, undefined>}\n *   Result.\n */ function createProcessor(options) {\n    const rehypePlugins = options.rehypePlugins || emptyPlugins;\n    const remarkPlugins = options.remarkPlugins || emptyPlugins;\n    const remarkRehypeOptions = options.remarkRehypeOptions ? {\n        ...options.remarkRehypeOptions,\n        ...emptyRemarkRehypeOptions\n    } : emptyRemarkRehypeOptions;\n    const processor = (0,unified__WEBPACK_IMPORTED_MODULE_2__.unified)().use(remark_parse__WEBPACK_IMPORTED_MODULE_3__[\"default\"]).use(remarkPlugins).use(remark_rehype__WEBPACK_IMPORTED_MODULE_4__[\"default\"], remarkRehypeOptions).use(rehypePlugins);\n    return processor;\n}\n/**\n * Set up the virtual file.\n *\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {VFile}\n *   Result.\n */ function createFile(options) {\n    const children = options.children || '';\n    const file = new vfile__WEBPACK_IMPORTED_MODULE_5__.VFile();\n    if (typeof children === 'string') {\n        file.value = children;\n    } else {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)('Unexpected value `' + children + '` for `children` prop, expected `string`');\n    }\n    return file;\n}\n/**\n * Process the result from unified some more.\n *\n * @param {Nodes} tree\n *   Tree.\n * @param {Readonly<Options>} options\n *   Props.\n * @returns {ReactElement}\n *   React element.\n */ function post(tree, options) {\n    const allowedElements = options.allowedElements;\n    const allowElement = options.allowElement;\n    const components = options.components;\n    const disallowedElements = options.disallowedElements;\n    const skipHtml = options.skipHtml;\n    const unwrapDisallowed = options.unwrapDisallowed;\n    const urlTransform = options.urlTransform || defaultUrlTransform;\n    for (const deprecation of deprecations){\n        if (Object.hasOwn(options, deprecation.from)) {\n            (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)('Unexpected `' + deprecation.from + '` prop, ' + (deprecation.to ? 'use `' + deprecation.to + '` instead' : 'remove it') + ' (see <' + changelog + '#' + deprecation.id + '> for more info)');\n        }\n    }\n    if (allowedElements && disallowedElements) {\n        (0,devlop__WEBPACK_IMPORTED_MODULE_6__.unreachable)('Unexpected combined `allowedElements` and `disallowedElements`, expected one or the other');\n    }\n    (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_7__.visit)(tree, transform);\n    return (0,hast_util_to_jsx_runtime__WEBPACK_IMPORTED_MODULE_8__.toJsxRuntime)(tree, {\n        Fragment: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment,\n        components,\n        ignoreInvalidStyle: true,\n        jsx: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx,\n        jsxs: react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs,\n        passKeys: true,\n        passNode: true\n    });\n    /** @type {BuildVisitor<Root>} */ function transform(node, index, parent) {\n        if (node.type === 'raw' && parent && typeof index === 'number') {\n            if (skipHtml) {\n                parent.children.splice(index, 1);\n            } else {\n                parent.children[index] = {\n                    type: 'text',\n                    value: node.value\n                };\n            }\n            return index;\n        }\n        if (node.type === 'element') {\n            /** @type {string} */ let key;\n            for(key in html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes){\n                if (Object.hasOwn(html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes, key) && Object.hasOwn(node.properties, key)) {\n                    const value = node.properties[key];\n                    const test = html_url_attributes__WEBPACK_IMPORTED_MODULE_9__.urlAttributes[key];\n                    if (test === null || test.includes(node.tagName)) {\n                        node.properties[key] = urlTransform(String(value || ''), key, node);\n                    }\n                }\n            }\n        }\n        if (node.type === 'element') {\n            let remove = allowedElements ? !allowedElements.includes(node.tagName) : disallowedElements ? disallowedElements.includes(node.tagName) : false;\n            if (!remove && allowElement && typeof index === 'number') {\n                remove = !allowElement(node, index, parent);\n            }\n            if (remove && parent && typeof index === 'number') {\n                if (unwrapDisallowed && node.children) {\n                    parent.children.splice(index, 1, ...node.children);\n                } else {\n                    parent.children.splice(index, 1);\n                }\n                return index;\n            }\n        }\n    }\n}\n/**\n * Make a URL safe.\n *\n * @satisfies {UrlTransform}\n * @param {string} value\n *   URL.\n * @returns {string}\n *   Safe URL.\n */ function defaultUrlTransform(value) {\n    // Same as:\n    // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>\n    // But without the `encode` part.\n    const colon = value.indexOf(':');\n    const questionMark = value.indexOf('?');\n    const numberSign = value.indexOf('#');\n    const slash = value.indexOf('/');\n    if (// If there is no protocol, it’s relative.\n    colon === -1 || // If the first colon is after a `?`, `#`, or `/`, it’s not a protocol.\n    slash !== -1 && colon > slash || questionMark !== -1 && colon > questionMark || numberSign !== -1 && colon > numberSign || // It is a protocol, it should be allowed.\n    safeProtocol.test(value.slice(0, colon))) {\n        return value;\n    }\n    return '';\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-markdown/lib/index.js\n");

/***/ })

};
;