"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-rehype";
exports.ids = ["vendor-chunks/remark-rehype"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-rehype/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/remark-rehype/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ remarkRehype)\n/* harmony export */ });\n/* harmony import */ var mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! mdast-util-to-hast */ \"(ssr)/./node_modules/mdast-util-to-hast/lib/index.js\");\n/**\n * @import {Root as HastRoot} from 'hast'\n * @import {Root as MdastRoot} from 'mdast'\n * @import {Options as ToHastOptions} from 'mdast-util-to-hast'\n * @import {Processor} from 'unified'\n * @import {VFile} from 'vfile'\n */\n\n/**\n * @typedef {Omit<ToHastOptions, 'file'>} Options\n *\n * @callback TransformBridge\n *   Bridge-mode.\n *\n *   Runs the destination with the new hast tree.\n *   Discards result.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {Promise<undefined>}\n *   Nothing.\n *\n * @callback TransformMutate\n *  Mutate-mode.\n *\n *  Further transformers run on the hast tree.\n * @param {MdastRoot} tree\n *   Tree.\n * @param {VFile} file\n *   File.\n * @returns {HastRoot}\n *   Tree (hast).\n */\n\n\n\n/**\n * Turn markdown into HTML.\n *\n * ##### Notes\n *\n * ###### Signature\n *\n * * if a processor is given,\n *   runs the (rehype) plugins used on it with a hast tree,\n *   then discards the result (*bridge mode*)\n * * otherwise,\n *   returns a hast tree,\n *   the plugins used after `remarkRehype` are rehype plugins (*mutate mode*)\n *\n * > 👉 **Note**:\n * > It’s highly unlikely that you want to pass a `processor`.\n *\n * ###### HTML\n *\n * Raw HTML is available in mdast as `html` nodes and can be embedded in hast\n * as semistandard `raw` nodes.\n * Most plugins ignore `raw` nodes but two notable ones don’t:\n *\n * * `rehype-stringify` also has an option `allowDangerousHtml` which will\n *   output the raw HTML.\n *   This is typically discouraged as noted by the option name but is useful if\n *   you completely trust authors\n * * `rehype-raw` can handle the raw embedded HTML strings by parsing them\n *   into standard hast nodes (`element`, `text`, etc);\n *   this is a heavy task as it needs a full HTML parser,\n *   but it is the only way to support untrusted content\n *\n * ###### Footnotes\n *\n * Many options supported here relate to footnotes.\n * Footnotes are not specified by CommonMark,\n * which we follow by default.\n * They are supported by GitHub,\n * so footnotes can be enabled in markdown with `remark-gfm`.\n *\n * The options `footnoteBackLabel` and `footnoteLabel` define natural language\n * that explains footnotes,\n * which is hidden for sighted users but shown to assistive technology.\n * When your page is not in English,\n * you must define translated values.\n *\n * Back references use ARIA attributes,\n * but the section label itself uses a heading that is hidden with an\n * `sr-only` class.\n * To show it to sighted users,\n * define different attributes in `footnoteLabelProperties`.\n *\n * ###### Clobbering\n *\n * Footnotes introduces a problem,\n * as it links footnote calls to footnote definitions on the page through `id`\n * attributes generated from user content,\n * which results in DOM clobbering.\n *\n * DOM clobbering is this:\n *\n * ```html\n * <p id=x></p>\n * <script>alert(x) // `x` now refers to the DOM `p#x` element</script>\n * ```\n *\n * Elements by their ID are made available by browsers on the `window` object,\n * which is a security risk.\n * Using a prefix solves this problem.\n *\n * More information on how to handle clobbering and the prefix is explained in\n * *Example: headings (DOM clobbering)* in `rehype-sanitize`.\n *\n * ###### Unknown nodes\n *\n * Unknown nodes are nodes with a type that isn’t in `handlers` or `passThrough`.\n * The default behavior for unknown nodes is:\n *\n * * when the node has a `value`\n *   (and doesn’t have `data.hName`, `data.hProperties`, or `data.hChildren`,\n *   see later),\n *   create a hast `text` node\n * * otherwise,\n *   create a `<div>` element (which could be changed with `data.hName`),\n *   with its children mapped from mdast to hast as well\n *\n * This behavior can be changed by passing an `unknownHandler`.\n *\n * @overload\n * @param {Processor} processor\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge}\n *\n * @overload\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformMutate}\n *\n * @overload\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n * @param {Readonly<Options> | null | undefined} [options]\n * @returns {TransformBridge | TransformMutate}\n *\n * @param {Readonly<Options> | Processor | null | undefined} [destination]\n *   Processor or configuration (optional).\n * @param {Readonly<Options> | null | undefined} [options]\n *   When a processor was given,\n *   configuration (optional).\n * @returns {TransformBridge | TransformMutate}\n *   Transform.\n */\nfunction remarkRehype(destination, options) {\n  if (destination && 'run' in destination) {\n    /**\n     * @type {TransformBridge}\n     */\n    return async function (tree, file) {\n      // Cast because root in -> root out.\n      const hastTree = /** @type {HastRoot} */ (\n        (0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(tree, {file, ...options})\n      )\n      await destination.run(hastTree, file)\n    }\n  }\n\n  /**\n   * @type {TransformMutate}\n   */\n  return function (tree, file) {\n    // Cast because root in -> root out.\n    // To do: in the future, disallow ` || options` fallback.\n    // With `unified-engine`, `destination` can be `undefined` but\n    // `options` will be the file set.\n    // We should not pass that as `options`.\n    return /** @type {HastRoot} */ (\n      (0,mdast_util_to_hast__WEBPACK_IMPORTED_MODULE_0__.toHast)(tree, {file, ...(destination || options)})\n    )\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVtYXJrLXJlaHlwZS9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksa0JBQWtCO0FBQzlCLFlBQVksbUJBQW1CO0FBQy9CLFlBQVksMEJBQTBCO0FBQ3RDLFlBQVksV0FBVztBQUN2QixZQUFZLE9BQU87QUFDbkI7O0FBRUE7QUFDQSxhQUFhLDZCQUE2QjtBQUMxQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLFdBQVc7QUFDdEI7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxXQUFXO0FBQ3RCO0FBQ0EsV0FBVyxPQUFPO0FBQ2xCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7O0FBRXlDOztBQUV6QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxXQUFXO0FBQ3RCLFdBQVcsc0NBQXNDO0FBQ2pELGFBQWE7QUFDYjtBQUNBO0FBQ0EsV0FBVyxzQ0FBc0M7QUFDakQsYUFBYTtBQUNiO0FBQ0E7QUFDQSxXQUFXLGtEQUFrRDtBQUM3RCxXQUFXLHNDQUFzQztBQUNqRCxhQUFhO0FBQ2I7QUFDQSxXQUFXLGtEQUFrRDtBQUM3RDtBQUNBLFdBQVcsc0NBQXNDO0FBQ2pEO0FBQ0E7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNlO0FBQ2Y7QUFDQTtBQUNBLGNBQWM7QUFDZDtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsVUFBVTtBQUM1QyxRQUFRLDBEQUFNLFFBQVEsaUJBQWlCO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLFVBQVU7QUFDaEMsTUFBTSwwREFBTSxRQUFRLGtDQUFrQztBQUN0RDtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxSb0tleSBBcHBcXHJva2V5LWFwcFxcbm9kZV9tb2R1bGVzXFxyZW1hcmstcmVoeXBlXFxsaWJcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7Um9vdCBhcyBIYXN0Um9vdH0gZnJvbSAnaGFzdCdcbiAqIEBpbXBvcnQge1Jvb3QgYXMgTWRhc3RSb290fSBmcm9tICdtZGFzdCdcbiAqIEBpbXBvcnQge09wdGlvbnMgYXMgVG9IYXN0T3B0aW9uc30gZnJvbSAnbWRhc3QtdXRpbC10by1oYXN0J1xuICogQGltcG9ydCB7UHJvY2Vzc29yfSBmcm9tICd1bmlmaWVkJ1xuICogQGltcG9ydCB7VkZpbGV9IGZyb20gJ3ZmaWxlJ1xuICovXG5cbi8qKlxuICogQHR5cGVkZWYge09taXQ8VG9IYXN0T3B0aW9ucywgJ2ZpbGUnPn0gT3B0aW9uc1xuICpcbiAqIEBjYWxsYmFjayBUcmFuc2Zvcm1CcmlkZ2VcbiAqICAgQnJpZGdlLW1vZGUuXG4gKlxuICogICBSdW5zIHRoZSBkZXN0aW5hdGlvbiB3aXRoIHRoZSBuZXcgaGFzdCB0cmVlLlxuICogICBEaXNjYXJkcyByZXN1bHQuXG4gKiBAcGFyYW0ge01kYXN0Um9vdH0gdHJlZVxuICogICBUcmVlLlxuICogQHBhcmFtIHtWRmlsZX0gZmlsZVxuICogICBGaWxlLlxuICogQHJldHVybnMge1Byb21pc2U8dW5kZWZpbmVkPn1cbiAqICAgTm90aGluZy5cbiAqXG4gKiBAY2FsbGJhY2sgVHJhbnNmb3JtTXV0YXRlXG4gKiAgTXV0YXRlLW1vZGUuXG4gKlxuICogIEZ1cnRoZXIgdHJhbnNmb3JtZXJzIHJ1biBvbiB0aGUgaGFzdCB0cmVlLlxuICogQHBhcmFtIHtNZGFzdFJvb3R9IHRyZWVcbiAqICAgVHJlZS5cbiAqIEBwYXJhbSB7VkZpbGV9IGZpbGVcbiAqICAgRmlsZS5cbiAqIEByZXR1cm5zIHtIYXN0Um9vdH1cbiAqICAgVHJlZSAoaGFzdCkuXG4gKi9cblxuaW1wb3J0IHt0b0hhc3R9IGZyb20gJ21kYXN0LXV0aWwtdG8taGFzdCdcblxuLyoqXG4gKiBUdXJuIG1hcmtkb3duIGludG8gSFRNTC5cbiAqXG4gKiAjIyMjIyBOb3Rlc1xuICpcbiAqICMjIyMjIyBTaWduYXR1cmVcbiAqXG4gKiAqIGlmIGEgcHJvY2Vzc29yIGlzIGdpdmVuLFxuICogICBydW5zIHRoZSAocmVoeXBlKSBwbHVnaW5zIHVzZWQgb24gaXQgd2l0aCBhIGhhc3QgdHJlZSxcbiAqICAgdGhlbiBkaXNjYXJkcyB0aGUgcmVzdWx0ICgqYnJpZGdlIG1vZGUqKVxuICogKiBvdGhlcndpc2UsXG4gKiAgIHJldHVybnMgYSBoYXN0IHRyZWUsXG4gKiAgIHRoZSBwbHVnaW5zIHVzZWQgYWZ0ZXIgYHJlbWFya1JlaHlwZWAgYXJlIHJlaHlwZSBwbHVnaW5zICgqbXV0YXRlIG1vZGUqKVxuICpcbiAqID4g8J+RiSAqKk5vdGUqKjpcbiAqID4gSXTigJlzIGhpZ2hseSB1bmxpa2VseSB0aGF0IHlvdSB3YW50IHRvIHBhc3MgYSBgcHJvY2Vzc29yYC5cbiAqXG4gKiAjIyMjIyMgSFRNTFxuICpcbiAqIFJhdyBIVE1MIGlzIGF2YWlsYWJsZSBpbiBtZGFzdCBhcyBgaHRtbGAgbm9kZXMgYW5kIGNhbiBiZSBlbWJlZGRlZCBpbiBoYXN0XG4gKiBhcyBzZW1pc3RhbmRhcmQgYHJhd2Agbm9kZXMuXG4gKiBNb3N0IHBsdWdpbnMgaWdub3JlIGByYXdgIG5vZGVzIGJ1dCB0d28gbm90YWJsZSBvbmVzIGRvbuKAmXQ6XG4gKlxuICogKiBgcmVoeXBlLXN0cmluZ2lmeWAgYWxzbyBoYXMgYW4gb3B0aW9uIGBhbGxvd0Rhbmdlcm91c0h0bWxgIHdoaWNoIHdpbGxcbiAqICAgb3V0cHV0IHRoZSByYXcgSFRNTC5cbiAqICAgVGhpcyBpcyB0eXBpY2FsbHkgZGlzY291cmFnZWQgYXMgbm90ZWQgYnkgdGhlIG9wdGlvbiBuYW1lIGJ1dCBpcyB1c2VmdWwgaWZcbiAqICAgeW91IGNvbXBsZXRlbHkgdHJ1c3QgYXV0aG9yc1xuICogKiBgcmVoeXBlLXJhd2AgY2FuIGhhbmRsZSB0aGUgcmF3IGVtYmVkZGVkIEhUTUwgc3RyaW5ncyBieSBwYXJzaW5nIHRoZW1cbiAqICAgaW50byBzdGFuZGFyZCBoYXN0IG5vZGVzIChgZWxlbWVudGAsIGB0ZXh0YCwgZXRjKTtcbiAqICAgdGhpcyBpcyBhIGhlYXZ5IHRhc2sgYXMgaXQgbmVlZHMgYSBmdWxsIEhUTUwgcGFyc2VyLFxuICogICBidXQgaXQgaXMgdGhlIG9ubHkgd2F5IHRvIHN1cHBvcnQgdW50cnVzdGVkIGNvbnRlbnRcbiAqXG4gKiAjIyMjIyMgRm9vdG5vdGVzXG4gKlxuICogTWFueSBvcHRpb25zIHN1cHBvcnRlZCBoZXJlIHJlbGF0ZSB0byBmb290bm90ZXMuXG4gKiBGb290bm90ZXMgYXJlIG5vdCBzcGVjaWZpZWQgYnkgQ29tbW9uTWFyayxcbiAqIHdoaWNoIHdlIGZvbGxvdyBieSBkZWZhdWx0LlxuICogVGhleSBhcmUgc3VwcG9ydGVkIGJ5IEdpdEh1YixcbiAqIHNvIGZvb3Rub3RlcyBjYW4gYmUgZW5hYmxlZCBpbiBtYXJrZG93biB3aXRoIGByZW1hcmstZ2ZtYC5cbiAqXG4gKiBUaGUgb3B0aW9ucyBgZm9vdG5vdGVCYWNrTGFiZWxgIGFuZCBgZm9vdG5vdGVMYWJlbGAgZGVmaW5lIG5hdHVyYWwgbGFuZ3VhZ2VcbiAqIHRoYXQgZXhwbGFpbnMgZm9vdG5vdGVzLFxuICogd2hpY2ggaXMgaGlkZGVuIGZvciBzaWdodGVkIHVzZXJzIGJ1dCBzaG93biB0byBhc3Npc3RpdmUgdGVjaG5vbG9neS5cbiAqIFdoZW4geW91ciBwYWdlIGlzIG5vdCBpbiBFbmdsaXNoLFxuICogeW91IG11c3QgZGVmaW5lIHRyYW5zbGF0ZWQgdmFsdWVzLlxuICpcbiAqIEJhY2sgcmVmZXJlbmNlcyB1c2UgQVJJQSBhdHRyaWJ1dGVzLFxuICogYnV0IHRoZSBzZWN0aW9uIGxhYmVsIGl0c2VsZiB1c2VzIGEgaGVhZGluZyB0aGF0IGlzIGhpZGRlbiB3aXRoIGFuXG4gKiBgc3Itb25seWAgY2xhc3MuXG4gKiBUbyBzaG93IGl0IHRvIHNpZ2h0ZWQgdXNlcnMsXG4gKiBkZWZpbmUgZGlmZmVyZW50IGF0dHJpYnV0ZXMgaW4gYGZvb3Rub3RlTGFiZWxQcm9wZXJ0aWVzYC5cbiAqXG4gKiAjIyMjIyMgQ2xvYmJlcmluZ1xuICpcbiAqIEZvb3Rub3RlcyBpbnRyb2R1Y2VzIGEgcHJvYmxlbSxcbiAqIGFzIGl0IGxpbmtzIGZvb3Rub3RlIGNhbGxzIHRvIGZvb3Rub3RlIGRlZmluaXRpb25zIG9uIHRoZSBwYWdlIHRocm91Z2ggYGlkYFxuICogYXR0cmlidXRlcyBnZW5lcmF0ZWQgZnJvbSB1c2VyIGNvbnRlbnQsXG4gKiB3aGljaCByZXN1bHRzIGluIERPTSBjbG9iYmVyaW5nLlxuICpcbiAqIERPTSBjbG9iYmVyaW5nIGlzIHRoaXM6XG4gKlxuICogYGBgaHRtbFxuICogPHAgaWQ9eD48L3A+XG4gKiA8c2NyaXB0PmFsZXJ0KHgpIC8vIGB4YCBub3cgcmVmZXJzIHRvIHRoZSBET00gYHAjeGAgZWxlbWVudDwvc2NyaXB0PlxuICogYGBgXG4gKlxuICogRWxlbWVudHMgYnkgdGhlaXIgSUQgYXJlIG1hZGUgYXZhaWxhYmxlIGJ5IGJyb3dzZXJzIG9uIHRoZSBgd2luZG93YCBvYmplY3QsXG4gKiB3aGljaCBpcyBhIHNlY3VyaXR5IHJpc2suXG4gKiBVc2luZyBhIHByZWZpeCBzb2x2ZXMgdGhpcyBwcm9ibGVtLlxuICpcbiAqIE1vcmUgaW5mb3JtYXRpb24gb24gaG93IHRvIGhhbmRsZSBjbG9iYmVyaW5nIGFuZCB0aGUgcHJlZml4IGlzIGV4cGxhaW5lZCBpblxuICogKkV4YW1wbGU6IGhlYWRpbmdzIChET00gY2xvYmJlcmluZykqIGluIGByZWh5cGUtc2FuaXRpemVgLlxuICpcbiAqICMjIyMjIyBVbmtub3duIG5vZGVzXG4gKlxuICogVW5rbm93biBub2RlcyBhcmUgbm9kZXMgd2l0aCBhIHR5cGUgdGhhdCBpc27igJl0IGluIGBoYW5kbGVyc2Agb3IgYHBhc3NUaHJvdWdoYC5cbiAqIFRoZSBkZWZhdWx0IGJlaGF2aW9yIGZvciB1bmtub3duIG5vZGVzIGlzOlxuICpcbiAqICogd2hlbiB0aGUgbm9kZSBoYXMgYSBgdmFsdWVgXG4gKiAgIChhbmQgZG9lc27igJl0IGhhdmUgYGRhdGEuaE5hbWVgLCBgZGF0YS5oUHJvcGVydGllc2AsIG9yIGBkYXRhLmhDaGlsZHJlbmAsXG4gKiAgIHNlZSBsYXRlciksXG4gKiAgIGNyZWF0ZSBhIGhhc3QgYHRleHRgIG5vZGVcbiAqICogb3RoZXJ3aXNlLFxuICogICBjcmVhdGUgYSBgPGRpdj5gIGVsZW1lbnQgKHdoaWNoIGNvdWxkIGJlIGNoYW5nZWQgd2l0aCBgZGF0YS5oTmFtZWApLFxuICogICB3aXRoIGl0cyBjaGlsZHJlbiBtYXBwZWQgZnJvbSBtZGFzdCB0byBoYXN0IGFzIHdlbGxcbiAqXG4gKiBUaGlzIGJlaGF2aW9yIGNhbiBiZSBjaGFuZ2VkIGJ5IHBhc3NpbmcgYW4gYHVua25vd25IYW5kbGVyYC5cbiAqXG4gKiBAb3ZlcmxvYWRcbiAqIEBwYXJhbSB7UHJvY2Vzc29yfSBwcm9jZXNzb3JcbiAqIEBwYXJhbSB7UmVhZG9ubHk8T3B0aW9ucz4gfCBudWxsIHwgdW5kZWZpbmVkfSBbb3B0aW9uc11cbiAqIEByZXR1cm5zIHtUcmFuc2Zvcm1CcmlkZ2V9XG4gKlxuICogQG92ZXJsb2FkXG4gKiBAcGFyYW0ge1JlYWRvbmx5PE9wdGlvbnM+IHwgbnVsbCB8IHVuZGVmaW5lZH0gW29wdGlvbnNdXG4gKiBAcmV0dXJucyB7VHJhbnNmb3JtTXV0YXRlfVxuICpcbiAqIEBvdmVybG9hZFxuICogQHBhcmFtIHtSZWFkb25seTxPcHRpb25zPiB8IFByb2Nlc3NvciB8IG51bGwgfCB1bmRlZmluZWR9IFtkZXN0aW5hdGlvbl1cbiAqIEBwYXJhbSB7UmVhZG9ubHk8T3B0aW9ucz4gfCBudWxsIHwgdW5kZWZpbmVkfSBbb3B0aW9uc11cbiAqIEByZXR1cm5zIHtUcmFuc2Zvcm1CcmlkZ2UgfCBUcmFuc2Zvcm1NdXRhdGV9XG4gKlxuICogQHBhcmFtIHtSZWFkb25seTxPcHRpb25zPiB8IFByb2Nlc3NvciB8IG51bGwgfCB1bmRlZmluZWR9IFtkZXN0aW5hdGlvbl1cbiAqICAgUHJvY2Vzc29yIG9yIGNvbmZpZ3VyYXRpb24gKG9wdGlvbmFsKS5cbiAqIEBwYXJhbSB7UmVhZG9ubHk8T3B0aW9ucz4gfCBudWxsIHwgdW5kZWZpbmVkfSBbb3B0aW9uc11cbiAqICAgV2hlbiBhIHByb2Nlc3NvciB3YXMgZ2l2ZW4sXG4gKiAgIGNvbmZpZ3VyYXRpb24gKG9wdGlvbmFsKS5cbiAqIEByZXR1cm5zIHtUcmFuc2Zvcm1CcmlkZ2UgfCBUcmFuc2Zvcm1NdXRhdGV9XG4gKiAgIFRyYW5zZm9ybS5cbiAqL1xuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gcmVtYXJrUmVoeXBlKGRlc3RpbmF0aW9uLCBvcHRpb25zKSB7XG4gIGlmIChkZXN0aW5hdGlvbiAmJiAncnVuJyBpbiBkZXN0aW5hdGlvbikge1xuICAgIC8qKlxuICAgICAqIEB0eXBlIHtUcmFuc2Zvcm1CcmlkZ2V9XG4gICAgICovXG4gICAgcmV0dXJuIGFzeW5jIGZ1bmN0aW9uICh0cmVlLCBmaWxlKSB7XG4gICAgICAvLyBDYXN0IGJlY2F1c2Ugcm9vdCBpbiAtPiByb290IG91dC5cbiAgICAgIGNvbnN0IGhhc3RUcmVlID0gLyoqIEB0eXBlIHtIYXN0Um9vdH0gKi8gKFxuICAgICAgICB0b0hhc3QodHJlZSwge2ZpbGUsIC4uLm9wdGlvbnN9KVxuICAgICAgKVxuICAgICAgYXdhaXQgZGVzdGluYXRpb24ucnVuKGhhc3RUcmVlLCBmaWxlKVxuICAgIH1cbiAgfVxuXG4gIC8qKlxuICAgKiBAdHlwZSB7VHJhbnNmb3JtTXV0YXRlfVxuICAgKi9cbiAgcmV0dXJuIGZ1bmN0aW9uICh0cmVlLCBmaWxlKSB7XG4gICAgLy8gQ2FzdCBiZWNhdXNlIHJvb3QgaW4gLT4gcm9vdCBvdXQuXG4gICAgLy8gVG8gZG86IGluIHRoZSBmdXR1cmUsIGRpc2FsbG93IGAgfHwgb3B0aW9uc2AgZmFsbGJhY2suXG4gICAgLy8gV2l0aCBgdW5pZmllZC1lbmdpbmVgLCBgZGVzdGluYXRpb25gIGNhbiBiZSBgdW5kZWZpbmVkYCBidXRcbiAgICAvLyBgb3B0aW9uc2Agd2lsbCBiZSB0aGUgZmlsZSBzZXQuXG4gICAgLy8gV2Ugc2hvdWxkIG5vdCBwYXNzIHRoYXQgYXMgYG9wdGlvbnNgLlxuICAgIHJldHVybiAvKiogQHR5cGUge0hhc3RSb290fSAqLyAoXG4gICAgICB0b0hhc3QodHJlZSwge2ZpbGUsIC4uLihkZXN0aW5hdGlvbiB8fCBvcHRpb25zKX0pXG4gICAgKVxuICB9XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-rehype/lib/index.js\n");

/***/ })

};
;