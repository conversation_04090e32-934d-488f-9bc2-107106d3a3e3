"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_components_OrchestrationCanvas_tsx"],{

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js":
/*!************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction ArrowRightIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3\"\n    }));\n}\n_c = ArrowRightIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowRightIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"ArrowRightIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\nfunction MinusIcon(param, svgRef) {\n    let { title, titleId, ...props } = param;\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M5 12h14\"\n    }));\n}\n_c = MinusIcon;\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(MinusIcon);\n_c1 = ForwardRef;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\nvar _c, _c1;\n$RefreshReg$(_c, \"MinusIcon\");\n$RefreshReg$(_c1, \"ForwardRef\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ChatMessage.tsx":
/*!****************************************!*\
  !*** ./src/components/ChatMessage.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatMessage: () => (/* binding */ ChatMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ChatMessage auto */ \n\n\nconst ChatMessage = (param)=>{\n    let { message } = param;\n    const getRoleColor = (roleId)=>{\n        if (!roleId) return 'from-blue-500 to-blue-600'; // Moderator\n        // Generate consistent colors based on role name\n        const colors = [\n            'from-green-500 to-green-600',\n            'from-purple-500 to-purple-600',\n            'from-orange-500 to-orange-600',\n            'from-pink-500 to-pink-600',\n            'from-indigo-500 to-indigo-600',\n            'from-teal-500 to-teal-600',\n            'from-red-500 to-red-600',\n            'from-yellow-500 to-yellow-600'\n        ];\n        const hash = roleId.split('').reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n        return colors[hash % colors.length];\n    };\n    const getRoleIcon = (sender, roleId)=>{\n        if (sender === 'moderator') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                lineNumber: 48,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 50,\n            columnNumber: 12\n        }, undefined);\n    };\n    const getMessageTypeIcon = (type)=>{\n        switch(type){\n            case 'assignment':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, undefined);\n            case 'completion':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, undefined);\n            case 'handoff':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    const formatTime = (timestamp)=>{\n        return timestamp.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const isFromModerator = message.sender === 'moderator';\n    const roleColor = getRoleColor(message.roleId);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex \".concat(isFromModerator ? 'justify-start' : 'justify-start', \" mb-4\"),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[85%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r \".concat(roleColor, \" flex items-center justify-center text-white shadow-sm\"),\n                    children: getRoleIcon(message.sender, message.roleId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-semibold \".concat(isFromModerator ? 'text-blue-700' : 'text-gray-700'),\n                                    children: message.senderName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined),\n                                getMessageTypeIcon(message.type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: getMessageTypeIcon(message.type)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block px-4 py-3 rounded-2xl shadow-sm \".concat(isFromModerator ? 'bg-blue-50 border border-blue-100' : 'bg-gray-50 border border-gray-100', \" \").concat(message.type === 'completion' ? 'border-green-200 bg-green-50' : message.type === 'assignment' ? 'border-blue-200 bg-blue-50' : message.type === 'handoff' ? 'border-purple-200 bg-purple-50' : ''),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm leading-relaxed \".concat(isFromModerator ? 'text-blue-900' : 'text-gray-800', \" \").concat(message.type === 'completion' ? 'text-green-900' : message.type === 'assignment' ? 'text-blue-900' : message.type === 'handoff' ? 'text-purple-900' : ''),\n                                children: message.content.split('\\n').map((line, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                        children: [\n                                            line,\n                                            index < message.content.split('\\n').length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 70\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined),\n                        message.type !== 'message' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium \".concat(message.type === 'assignment' ? 'bg-blue-100 text-blue-800' : message.type === 'completion' ? 'bg-green-100 text-green-800' : message.type === 'handoff' ? 'bg-purple-100 text-purple-800' : message.type === 'clarification' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'),\n                                children: [\n                                    message.type === 'assignment' && '📋 Task Assignment',\n                                    message.type === 'completion' && '✅ Task Complete',\n                                    message.type === 'handoff' && '🔄 Handoff',\n                                    message.type === 'clarification' && '❓ Clarification'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n_c = ChatMessage;\nvar _c;\n$RefreshReg$(_c, \"ChatMessage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ChatMessage.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/OrchestrationCanvas.tsx":
/*!************************************************!*\
  !*** ./src/components/OrchestrationCanvas.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationCanvas: () => (/* binding */ OrchestrationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OrchestrationChatroom */ \"(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationCanvas auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nconst OrchestrationCanvas = (param)=>{\n    let { executionId, onComplete, onError, onCanvasStateChange, forceMaximize = false } = param;\n    _s();\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orchestrationComplete, setOrchestrationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { events, isConnected, error } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Handle orchestration completion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            const synthesisCompleteEvent = events.find({\n                \"OrchestrationCanvas.useEffect.synthesisCompleteEvent\": (event)=>event.type === 'synthesis_complete'\n            }[\"OrchestrationCanvas.useEffect.synthesisCompleteEvent\"]);\n            if (synthesisCompleteEvent && !orchestrationComplete) {\n                var _synthesisCompleteEvent_data;\n                setOrchestrationComplete(true);\n                const result = ((_synthesisCompleteEvent_data = synthesisCompleteEvent.data) === null || _synthesisCompleteEvent_data === void 0 ? void 0 : _synthesisCompleteEvent_data.result) || 'Orchestration completed successfully';\n                setFinalResult(result);\n                // Notify parent component\n                if (onComplete) {\n                    onComplete(result);\n                }\n            }\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        events,\n        orchestrationComplete,\n        onComplete\n    ]);\n    // Handle errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            if (error && onError) {\n                onError(error);\n            }\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        error,\n        onError\n    ]);\n    const handleMinimize = ()=>{\n        setIsMinimized(true);\n        // Keep isCanvasOpen as true so component doesn't disappear completely\n        // We'll hide it via CSS transform instead\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(false, true);\n    };\n    const handleMaximize = ()=>{\n        setIsMinimized(false);\n        // isCanvasOpen should already be true, but ensure it\n        setIsCanvasOpen(true);\n        onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(true, false);\n    };\n    // Notify parent of initial canvas state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            onCanvasStateChange === null || onCanvasStateChange === void 0 ? void 0 : onCanvasStateChange(isCanvasOpen, isMinimized);\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        isCanvasOpen,\n        isMinimized,\n        onCanvasStateChange\n    ]);\n    // Handle external maximize trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            if (forceMaximize && isMinimized) {\n                console.log('🎭 [DEBUG] External maximize trigger received!');\n                handleMaximize();\n            }\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        forceMaximize,\n        isMinimized,\n        handleMaximize\n    ]);\n    // Minimized card state - now returns null, will be rendered inline in chat\n    if (isMinimized) {\n        return null;\n    }\n    // Canvas is closed\n    if (!isCanvasOpen) {\n        return null;\n    }\n    // Debug log when rendering\n    console.log('🎭 [DEBUG] OrchestrationCanvas is rendering!', {\n        isCanvasOpen,\n        isMinimized,\n        executionId,\n        shouldBeVisible: isCanvasOpen && !isMinimized,\n        transformClass: isCanvasOpen && !isMinimized ? 'translate-x-0' : 'translate-x-full'\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed top-0 right-0 h-full w-1/2 bg-gradient-to-br from-gray-900 via-black to-gray-900 shadow-2xl z-[9999] transform transition-all duration-500 ease-out \".concat(isCanvasOpen && !isMinimized ? 'translate-x-0' : 'translate-x-full'),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-r from-orange-500/10 via-transparent to-orange-500/10 pointer-events-none\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex items-center justify-between p-6 border-b border-orange-500/20 bg-gradient-to-r from-black/80 via-gray-900/90 to-black/80 backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-orange-500/50 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-orange-500/30 rounded-xl blur-md -z-10 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-bold text-white text-lg tracking-wide\",\n                                            children: \"AI Team Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-2 h-2 rounded-full \".concat(orchestrationComplete ? 'bg-green-400' : 'bg-orange-400', \" animate-pulse\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-300 font-medium\",\n                                                    children: orchestrationComplete ? 'Mission Complete' : 'Team Active'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 px-3 py-1.5 bg-orange-500/10 border border-orange-500/20 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-orange-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-orange-300 font-medium\",\n                                            children: \"LIVE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleMinimize,\n                                    className: \"group relative p-2.5 text-gray-400 hover:text-white hover:bg-orange-500/20 rounded-xl transition-all duration-300 border border-transparent hover:border-orange-500/30\",\n                                    \"aria-label\": \"Minimize canvas\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 transition-transform group-hover:scale-110\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-orange-500/20 rounded-xl opacity-0 group-hover:opacity-100 blur-sm transition-opacity duration-300 -z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 h-full overflow-hidden relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,107,53,0.1),transparent_50%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,107,53,0.05)_50%,transparent_100%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__.OrchestrationChatroom, {\n                            executionId: executionId,\n                            events: events,\n                            isConnected: isConnected,\n                            error: error,\n                            isComplete: orchestrationComplete\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n_s(OrchestrationCanvas, \"KsTXJg8wJUpwnTOsKVHiLc1u04Q=\", false, function() {\n    return [\n        _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream\n    ];\n});\n_c = OrchestrationCanvas;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationCanvas\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationCanvas.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/OrchestrationChatroom.tsx":
/*!**************************************************!*\
  !*** ./src/components/OrchestrationChatroom.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationChatroom: () => (/* binding */ OrchestrationChatroom)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatMessage */ \"(app-pages-browser)/./src/components/ChatMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TypingIndicator */ \"(app-pages-browser)/./src/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationChatroom auto */ \nvar _s = $RefreshSig$();\n\n\n\nconst OrchestrationChatroom = (param)=>{\n    let { executionId, events, isConnected, error, isComplete } = param;\n    _s();\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingSpecialists, setTypingSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationChatroom.useEffect\": ()=>{\n            var _messagesEndRef_current;\n            (_messagesEndRef_current = messagesEndRef.current) === null || _messagesEndRef_current === void 0 ? void 0 : _messagesEndRef_current.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"OrchestrationChatroom.useEffect\"], [\n        chatMessages\n    ]);\n    // Convert orchestration events to chat messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationChatroom.useEffect\": ()=>{\n            const newMessages = [];\n            const currentlyTyping = new Set();\n            events.forEach({\n                \"OrchestrationChatroom.useEffect\": (event, index)=>{\n                    const timestamp = new Date(event.timestamp || Date.now());\n                    const messageId = \"\".concat(executionId, \"-\").concat(index);\n                    switch(event.type){\n                        case 'orchestration_started':\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: \"🎬 Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.\",\n                                timestamp,\n                                type: 'message'\n                            });\n                            break;\n                        case 'task_decomposed':\n                            var _event_data;\n                            const steps = ((_event_data = event.data) === null || _event_data === void 0 ? void 0 : _event_data.steps) || [];\n                            const teamIntro = steps.map({\n                                \"OrchestrationChatroom.useEffect.teamIntro\": (step)=>\"\\uD83E\\uDD16 @\".concat(step.roleId, \" - \").concat(step.modelName || 'AI Specialist')\n                            }[\"OrchestrationChatroom.useEffect.teamIntro\"]).join('\\n');\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: \"\\uD83D\\uDCCB I've analyzed the task and assembled this expert team:\\n\\n\".concat(teamIntro, \"\\n\\nLet's begin the collaboration!\"),\n                                timestamp,\n                                type: 'assignment'\n                            });\n                            break;\n                        case 'step_assigned':\n                            var _event_data1;\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                roleId: event.role_id,\n                                content: \"\\uD83C\\uDFAF @\".concat(event.role_id, \", you're up! \").concat(((_event_data1 = event.data) === null || _event_data1 === void 0 ? void 0 : _event_data1.commentary) || 'Please begin your specialized work on this task.'),\n                                timestamp,\n                                type: 'assignment'\n                            });\n                            break;\n                        case 'moderator_assignment':\n                            var _event_data2;\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                roleId: event.role_id,\n                                content: ((_event_data2 = event.data) === null || _event_data2 === void 0 ? void 0 : _event_data2.message) || \"\\uD83C\\uDFAF @\".concat(event.role_id, \", you're up! Please begin your specialized work on this task.\"),\n                                timestamp,\n                                type: 'assignment'\n                            });\n                            break;\n                        case 'specialist_acknowledgment':\n                            var _event_data3;\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'specialist',\n                                senderName: event.role_id || 'Specialist',\n                                roleId: event.role_id,\n                                content: ((_event_data3 = event.data) === null || _event_data3 === void 0 ? void 0 : _event_data3.message) || \"✅ Understood! I'm \".concat(event.role_id, \" and I'll handle this task with expertise. Starting work now...\"),\n                                timestamp,\n                                type: 'message'\n                            });\n                            break;\n                        case 'step_started':\n                            // Add to typing indicators\n                            if (event.role_id) {\n                                currentlyTyping.add(event.role_id);\n                            }\n                            break;\n                        case 'step_progress':\n                            if (event.role_id) {\n                                currentlyTyping.add(event.role_id);\n                            }\n                            break;\n                        case 'specialist_message':\n                            var _event_data4, _event_data5;\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'specialist',\n                                senderName: event.role_id || 'Specialist',\n                                roleId: event.role_id,\n                                content: \"\".concat(((_event_data4 = event.data) === null || _event_data4 === void 0 ? void 0 : _event_data4.message) || '🎉 Perfect! I\\'ve completed my part of the task. Here\\'s what I\\'ve delivered:', \"\\n\\n\").concat(((_event_data5 = event.data) === null || _event_data5 === void 0 ? void 0 : _event_data5.output) || 'Task completed successfully!'),\n                                timestamp,\n                                type: 'completion'\n                            });\n                            break;\n                        case 'step_completed':\n                            // Remove from typing\n                            if (event.role_id) {\n                                currentlyTyping.delete(event.role_id);\n                            }\n                            break;\n                        case 'handoff_message':\n                            var _event_data6, _event_data7, _event_data8;\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: ((_event_data6 = event.data) === null || _event_data6 === void 0 ? void 0 : _event_data6.message) || \"✨ Excellent work, @\".concat((_event_data7 = event.data) === null || _event_data7 === void 0 ? void 0 : _event_data7.fromRole, \"! Quality looks great. Now passing to @\").concat((_event_data8 = event.data) === null || _event_data8 === void 0 ? void 0 : _event_data8.toRole, \"...\"),\n                                timestamp,\n                                type: 'handoff'\n                            });\n                            break;\n                        case 'synthesis_started':\n                            var _event_data9;\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: ((_event_data9 = event.data) === null || _event_data9 === void 0 ? void 0 : _event_data9.message) || \"\\uD83E\\uDDE9 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...\",\n                                timestamp,\n                                type: 'message'\n                            });\n                            currentlyTyping.add('moderator');\n                            break;\n                        case 'synthesis_complete':\n                            var _event_data10;\n                            currentlyTyping.delete('moderator');\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: ((_event_data10 = event.data) === null || _event_data10 === void 0 ? void 0 : _event_data10.message) || \"\\uD83C\\uDF8A Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!\",\n                                timestamp,\n                                type: 'completion'\n                            });\n                            break;\n                    }\n                }\n            }[\"OrchestrationChatroom.useEffect\"]);\n            setChatMessages(newMessages);\n            setTypingSpecialists(currentlyTyping);\n        }\n    }[\"OrchestrationChatroom.useEffect\"], [\n        events,\n        executionId\n    ]);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"px-4 py-2 text-xs font-medium \".concat(isConnected ? 'bg-green-50 text-green-700 border-b border-green-100' : 'bg-yellow-50 text-yellow-700 border-b border-yellow-100'),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-2 h-2 rounded-full \".concat(isConnected ? 'bg-green-500' : 'bg-yellow-500')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: isConnected ? 'Connected to AI Team' : 'Connecting...'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    chatMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600 animate-pulse\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Waiting for AI team to start collaboration...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined),\n                    chatMessages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from(typingSpecialists).map((specialist)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_3__.TypingIndicator, {\n                            senderName: specialist,\n                            roleId: specialist !== 'moderator' ? specialist : undefined\n                        }, specialist, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, undefined);\n};\n_s(OrchestrationChatroom, \"Trlsk+ImATjahibYSefjX0C5OX4=\");\n_c = OrchestrationChatroom;\nvar _c;\n$RefreshReg$(_c, \"OrchestrationChatroom\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/OrchestrationChatroom.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/TypingIndicator.tsx":
/*!********************************************!*\
  !*** ./src/components/TypingIndicator.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TypingIndicator: () => (/* binding */ TypingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* __next_internal_client_entry_do_not_use__ TypingIndicator auto */ \n\n\nconst TypingIndicator = (param)=>{\n    let { senderName, roleId } = param;\n    const getRoleColor = (roleId)=>{\n        if (!roleId || roleId === 'moderator') return 'from-blue-500 to-blue-600'; // Moderator\n        // Generate consistent colors based on role name\n        const colors = [\n            'from-green-500 to-green-600',\n            'from-purple-500 to-purple-600',\n            'from-orange-500 to-orange-600',\n            'from-pink-500 to-pink-600',\n            'from-indigo-500 to-indigo-600',\n            'from-teal-500 to-teal-600',\n            'from-red-500 to-red-600',\n            'from-yellow-500 to-yellow-600'\n        ];\n        const hash = roleId.split('').reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n        return colors[hash % colors.length];\n    };\n    const getRoleIcon = (roleId)=>{\n        if (!roleId || roleId === 'moderator') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                lineNumber: 36,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n            lineNumber: 38,\n            columnNumber: 12\n        }, undefined);\n    };\n    const getTypingMessage = (senderName)=>{\n        const messages = [\n            'is thinking...',\n            'is working on this...',\n            'is analyzing...',\n            'is processing...',\n            'is crafting a response...'\n        ];\n        // Use sender name to consistently pick a message\n        const hash = senderName.split('').reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n        return messages[hash % messages.length];\n    };\n    const roleColor = getRoleColor(roleId);\n    const isFromModerator = !roleId || roleId === 'moderator';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-start mb-4 opacity-75\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[85%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r \".concat(roleColor, \" flex items-center justify-center text-white shadow-sm animate-pulse\"),\n                    children: getRoleIcon(roleId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-semibold \".concat(isFromModerator ? 'text-blue-700' : 'text-gray-700'),\n                                    children: senderName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: getTypingMessage(senderName)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"inline-block px-4 py-3 rounded-2xl shadow-sm \".concat(isFromModerator ? 'bg-blue-50 border border-blue-100' : 'bg-gray-50 border border-gray-100'),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: '0ms'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: '150ms'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: '300ms'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n_c = TypingIndicator;\nvar _c;\n$RefreshReg$(_c, \"TypingIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1R5cGluZ0luZGljYXRvci50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7OztBQUUwQjtBQUlXO0FBTzlCLE1BQU1HLGtCQUFrRDtRQUFDLEVBQUVDLFVBQVUsRUFBRUMsTUFBTSxFQUFFO0lBQ3BGLE1BQU1DLGVBQWUsQ0FBQ0Q7UUFDcEIsSUFBSSxDQUFDQSxVQUFVQSxXQUFXLGFBQWEsT0FBTyw2QkFBNkIsWUFBWTtRQUV2RixnREFBZ0Q7UUFDaEQsTUFBTUUsU0FBUztZQUNiO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7WUFDQTtZQUNBO1lBQ0E7U0FDRDtRQUVELE1BQU1DLE9BQU9ILE9BQU9JLEtBQUssQ0FBQyxJQUFJQyxNQUFNLENBQUMsQ0FBQ0MsS0FBS0MsT0FBU0QsTUFBTUMsS0FBS0MsVUFBVSxDQUFDLElBQUk7UUFDOUUsT0FBT04sTUFBTSxDQUFDQyxPQUFPRCxPQUFPTyxNQUFNLENBQUM7SUFDckM7SUFFQSxNQUFNQyxjQUFjLENBQUNWO1FBQ25CLElBQUksQ0FBQ0EsVUFBVUEsV0FBVyxhQUFhO1lBQ3JDLHFCQUFPLDhEQUFDSCxrSEFBWUE7Z0JBQUNjLFdBQVU7Ozs7OztRQUNqQztRQUNBLHFCQUFPLDhEQUFDZixrSEFBV0E7WUFBQ2UsV0FBVTs7Ozs7O0lBQ2hDO0lBRUEsTUFBTUMsbUJBQW1CLENBQUNiO1FBQ3hCLE1BQU1jLFdBQVc7WUFDZjtZQUNBO1lBQ0E7WUFDQTtZQUNBO1NBQ0Q7UUFFRCxpREFBaUQ7UUFDakQsTUFBTVYsT0FBT0osV0FBV0ssS0FBSyxDQUFDLElBQUlDLE1BQU0sQ0FBQyxDQUFDQyxLQUFLQyxPQUFTRCxNQUFNQyxLQUFLQyxVQUFVLENBQUMsSUFBSTtRQUNsRixPQUFPSyxRQUFRLENBQUNWLE9BQU9VLFNBQVNKLE1BQU0sQ0FBQztJQUN6QztJQUVBLE1BQU1LLFlBQVliLGFBQWFEO0lBQy9CLE1BQU1lLGtCQUFrQixDQUFDZixVQUFVQSxXQUFXO0lBRTlDLHFCQUNFLDhEQUFDZ0I7UUFBSUwsV0FBVTtrQkFDYiw0RUFBQ0s7WUFBSUwsV0FBVTs7OEJBRWIsOERBQUNLO29CQUFJTCxXQUFXLHVEQUFpRSxPQUFWRyxXQUFVOzhCQUM5RUosWUFBWVY7Ozs7Ozs4QkFJZiw4REFBQ2dCO29CQUFJTCxXQUFVOztzQ0FFYiw4REFBQ0s7NEJBQUlMLFdBQVU7OzhDQUNiLDhEQUFDTTtvQ0FBS04sV0FBVyx5QkFFaEIsT0FEQ0ksa0JBQWtCLGtCQUFrQjs4Q0FFbkNoQjs7Ozs7OzhDQUVILDhEQUFDa0I7b0NBQUtOLFdBQVU7OENBQ2JDLGlCQUFpQmI7Ozs7Ozs7Ozs7OztzQ0FLdEIsOERBQUNpQjs0QkFBSUwsV0FBVyxnREFJZixPQUhDSSxrQkFDSSxzQ0FDQTtzQ0FFSiw0RUFBQ0M7Z0NBQUlMLFdBQVU7MENBQ2IsNEVBQUNLO29DQUFJTCxXQUFVOztzREFDYiw4REFBQ0s7NENBQUlMLFdBQVU7NENBQWtETyxPQUFPO2dEQUFFQyxnQkFBZ0I7NENBQU07Ozs7OztzREFDaEcsOERBQUNIOzRDQUFJTCxXQUFVOzRDQUFrRE8sT0FBTztnREFBRUMsZ0JBQWdCOzRDQUFROzs7Ozs7c0RBQ2xHLDhEQUFDSDs0Q0FBSUwsV0FBVTs0Q0FBa0RPLE9BQU87Z0RBQUVDLGdCQUFnQjs0Q0FBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBUWxILEVBQUU7S0FwRldyQiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXGNvbXBvbmVudHNcXFR5cGluZ0luZGljYXRvci50c3giXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnO1xuXG5pbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgXG4gIENwdUNoaXBJY29uLFxuICBTcGFya2xlc0ljb25cbn0gZnJvbSAnQGhlcm9pY29ucy9yZWFjdC8yNC9vdXRsaW5lJztcblxuaW50ZXJmYWNlIFR5cGluZ0luZGljYXRvclByb3BzIHtcbiAgc2VuZGVyTmFtZTogc3RyaW5nO1xuICByb2xlSWQ/OiBzdHJpbmc7XG59XG5cbmV4cG9ydCBjb25zdCBUeXBpbmdJbmRpY2F0b3I6IFJlYWN0LkZDPFR5cGluZ0luZGljYXRvclByb3BzPiA9ICh7IHNlbmRlck5hbWUsIHJvbGVJZCB9KSA9PiB7XG4gIGNvbnN0IGdldFJvbGVDb2xvciA9IChyb2xlSWQ/OiBzdHJpbmcpID0+IHtcbiAgICBpZiAoIXJvbGVJZCB8fCByb2xlSWQgPT09ICdtb2RlcmF0b3InKSByZXR1cm4gJ2Zyb20tYmx1ZS01MDAgdG8tYmx1ZS02MDAnOyAvLyBNb2RlcmF0b3JcbiAgICBcbiAgICAvLyBHZW5lcmF0ZSBjb25zaXN0ZW50IGNvbG9ycyBiYXNlZCBvbiByb2xlIG5hbWVcbiAgICBjb25zdCBjb2xvcnMgPSBbXG4gICAgICAnZnJvbS1ncmVlbi01MDAgdG8tZ3JlZW4tNjAwJyxcbiAgICAgICdmcm9tLXB1cnBsZS01MDAgdG8tcHVycGxlLTYwMCcsXG4gICAgICAnZnJvbS1vcmFuZ2UtNTAwIHRvLW9yYW5nZS02MDAnLFxuICAgICAgJ2Zyb20tcGluay01MDAgdG8tcGluay02MDAnLFxuICAgICAgJ2Zyb20taW5kaWdvLTUwMCB0by1pbmRpZ28tNjAwJyxcbiAgICAgICdmcm9tLXRlYWwtNTAwIHRvLXRlYWwtNjAwJyxcbiAgICAgICdmcm9tLXJlZC01MDAgdG8tcmVkLTYwMCcsXG4gICAgICAnZnJvbS15ZWxsb3ctNTAwIHRvLXllbGxvdy02MDAnLFxuICAgIF07XG4gICAgXG4gICAgY29uc3QgaGFzaCA9IHJvbGVJZC5zcGxpdCgnJykucmVkdWNlKChhY2MsIGNoYXIpID0+IGFjYyArIGNoYXIuY2hhckNvZGVBdCgwKSwgMCk7XG4gICAgcmV0dXJuIGNvbG9yc1toYXNoICUgY29sb3JzLmxlbmd0aF07XG4gIH07XG5cbiAgY29uc3QgZ2V0Um9sZUljb24gPSAocm9sZUlkPzogc3RyaW5nKSA9PiB7XG4gICAgaWYgKCFyb2xlSWQgfHwgcm9sZUlkID09PSAnbW9kZXJhdG9yJykge1xuICAgICAgcmV0dXJuIDxTcGFya2xlc0ljb24gY2xhc3NOYW1lPVwidy00IGgtNFwiIC8+O1xuICAgIH1cbiAgICByZXR1cm4gPENwdUNoaXBJY29uIGNsYXNzTmFtZT1cInctNCBoLTRcIiAvPjtcbiAgfTtcblxuICBjb25zdCBnZXRUeXBpbmdNZXNzYWdlID0gKHNlbmRlck5hbWU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IG1lc3NhZ2VzID0gW1xuICAgICAgJ2lzIHRoaW5raW5nLi4uJyxcbiAgICAgICdpcyB3b3JraW5nIG9uIHRoaXMuLi4nLFxuICAgICAgJ2lzIGFuYWx5emluZy4uLicsXG4gICAgICAnaXMgcHJvY2Vzc2luZy4uLicsXG4gICAgICAnaXMgY3JhZnRpbmcgYSByZXNwb25zZS4uLicsXG4gICAgXTtcbiAgICBcbiAgICAvLyBVc2Ugc2VuZGVyIG5hbWUgdG8gY29uc2lzdGVudGx5IHBpY2sgYSBtZXNzYWdlXG4gICAgY29uc3QgaGFzaCA9IHNlbmRlck5hbWUuc3BsaXQoJycpLnJlZHVjZSgoYWNjLCBjaGFyKSA9PiBhY2MgKyBjaGFyLmNoYXJDb2RlQXQoMCksIDApO1xuICAgIHJldHVybiBtZXNzYWdlc1toYXNoICUgbWVzc2FnZXMubGVuZ3RoXTtcbiAgfTtcblxuICBjb25zdCByb2xlQ29sb3IgPSBnZXRSb2xlQ29sb3Iocm9sZUlkKTtcbiAgY29uc3QgaXNGcm9tTW9kZXJhdG9yID0gIXJvbGVJZCB8fCByb2xlSWQgPT09ICdtb2RlcmF0b3InO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGp1c3RpZnktc3RhcnQgbWItNCBvcGFjaXR5LTc1XCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtc3RhcnQgc3BhY2UteC0zIG1heC13LVs4NSVdXCI+XG4gICAgICAgIHsvKiBBdmF0YXIgKi99XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleC1zaHJpbmstMCB3LTggaC04IHJvdW5kZWQtZnVsbCBiZy1ncmFkaWVudC10by1yICR7cm9sZUNvbG9yfSBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciB0ZXh0LXdoaXRlIHNoYWRvdy1zbSBhbmltYXRlLXB1bHNlYH0+XG4gICAgICAgICAge2dldFJvbGVJY29uKHJvbGVJZCl9XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBUeXBpbmcgQ29udGVudCAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLXctMFwiPlxuICAgICAgICAgIHsvKiBTZW5kZXIgSW5mbyAqL31cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMiBtYi0xXCI+XG4gICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9e2B0ZXh0LXNtIGZvbnQtc2VtaWJvbGQgJHtcbiAgICAgICAgICAgICAgaXNGcm9tTW9kZXJhdG9yID8gJ3RleHQtYmx1ZS03MDAnIDogJ3RleHQtZ3JheS03MDAnXG4gICAgICAgICAgICB9YH0+XG4gICAgICAgICAgICAgIHtzZW5kZXJOYW1lfVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC14cyB0ZXh0LWdyYXktNTAwXCI+XG4gICAgICAgICAgICAgIHtnZXRUeXBpbmdNZXNzYWdlKHNlbmRlck5hbWUpfVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgey8qIFR5cGluZyBCdWJibGUgKi99XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9e2BpbmxpbmUtYmxvY2sgcHgtNCBweS0zIHJvdW5kZWQtMnhsIHNoYWRvdy1zbSAke1xuICAgICAgICAgICAgaXNGcm9tTW9kZXJhdG9yIFxuICAgICAgICAgICAgICA/ICdiZy1ibHVlLTUwIGJvcmRlciBib3JkZXItYmx1ZS0xMDAnIFxuICAgICAgICAgICAgICA6ICdiZy1ncmF5LTUwIGJvcmRlciBib3JkZXItZ3JheS0xMDAnXG4gICAgICAgICAgfWB9PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTFcIj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IHNwYWNlLXgtMVwiPlxuICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidy0yIGgtMiBiZy1ncmF5LTQwMCByb3VuZGVkLWZ1bGwgYW5pbWF0ZS1ib3VuY2VcIiBzdHlsZT17eyBhbmltYXRpb25EZWxheTogJzBtcycgfX0gLz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctZ3JheS00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICcxNTBtcycgfX0gLz5cbiAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctMiBoLTIgYmctZ3JheS00MDAgcm91bmRlZC1mdWxsIGFuaW1hdGUtYm91bmNlXCIgc3R5bGU9e3sgYW5pbWF0aW9uRGVsYXk6ICczMDBtcycgfX0gLz5cbiAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJDcHVDaGlwSWNvbiIsIlNwYXJrbGVzSWNvbiIsIlR5cGluZ0luZGljYXRvciIsInNlbmRlck5hbWUiLCJyb2xlSWQiLCJnZXRSb2xlQ29sb3IiLCJjb2xvcnMiLCJoYXNoIiwic3BsaXQiLCJyZWR1Y2UiLCJhY2MiLCJjaGFyIiwiY2hhckNvZGVBdCIsImxlbmd0aCIsImdldFJvbGVJY29uIiwiY2xhc3NOYW1lIiwiZ2V0VHlwaW5nTWVzc2FnZSIsIm1lc3NhZ2VzIiwicm9sZUNvbG9yIiwiaXNGcm9tTW9kZXJhdG9yIiwiZGl2Iiwic3BhbiIsInN0eWxlIiwiYW5pbWF0aW9uRGVsYXkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/TypingIndicator.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/hooks/useOrchestrationStream.ts":
/*!*********************************************!*\
  !*** ./src/hooks/useOrchestrationStream.ts ***!
  \*********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLatestOrchestrationEvent: () => (/* binding */ useLatestOrchestrationEvent),\n/* harmony export */   useOrchestrationEventsByType: () => (/* binding */ useOrchestrationEventsByType),\n/* harmony export */   useOrchestrationProgress: () => (/* binding */ useOrchestrationProgress),\n/* harmony export */   useOrchestrationStream: () => (/* binding */ useOrchestrationStream)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useOrchestrationStream,useOrchestrationEventsByType,useLatestOrchestrationEvent,useOrchestrationProgress auto */ \nfunction useOrchestrationStream(executionId, directStreamUrl) {\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [lastEvent, setLastEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [streamUrl, setStreamUrl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const pollingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttempts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const maxReconnectAttempts = 5;\n    const baseReconnectDelay = 1000; // 1 second\n    // Track the current execution ID and stream URL to detect changes\n    const currentExecutionIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const currentStreamUrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationStream.useCallback[disconnect]\": ()=>{\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n            setIsConnected(false);\n        }\n    }[\"useOrchestrationStream.useCallback[disconnect]\"], []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationStream.useCallback[connect]\": ()=>{\n            if (!executionId && !directStreamUrl) {\n                setError('No execution ID or direct stream URL provided');\n                return;\n            }\n            // Determine the URL to connect to\n            const url = directStreamUrl || (executionId ? \"/api/orchestration/stream/\".concat(executionId) : '');\n            if (!url) {\n                setError('No valid stream URL could be determined');\n                return;\n            }\n            // Skip if we're already connected to this URL\n            if (currentStreamUrlRef.current === url && isConnected) {\n                console.log(\"[Orchestration Stream] Already connected to: \".concat(url));\n                return;\n            }\n            console.log(\"[Orchestration Stream] Connecting to: \".concat(url));\n            // Clean up existing connection\n            disconnect();\n            // Update refs\n            currentExecutionIdRef.current = executionId || '';\n            currentStreamUrlRef.current = url;\n            try {\n                const eventSource = new EventSource(url);\n                eventSourceRef.current = eventSource;\n                eventSource.onopen = ({\n                    \"useOrchestrationStream.useCallback[connect]\": ()=>{\n                        console.log(\"[Orchestration Stream] Connected to execution \".concat(executionId));\n                        setIsConnected(true);\n                        setError(null);\n                        reconnectAttempts.current = 0;\n                    }\n                })[\"useOrchestrationStream.useCallback[connect]\"];\n                eventSource.onmessage = ({\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        try {\n                            const orchestrationEvent = JSON.parse(event.data);\n                            console.log(\"[Orchestration Stream] Received event:\", orchestrationEvent);\n                            setEvents({\n                                \"useOrchestrationStream.useCallback[connect]\": (prev)=>[\n                                        ...prev,\n                                        orchestrationEvent\n                                    ]\n                            }[\"useOrchestrationStream.useCallback[connect]\"]);\n                            setLastEvent(orchestrationEvent);\n                            // Reset error state on successful message\n                            setError(null);\n                        } catch (parseError) {\n                            console.error('[Orchestration Stream] Error parsing event:', parseError);\n                            setError('Error parsing stream data');\n                        }\n                    }\n                })[\"useOrchestrationStream.useCallback[connect]\"];\n                // Handle specific event types\n                eventSource.addEventListener('orchestration_started', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Orchestration started:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('step_started', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Step started:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('step_progress', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Step progress:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('step_completed', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Step completed:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('synthesis_started', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Synthesis started:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('orchestration_completed', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Orchestration completed:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.onerror = ({\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        console.error('[Orchestration Stream] Connection error:', event);\n                        setIsConnected(false);\n                        // Attempt to reconnect with exponential backoff\n                        if (reconnectAttempts.current < maxReconnectAttempts) {\n                            const delay = baseReconnectDelay * Math.pow(2, reconnectAttempts.current);\n                            reconnectAttempts.current++;\n                            setError(\"Connection lost. Reconnecting in \".concat(delay / 1000, \"s... (attempt \").concat(reconnectAttempts.current, \"/\").concat(maxReconnectAttempts, \")\"));\n                            reconnectTimeoutRef.current = setTimeout({\n                                \"useOrchestrationStream.useCallback[connect]\": ()=>{\n                                    console.log(\"[Orchestration Stream] Reconnecting... (attempt \".concat(reconnectAttempts.current, \")\"));\n                                    connect();\n                                }\n                            }[\"useOrchestrationStream.useCallback[connect]\"], delay);\n                        } else {\n                            setError('Connection failed after multiple attempts. Please refresh the page.');\n                        }\n                    }\n                })[\"useOrchestrationStream.useCallback[connect]\"];\n            } catch (connectionError) {\n                console.error('[Orchestration Stream] Failed to create connection:', connectionError);\n                setError('Failed to establish connection');\n                setIsConnected(false);\n            }\n        }\n    }[\"useOrchestrationStream.useCallback[connect]\"], [\n        executionId,\n        disconnect\n    ]);\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationStream.useCallback[reconnect]\": ()=>{\n            reconnectAttempts.current = 0;\n            connect();\n        }\n    }[\"useOrchestrationStream.useCallback[reconnect]\"], [\n        connect\n    ]);\n    // Connect on mount and when executionId or directStreamUrl changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationStream.useEffect\": ()=>{\n            if (executionId || directStreamUrl) {\n                connect();\n            }\n            // Cleanup on unmount\n            return ({\n                \"useOrchestrationStream.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useOrchestrationStream.useEffect\"];\n        }\n    }[\"useOrchestrationStream.useEffect\"], [\n        executionId,\n        directStreamUrl,\n        connect,\n        disconnect\n    ]);\n    // Handle page visibility changes to reconnect when page becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationStream.useEffect\": ()=>{\n            const handleVisibilityChange = {\n                \"useOrchestrationStream.useEffect.handleVisibilityChange\": ()=>{\n                    if (document.visibilityState === 'visible' && !isConnected && (executionId || directStreamUrl)) {\n                        console.log('[Orchestration Stream] Page became visible, attempting to reconnect...');\n                        reconnect();\n                    }\n                }\n            }[\"useOrchestrationStream.useEffect.handleVisibilityChange\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            return ({\n                \"useOrchestrationStream.useEffect\": ()=>{\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                }\n            })[\"useOrchestrationStream.useEffect\"];\n        }\n    }[\"useOrchestrationStream.useEffect\"], [\n        isConnected,\n        executionId,\n        directStreamUrl,\n        reconnect\n    ]);\n    // Handle online/offline events\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationStream.useEffect\": ()=>{\n            const handleOnline = {\n                \"useOrchestrationStream.useEffect.handleOnline\": ()=>{\n                    if (!isConnected && (executionId || directStreamUrl)) {\n                        console.log('[Orchestration Stream] Network came back online, attempting to reconnect...');\n                        reconnect();\n                    }\n                }\n            }[\"useOrchestrationStream.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useOrchestrationStream.useEffect.handleOffline\": ()=>{\n                    setError('Network connection lost');\n                }\n            }[\"useOrchestrationStream.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            return ({\n                \"useOrchestrationStream.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                }\n            })[\"useOrchestrationStream.useEffect\"];\n        }\n    }[\"useOrchestrationStream.useEffect\"], [\n        isConnected,\n        executionId,\n        directStreamUrl,\n        reconnect\n    ]);\n    return {\n        events,\n        isConnected,\n        error,\n        lastEvent,\n        reconnect,\n        disconnect\n    };\n}\n// Helper hook for filtering events by type\nfunction useOrchestrationEventsByType(executionId, eventType) {\n    const { events } = useOrchestrationStream(executionId);\n    return events.filter((event)=>event.type === eventType);\n}\n// Helper hook for getting the latest event of a specific type\nfunction useLatestOrchestrationEvent(executionId, eventType) {\n    const { events } = useOrchestrationStream(executionId);\n    const filteredEvents = events.filter((event)=>event.type === eventType);\n    return filteredEvents.length > 0 ? filteredEvents[filteredEvents.length - 1] : null;\n}\n// Helper hook for tracking orchestration progress\nfunction useOrchestrationProgress(executionId) {\n    const { events } = useOrchestrationStream(executionId);\n    const stepStartedEvents = events.filter((e)=>e.type === 'step_started');\n    const stepCompletedEvents = events.filter((e)=>e.type === 'step_completed');\n    const orchestrationCompleted = events.some((e)=>e.type === 'orchestration_completed');\n    const totalSteps = stepStartedEvents.length;\n    const completedSteps = stepCompletedEvents.length;\n    const currentStep = totalSteps > 0 ? Math.min(completedSteps + 1, totalSteps) : 0;\n    const progress = totalSteps > 0 ? completedSteps / totalSteps * 100 : 0;\n    return {\n        totalSteps,\n        completedSteps,\n        currentStep,\n        progress,\n        isComplete: orchestrationCompleted\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useOrchestrationStream.ts\n"));

/***/ })

}]);