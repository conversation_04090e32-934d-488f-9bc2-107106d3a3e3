"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_src_utils_advancedCache_ts"],{

/***/ "(app-pages-browser)/./src/utils/advancedCache.ts":
/*!************************************!*\
  !*** ./src/utils/advancedCache.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AdvancedCache: () => (/* binding */ AdvancedCache),\n/* harmony export */   cachedFetch: () => (/* binding */ cachedFetch),\n/* harmony export */   globalCache: () => (/* binding */ globalCache),\n/* harmony export */   useAdvancedCache: () => (/* binding */ useAdvancedCache)\n/* harmony export */ });\n// Phase 2B: Advanced Client-Side Caching System\nclass AdvancedCache {\n    // Set cache entry with advanced options\n    set(key, data) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        const { ttl = 300000, tags = [], priority = 'medium', serialize = false } = options;\n        // Serialize data if requested (useful for complex objects)\n        const cacheData = serialize ? JSON.parse(JSON.stringify(data)) : data;\n        const entry = {\n            data: cacheData,\n            timestamp: Date.now(),\n            ttl,\n            accessCount: 0,\n            lastAccessed: Date.now(),\n            tags,\n            priority\n        };\n        // Check if cache is full and evict if necessary\n        if (this.cache.size >= this.maxSize) {\n            this.evictLeastUsed();\n        }\n        this.cache.set(key, entry);\n    }\n    // Get cache entry with access tracking\n    get(key) {\n        const entry = this.cache.get(key);\n        if (!entry) {\n            return null;\n        }\n        // Check if expired\n        if (this.isExpired(entry)) {\n            this.cache.delete(key);\n            return null;\n        }\n        // Update access statistics\n        entry.accessCount++;\n        entry.lastAccessed = Date.now();\n        return entry.data;\n    }\n    // Get with stale-while-revalidate pattern\n    getStale(key) {\n        const entry = this.cache.get(key);\n        if (!entry) {\n            return {\n                data: null,\n                isStale: false\n            };\n        }\n        const isStale = this.isExpired(entry);\n        // Update access statistics even for stale data\n        entry.accessCount++;\n        entry.lastAccessed = Date.now();\n        return {\n            data: entry.data,\n            isStale\n        };\n    }\n    // Check if entry exists and is valid\n    has(key) {\n        const entry = this.cache.get(key);\n        if (!entry) return false;\n        if (this.isExpired(entry)) {\n            this.cache.delete(key);\n            return false;\n        }\n        return true;\n    }\n    // Delete specific entry\n    delete(key) {\n        return this.cache.delete(key);\n    }\n    // Invalidate by tags\n    invalidateByTags(tags) {\n        let invalidated = 0;\n        for (const [key, entry] of this.cache.entries()){\n            if (entry.tags.some((tag)=>tags.includes(tag))) {\n                this.cache.delete(key);\n                invalidated++;\n            }\n        }\n        return invalidated;\n    }\n    // Clear all cache\n    clear() {\n        this.cache.clear();\n    }\n    // Get cache statistics\n    getStats() {\n        const entries = Array.from(this.cache.values());\n        const now = Date.now();\n        return {\n            size: this.cache.size,\n            maxSize: this.maxSize,\n            hitRate: this.calculateHitRate(),\n            averageAge: entries.reduce((sum, entry)=>sum + (now - entry.timestamp), 0) / entries.length || 0,\n            totalAccesses: entries.reduce((sum, entry)=>sum + entry.accessCount, 0),\n            expiredEntries: entries.filter((entry)=>this.isExpired(entry)).length,\n            priorityDistribution: {\n                high: entries.filter((e)=>e.priority === 'high').length,\n                medium: entries.filter((e)=>e.priority === 'medium').length,\n                low: entries.filter((e)=>e.priority === 'low').length\n            }\n        };\n    }\n    // Preload data with background refresh\n    async preload(key, fetchFn) {\n        let options = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : {};\n        // Check if we have cached data\n        const cached = this.get(key);\n        if (cached) {\n            // Return cached data immediately, but refresh in background\n            this.backgroundRefresh(key, fetchFn, options);\n            return cached;\n        }\n        // No cached data, fetch and cache\n        const data = await fetchFn();\n        this.set(key, data, options);\n        return data;\n    }\n    // Background refresh for stale-while-revalidate\n    async backgroundRefresh(key, fetchFn, options) {\n        try {\n            const freshData = await fetchFn();\n            this.set(key, freshData, options);\n        } catch (error) {\n            console.warn(\"Background refresh failed for key \".concat(key, \":\"), error);\n        }\n    }\n    // Check if entry is expired\n    isExpired(entry) {\n        return Date.now() - entry.timestamp > entry.ttl;\n    }\n    // Evict least recently used entries\n    evictLeastUsed() {\n        if (this.cache.size === 0) return;\n        // Sort by priority first, then by last accessed time\n        const entries = Array.from(this.cache.entries()).sort((param, param1)=>{\n            let [, a] = param, [, b] = param1;\n            const priorityOrder = {\n                low: 0,\n                medium: 1,\n                high: 2\n            };\n            const priorityDiff = priorityOrder[a.priority] - priorityOrder[b.priority];\n            if (priorityDiff !== 0) return priorityDiff;\n            return a.lastAccessed - b.lastAccessed;\n        });\n        // Remove the least important/oldest entry\n        const [keyToRemove] = entries[0];\n        this.cache.delete(keyToRemove);\n    }\n    // Calculate hit rate (simplified)\n    calculateHitRate() {\n        const entries = Array.from(this.cache.values());\n        const totalAccesses = entries.reduce((sum, entry)=>sum + entry.accessCount, 0);\n        return totalAccesses > 0 ? this.cache.size / totalAccesses * 100 : 0;\n    }\n    // Start automatic cleanup\n    startCleanup() {\n        this.cleanupInterval = setInterval(()=>{\n            this.cleanup();\n        }, 60000); // Clean up every minute\n    }\n    // Clean up expired entries\n    cleanup() {\n        const now = Date.now();\n        const keysToDelete = [];\n        for (const [key, entry] of this.cache.entries()){\n            if (this.isExpired(entry)) {\n                keysToDelete.push(key);\n            }\n        }\n        keysToDelete.forEach((key)=>this.cache.delete(key));\n        if (keysToDelete.length > 0) {\n            console.log(\"[Cache] Cleaned up \".concat(keysToDelete.length, \" expired entries\"));\n        }\n    }\n    // Destroy cache and cleanup\n    destroy() {\n        if (this.cleanupInterval) {\n            clearInterval(this.cleanupInterval);\n        }\n        this.cache.clear();\n    }\n    constructor(maxSize = 100){\n        this.cache = new Map();\n        this.cleanupInterval = null;\n        this.maxSize = maxSize;\n        this.startCleanup();\n    }\n}\n// Global cache instance\nconst globalCache = new AdvancedCache(200);\n// React hook for cache management\nfunction useAdvancedCache() {\n    return {\n        set: globalCache.set.bind(globalCache),\n        get: globalCache.get.bind(globalCache),\n        getStale: globalCache.getStale.bind(globalCache),\n        has: globalCache.has.bind(globalCache),\n        delete: globalCache.delete.bind(globalCache),\n        invalidateByTags: globalCache.invalidateByTags.bind(globalCache),\n        clear: globalCache.clear.bind(globalCache),\n        preload: globalCache.preload.bind(globalCache),\n        getStats: globalCache.getStats.bind(globalCache)\n    };\n}\n// Utility function for API caching\nasync function cachedFetch(url) {\n    let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n    const { cacheKey = url, cacheTTL = 300000, tags = [], ...fetchOptions } = options;\n    // Try to get from cache first\n    const cached = globalCache.get(cacheKey);\n    if (cached) {\n        return cached;\n    }\n    // Fetch from network\n    const response = await fetch(url, fetchOptions);\n    if (!response.ok) {\n        throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n    }\n    const data = await response.json();\n    // Cache the result\n    globalCache.set(cacheKey, data, {\n        ttl: cacheTTL,\n        tags,\n        priority: 'medium'\n    });\n    return data;\n}\n// Export the cache instance\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/utils/advancedCache.ts\n"));

/***/ })

}]);