# Rou<PERSON>ey Browsing Integration Test Guide

## Overview
This guide helps you test the newly integrated browsing functionality in Rou<PERSON>ey's main router.

## Prerequisites
1. ✅ Browsing configuration UI is working (completed previously)
2. ✅ At least one router configuration with browsing enabled
3. ✅ Browsing models configured with API keys
4. ✅ Browserless API keys configured in environment

## Test Scenarios

### 1. Basic Browsing Detection Test
**Query Examples that SHOULD trigger browsing:**
- "What's the current price of Bitcoin?"
- "What's happening in the news today?"
- "Check the weather in New York"
- "What are the latest developments in AI?"
- "Find current job openings at Google"

**Query Examples that should NOT trigger browsing:**
- "What is machine learning?"
- "Help me write a poem"
- "Explain quantum physics"
- "What's 2+2?"

### 2. Browsing Model Fallback Test
**Setup:**
1. Configure multiple browsing models in order (e.g., OpenAI GPT-4, then Anthropic Claude)
2. Temporarily use invalid API key for first model
3. Send browsing query

**Expected Result:**
- First model fails with API error
- System automatically tries second model
- Response generated using second model
- Logs show fallback sequence

### 3. Browsing Integration Test
**Test Query:** "What's the current stock price of Apple?"

**Expected Flow:**
1. Router detects browsing is enabled
2. AI classifier determines query needs browsing
3. BrowserlessService searches for Apple stock price
4. Browsing model processes the web results
5. Response includes current, real-time stock information

## Monitoring & Debugging

### Key Log Messages to Watch For:
```
[Smart Browsing] Query requires browsing: "What's the current..."
[Smart Browsing] Browsing type: search, Confidence: 0.9
[Browsing Execution] Starting browsing with 2 models configured
[Browsing Execution] ✅ Success with openai/gpt-4
[Smart Browsing] ✅ Browsing successful with openai/gpt-4
[Browsing Enhancement] Injected 1234 characters of browsing context
```

### Error Scenarios to Test:
1. **No browsing models configured** → Should skip browsing gracefully
2. **All browsing models fail** → Should continue with normal routing
3. **Browserless API failure** → Should handle gracefully with error logging
4. **Invalid browsing configuration** → Should validate and skip

## Performance Testing

### Expected Performance Impact:
- **Browsing queries:** +2-5 seconds (due to web browsing)
- **Non-browsing queries:** No impact (parallel processing)
- **Memory usage:** Minimal increase

### Optimization Features:
- Parallel processing (browsing runs alongside RAG and routing)
- Smart detection (only browses when necessary)
- Fallback models (ensures reliability)
- Timeout handling (prevents hanging requests)

## Configuration Verification

### Database Check:
```sql
-- Verify browsing configuration
SELECT id, name, browsing_enabled, browsing_models 
FROM custom_api_configs 
WHERE browsing_enabled = true;
```

### Environment Variables:
- `ROKEY_CLASSIFICATION_GEMINI_API_KEY` (for browsing detection)
- Browserless API keys (already configured)

## Troubleshooting

### Common Issues:
1. **"Browsing disabled"** → Check `browsing_enabled` in database
2. **"No browsing models configured"** → Add models via UI
3. **"Classification failed"** → Check Gemini API key
4. **"All browsing models failed"** → Verify API keys and model names

### Debug Mode:
Enable detailed logging by checking terminal output for browsing-related messages.

## Success Criteria
✅ Browsing detection works correctly
✅ Web browsing executes successfully  
✅ Model fallback functions properly
✅ Context injection enhances responses
✅ Non-browsing queries unaffected
✅ Error handling works gracefully

## Next Steps After Testing
1. Monitor performance in production
2. Adjust browsing detection sensitivity if needed
3. Add more browsing model providers if desired
4. Consider caching browsing results for similar queries
