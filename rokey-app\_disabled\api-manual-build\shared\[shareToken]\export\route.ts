/**
 * API endpoint for exporting shared workflows
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { workflowSharingService } from '@/lib/workflow/WorkflowSharingService';

interface RouteParams {
  params: Promise<{
    shareToken: string;
  }>;
}

/**
 * GET /api/manual-build/shared/[shareToken]/export
 * Export shared workflow as downloadable file
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { shareToken } = await params;
  
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Get user (optional for public shares)
    const { data: { user } } = await supabase.auth.getUser();
    
    // Get shared workflow
    const { workflow, permissions } = await workflowSharingService.getSharedWorkflow(
      shareToken,
      user?.id
    );

    // Check export permission
    if (!permissions.canExport) {
      return NextResponse.json(
        { error: 'Export not allowed' },
        { status: 403 }
      );
    }

    // Export workflow
    const exportData = await workflowSharingService.exportWorkflow(
      workflow.id,
      workflow.user_id,
      'json'
    );

    // Return as downloadable file
    const fileName = `${workflow.name.replace(/[^a-zA-Z0-9]/g, '_')}.json`;
    
    return new Response(JSON.stringify(exportData, null, 2), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${fileName}"`,
      },
    });

  } catch (error) {
    console.error('Export shared workflow error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}
