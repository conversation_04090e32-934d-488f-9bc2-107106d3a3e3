/**
 * API endpoints for workflow sharing
 * <PERSON>les creating, listing, and managing workflow shares
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { workflowSharingService } from '@/lib/workflow/WorkflowSharingService';

interface RouteParams {
  params: Promise<{
    workflowId: string;
  }>;
}

/**
 * GET /api/manual-build/workflows/[workflowId]/shares
 * Get all shares for a workflow
 */
export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { workflowId } = await params;
  
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get shares for the workflow
    const shares = await workflowSharingService.getWorkflowShares(workflowId, user.id);

    return NextResponse.json({
      shares,
      total: shares.length
    });

  } catch (error) {
    console.error('Get workflow shares error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/manual-build/workflows/[workflowId]/shares
 * Create a new share for a workflow
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  const { workflowId } = await params;
  
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const {
      sharedWith,
      permissionLevel,
      isPublic,
      expiresAt
    } = body;

    // Validate required fields
    if (!permissionLevel || (!sharedWith && !isPublic)) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Create shares
    const shares = await workflowSharingService.shareWorkflow(
      workflowId,
      user.id,
      {
        sharedWith,
        permissionLevel,
        isPublic,
        expiresAt: expiresAt ? new Date(expiresAt) : undefined
      }
    );

    return NextResponse.json({
      shares,
      message: 'Workflow shared successfully'
    });

  } catch (error) {
    console.error('Create workflow share error:', error);
    return NextResponse.json(
      { error: error instanceof Error ? error.message : 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/manual-build/workflows/[workflowId]/shares/[shareId]
 * Revoke a specific share
 */
export async function DELETE(
  request: NextRequest,
  context: { params: Promise<{ workflowId: string; shareId: string }> }
) {
  const { workflowId, shareId } = await context.params;
  
  try {
    const supabase = await createSupabaseServerClientOnRequest();
    
    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Revoke the share
    await workflowSharingService.revokeShare(shareId, user.id);

    return NextResponse.json({
      message: 'Share revoked successfully'
    });

  } catch (error) {
    console.error('Revoke workflow share error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
