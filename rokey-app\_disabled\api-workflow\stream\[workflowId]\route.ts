/**
 * WebSocket API endpoint for real-time workflow updates
 * Provides Server-Sent Events stream for Manual Build workflow execution
 */

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';
import { createWorkflowEventStream, emitWorkflowEvent } from '@/lib/websocket/WorkflowWebSocketServer';

interface RouteParams {
  params: Promise<{
    workflowId: string;
  }>;
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  const { workflowId } = await params;
  
  if (!workflowId) {
    return NextResponse.json(
      { error: 'Workflow ID is required' },
      { status: 400 }
    );
  }

  try {
    // Authenticate user
    const supabase = await createSupabaseServerClientOnRequest();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Verify user has access to this workflow
    const { data: workflow, error: workflowError } = await supabase
      .from('manual_build_workflows')
      .select('id, user_id, name, is_active')
      .eq('id', workflowId)
      .eq('user_id', user.id)
      .single();

    if (workflowError || !workflow) {
      return NextResponse.json(
        { error: 'Workflow not found or access denied' },
        { status: 404 }
      );
    }

    console.log(`[Workflow Stream] Starting stream for workflow ${workflowId} (${workflow.name})`);

    // Create the event stream
    const stream = createWorkflowEventStream(workflowId, user.id);

    // Send initial workflow status
    setTimeout(() => {
      emitWorkflowEvent(
        workflowId,
        user.id,
        'connection_established',
        {
          message: `🔗 Connected to workflow: ${workflow.name}`,
          workflow: {
            id: workflow.id,
            name: workflow.name,
            isActive: workflow.is_active
          },
          timestamp: new Date().toISOString()
        }
      );
    }, 100);

    // Return SSE response
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control',
        'X-Accel-Buffering': 'no'
      },
    });

  } catch (error) {
    console.error(`[Workflow Stream] Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * POST endpoint for sending workflow events (for testing or external triggers)
 */
export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  const { workflowId } = await params;
  
  if (!workflowId) {
    return NextResponse.json(
      { error: 'Workflow ID is required' },
      { status: 400 }
    );
  }

  try {
    // Authenticate user
    const supabase = await createSupabaseServerClientOnRequest();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { eventType, data, executionId } = body;

    if (!eventType) {
      return NextResponse.json(
        { error: 'Event type is required' },
        { status: 400 }
      );
    }

    // Verify user has access to this workflow
    const { data: workflow, error: workflowError } = await supabase
      .from('manual_build_workflows')
      .select('id, user_id')
      .eq('id', workflowId)
      .eq('user_id', user.id)
      .single();

    if (workflowError || !workflow) {
      return NextResponse.json(
        { error: 'Workflow not found or access denied' },
        { status: 404 }
      );
    }

    // Emit the event
    emitWorkflowEvent(workflowId, user.id, eventType, data, executionId);

    return NextResponse.json({
      success: true,
      message: 'Event emitted successfully',
      eventType,
      workflowId
    });

  } catch (error) {
    console.error(`[Workflow Stream] POST Error: ${error}`);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS endpoint for CORS preflight
 */
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
