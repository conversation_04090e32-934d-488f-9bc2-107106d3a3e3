// Browsing streaming utilities for progress updates
import { BrowsingPlan, BrowsingSubtask } from '@/lib/browsing/SmartBrowsingExecutor';

export interface BrowsingProgressEvent {
  object: 'browsing.progress';
  data: {
    plan?: BrowsingPlan;
    status?: string;
    event_type: 'plan_created' | 'task_started' | 'task_completed' | 'task_failed' | 'progress_update' | 'status_update' | 'plan_completed';
    task?: BrowsingSubtask;
    progress?: number;
  };
}

/**
 * Create a browsing progress event in the streaming format
 */
export function createBrowsingProgressEvent(
  eventType: BrowsingProgressEvent['data']['event_type'],
  plan?: BrowsingPlan,
  status?: string,
  task?: BrowsingSubtask,
  progress?: number
): BrowsingProgressEvent {
  return {
    object: 'browsing.progress',
    data: {
      event_type: eventType,
      plan,
      status,
      task,
      progress
    }
  };
}

/**
 * Format browsing progress event as SSE data
 */
export function formatBrowsingProgressSSE(event: BrowsingProgressEvent): string {
  return `data: ${JSON.stringify(event)}\n\n`;
}

/**
 * Create a browsing progress callback that emits SSE events
 */
export function createBrowsingProgressSSECallback(
  controller: ReadableStreamDefaultController<Uint8Array>
) {
  const encoder = new TextEncoder();

  return {
    onPlanCreated: (plan: BrowsingPlan) => {
      const event = createBrowsingProgressEvent('plan_created', plan);
      const sseData = formatBrowsingProgressSSE(event);
      controller.enqueue(encoder.encode(sseData));
      console.log('🌐 [BROWSING SSE] Plan created event sent');
    },

    onTaskStarted: (task: BrowsingSubtask, plan: BrowsingPlan) => {
      const event = createBrowsingProgressEvent('task_started', plan, undefined, task);
      const sseData = formatBrowsingProgressSSE(event);
      controller.enqueue(encoder.encode(sseData));
      console.log(`🌐 [BROWSING SSE] Task started: ${task.description}`);
    },

    onTaskCompleted: (task: BrowsingSubtask, plan: BrowsingPlan) => {
      const event = createBrowsingProgressEvent('task_completed', plan, undefined, task);
      const sseData = formatBrowsingProgressSSE(event);
      controller.enqueue(encoder.encode(sseData));
      console.log(`🌐 [BROWSING SSE] Task completed: ${task.description}`);
    },

    onTaskFailed: (task: BrowsingSubtask, plan: BrowsingPlan) => {
      const event = createBrowsingProgressEvent('task_failed', plan, undefined, task);
      const sseData = formatBrowsingProgressSSE(event);
      controller.enqueue(encoder.encode(sseData));
      console.log(`🌐 [BROWSING SSE] Task failed: ${task.description}`);
    },

    onProgressUpdate: (progress: number, plan: BrowsingPlan) => {
      const event = createBrowsingProgressEvent('progress_update', plan, undefined, undefined, progress);
      const sseData = formatBrowsingProgressSSE(event);
      controller.enqueue(encoder.encode(sseData));
      console.log(`🌐 [BROWSING SSE] Progress update: ${progress}%`);
    },

    onStatusUpdate: (status: string, plan: BrowsingPlan) => {
      const event = createBrowsingProgressEvent('status_update', plan, status);
      const sseData = formatBrowsingProgressSSE(event);
      controller.enqueue(encoder.encode(sseData));
      console.log(`🌐 [BROWSING SSE] Status update: ${status}`);
    },

    onPlanCompleted: (plan: BrowsingPlan) => {
      const event = createBrowsingProgressEvent('plan_completed', plan);
      const sseData = formatBrowsingProgressSSE(event);
      controller.enqueue(encoder.encode(sseData));
      console.log('🌐 [BROWSING SSE] Plan completed event sent');
    }
  };
}
