'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { 
  ExclamationTriangleIcon, 
  ArrowPathIcon, 
  DocumentTextIcon,
  BugAntIcon 
} from '@heroicons/react/24/outline';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
  showDetails?: boolean;
}

interface State {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
  errorId: string;
}

export default class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    };
  }

  static getDerivedStateFromError(error: Error): Partial<State> {
    return {
      hasError: true,
      error,
      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    this.setState({
      error,
      errorInfo
    });

    // Log error details
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Call custom error handler if provided
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }

    // Send error to monitoring service (if available)
    this.reportError(error, errorInfo);
  }

  private reportError = async (error: Error, errorInfo: ErrorInfo) => {
    try {
      // Report to error tracking service
      const errorReport = {
        message: error.message,
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        errorId: this.state.errorId,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      };

      // In a real app, you'd send this to your error tracking service
      console.log('Error report:', errorReport);
      
    } catch (reportingError) {
      console.error('Failed to report error:', reportingError);
    }
  };

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
      errorId: ''
    });
  };

  private handleReload = () => {
    window.location.reload();
  };

  private copyErrorDetails = () => {
    const errorDetails = {
      error: this.state.error?.message,
      stack: this.state.error?.stack,
      componentStack: this.state.errorInfo?.componentStack,
      errorId: this.state.errorId,
      timestamp: new Date().toISOString()
    };

    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2))
      .then(() => {
        alert('Error details copied to clipboard');
      })
      .catch(() => {
        alert('Failed to copy error details');
      });
  };

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Default error UI
      return (
        <div className="min-h-screen bg-[#040716] flex items-center justify-center p-6">
          <div className="max-w-2xl w-full">
            <div className="bg-red-900/20 border border-red-700/50 rounded-lg p-8">
              {/* Error Icon */}
              <div className="flex items-center justify-center mb-6">
                <div className="p-4 bg-red-900/30 rounded-full">
                  <ExclamationTriangleIcon className="w-12 h-12 text-red-400" />
                </div>
              </div>

              {/* Error Title */}
              <div className="text-center mb-6">
                <h1 className="text-2xl font-bold text-white mb-2">
                  Something went wrong
                </h1>
                <p className="text-gray-400">
                  An unexpected error occurred in the workflow editor. 
                  We've been notified and are working to fix this issue.
                </p>
              </div>

              {/* Error ID */}
              <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
                <div className="flex items-center gap-2 text-sm">
                  <BugAntIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-gray-400">Error ID:</span>
                  <code className="text-gray-300 font-mono">{this.state.errorId}</code>
                </div>
              </div>

              {/* Error Message */}
              {this.state.error && (
                <div className="bg-gray-800/50 rounded-lg p-4 mb-6">
                  <h3 className="text-sm font-medium text-gray-300 mb-2">Error Message:</h3>
                  <p className="text-red-300 text-sm font-mono">
                    {this.state.error.message}
                  </p>
                </div>
              )}

              {/* Error Details (if enabled) */}
              {this.props.showDetails && this.state.error && (
                <details className="mb-6">
                  <summary className="text-gray-300 cursor-pointer hover:text-white transition-colors">
                    Technical Details
                  </summary>
                  <div className="mt-4 bg-gray-900/50 rounded-lg p-4">
                    <div className="text-xs font-mono text-gray-400 whitespace-pre-wrap overflow-auto max-h-40">
                      {this.state.error.stack}
                    </div>
                  </div>
                </details>
              )}

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-3">
                <button
                  onClick={this.handleRetry}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-[#ff6b35] text-white rounded-lg hover:bg-[#ff6b35]/80 transition-colors"
                >
                  <ArrowPathIcon className="w-5 h-5" />
                  Try Again
                </button>
                
                <button
                  onClick={this.handleReload}
                  className="flex-1 flex items-center justify-center gap-2 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  <ArrowPathIcon className="w-5 h-5" />
                  Reload Page
                </button>

                <button
                  onClick={this.copyErrorDetails}
                  className="flex items-center justify-center gap-2 px-4 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
                >
                  <DocumentTextIcon className="w-5 h-5" />
                  Copy Details
                </button>
              </div>

              {/* Help Text */}
              <div className="mt-6 text-center">
                <p className="text-sm text-gray-400">
                  If this problem persists, please contact support with the error ID above.
                </p>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Higher-order component for easier usage
export function withErrorBoundary<P extends object>(
  Component: React.ComponentType<P>,
  errorBoundaryProps?: Omit<Props, 'children'>
) {
  const WrappedComponent = (props: P) => (
    <ErrorBoundary {...errorBoundaryProps}>
      <Component {...props} />
    </ErrorBoundary>
  );

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

// Hook for error reporting in functional components
export function useErrorHandler() {
  const reportError = (error: Error, context?: string) => {
    console.error('Manual error report:', error, context);
    
    // In a real app, you'd send this to your error tracking service
    const errorReport = {
      message: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    console.log('Error report:', errorReport);
  };

  return { reportError };
}
