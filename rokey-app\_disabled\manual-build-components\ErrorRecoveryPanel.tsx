'use client';

import { useState, useEffect } from 'react';
import { 
  ExclamationTriangleIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  InformationCircleIcon,
  ChevronDownIcon,
  ChevronUpIcon
} from '@heroicons/react/24/outline';

interface ErrorRecoveryPanelProps {
  errors: WorkflowError[];
  onRetry: (errorId: string) => void;
  onSkip: (errorId: string) => void;
  onManualFix: (errorId: string) => void;
  isVisible: boolean;
  onClose: () => void;
}

interface WorkflowError {
  id: string;
  nodeId: string;
  nodeType: string;
  nodeLabel: string;
  message: string;
  timestamp: string;
  attempt: number;
  maxRetries: number;
  status: 'pending' | 'retrying' | 'recovered' | 'failed' | 'skipped';
  recoveryStrategies: RecoveryStrategy[];
  context?: any;
}

interface RecoveryStrategy {
  type: 'retry' | 'fallback' | 'skip' | 'manual' | 'alternative_path';
  description: string;
  available: boolean;
  recommended: boolean;
}

export default function ErrorRecoveryPanel({
  errors,
  onRetry,
  onSkip,
  onManualFix,
  isVisible,
  onClose
}: ErrorRecoveryPanelProps) {
  const [expandedErrors, setExpandedErrors] = useState<Set<string>>(new Set());
  const [selectedStrategy, setSelectedStrategy] = useState<Record<string, string>>({});

  const toggleErrorExpansion = (errorId: string) => {
    const newExpanded = new Set(expandedErrors);
    if (newExpanded.has(errorId)) {
      newExpanded.delete(errorId);
    } else {
      newExpanded.add(errorId);
    }
    setExpandedErrors(newExpanded);
  };

  const getStatusIcon = (status: WorkflowError['status']) => {
    switch (status) {
      case 'pending':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400" />;
      case 'retrying':
        return <ClockIcon className="w-5 h-5 text-blue-400 animate-spin" />;
      case 'recovered':
        return <CheckCircleIcon className="w-5 h-5 text-green-400" />;
      case 'failed':
        return <XCircleIcon className="w-5 h-5 text-red-400" />;
      case 'skipped':
        return <InformationCircleIcon className="w-5 h-5 text-gray-400" />;
      default:
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400" />;
    }
  };

  const getStatusColor = (status: WorkflowError['status']) => {
    switch (status) {
      case 'pending':
        return 'border-yellow-500 bg-yellow-900/20';
      case 'retrying':
        return 'border-blue-500 bg-blue-900/20';
      case 'recovered':
        return 'border-green-500 bg-green-900/20';
      case 'failed':
        return 'border-red-500 bg-red-900/20';
      case 'skipped':
        return 'border-gray-500 bg-gray-900/20';
      default:
        return 'border-yellow-500 bg-yellow-900/20';
    }
  };

  const handleStrategyAction = (errorId: string, strategyType: string) => {
    switch (strategyType) {
      case 'retry':
        onRetry(errorId);
        break;
      case 'skip':
        onSkip(errorId);
        break;
      case 'manual':
        onManualFix(errorId);
        break;
      default:
        console.log(`Strategy ${strategyType} not implemented`);
    }
  };

  if (!isVisible || errors.length === 0) {
    return null;
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-[70vh] bg-gray-800 border border-gray-700 rounded-lg shadow-xl overflow-hidden z-50">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-700 bg-gray-900/50">
        <div className="flex items-center gap-2">
          <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400" />
          <h3 className="text-lg font-semibold text-white">
            Error Recovery ({errors.length})
          </h3>
        </div>
        <button
          onClick={onClose}
          className="text-gray-400 hover:text-white transition-colors"
        >
          ✕
        </button>
      </div>

      {/* Error List */}
      <div className="overflow-y-auto max-h-[calc(70vh-80px)]">
        {errors.map((error) => (
          <div
            key={error.id}
            className={`border-l-4 ${getStatusColor(error.status)} m-2 rounded-r-lg`}
          >
            {/* Error Summary */}
            <div
              className="p-4 cursor-pointer hover:bg-gray-700/30 transition-colors"
              onClick={() => toggleErrorExpansion(error.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-3 flex-1">
                  {getStatusIcon(error.status)}
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <span className="text-sm font-medium text-white">
                        {error.nodeLabel}
                      </span>
                      <span className="text-xs text-gray-400 bg-gray-700 px-2 py-0.5 rounded">
                        {error.nodeType}
                      </span>
                    </div>
                    <p className="text-sm text-gray-300 truncate">
                      {error.message}
                    </p>
                    <div className="flex items-center gap-4 mt-2 text-xs text-gray-400">
                      <span>Attempt {error.attempt}/{error.maxRetries}</span>
                      <span>{new Date(error.timestamp).toLocaleTimeString()}</span>
                    </div>
                  </div>
                </div>
                <div className="ml-2">
                  {expandedErrors.has(error.id) ? (
                    <ChevronUpIcon className="w-4 h-4 text-gray-400" />
                  ) : (
                    <ChevronDownIcon className="w-4 h-4 text-gray-400" />
                  )}
                </div>
              </div>
            </div>

            {/* Error Details */}
            {expandedErrors.has(error.id) && (
              <div className="px-4 pb-4 border-t border-gray-700/50">
                {/* Full Error Message */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-300 mb-2">Error Details:</h4>
                  <div className="bg-gray-900/50 rounded p-3 text-sm text-gray-300 font-mono">
                    {error.message}
                  </div>
                </div>

                {/* Recovery Strategies */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-gray-300 mb-2">Recovery Options:</h4>
                  <div className="space-y-2">
                    {error.recoveryStrategies.map((strategy, index) => (
                      <div
                        key={index}
                        className={`p-3 rounded border ${
                          strategy.available
                            ? strategy.recommended
                              ? 'border-green-500/50 bg-green-900/20'
                              : 'border-gray-600 bg-gray-800/50'
                            : 'border-gray-700 bg-gray-900/50 opacity-50'
                        }`}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="text-sm font-medium text-white capitalize">
                                {strategy.type.replace('_', ' ')}
                              </span>
                              {strategy.recommended && (
                                <span className="text-xs bg-green-900/30 text-green-300 px-2 py-0.5 rounded">
                                  Recommended
                                </span>
                              )}
                            </div>
                            <p className="text-xs text-gray-400">
                              {strategy.description}
                            </p>
                          </div>
                          {strategy.available && error.status === 'pending' && (
                            <button
                              onClick={() => handleStrategyAction(error.id, strategy.type)}
                              className={`ml-3 px-3 py-1 text-xs rounded transition-colors ${
                                strategy.recommended
                                  ? 'bg-green-600 text-white hover:bg-green-500'
                                  : 'bg-gray-600 text-white hover:bg-gray-500'
                              }`}
                            >
                              Apply
                            </button>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Context Information */}
                {error.context && (
                  <div>
                    <h4 className="text-sm font-medium text-gray-300 mb-2">Context:</h4>
                    <div className="bg-gray-900/50 rounded p-3 text-xs text-gray-400 font-mono max-h-20 overflow-y-auto">
                      {JSON.stringify(error.context, null, 2)}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Footer Actions */}
      <div className="p-4 border-t border-gray-700 bg-gray-900/50">
        <div className="flex gap-2">
          <button
            onClick={() => errors.forEach(error => error.status === 'pending' && onRetry(error.id))}
            className="flex-1 px-3 py-2 bg-[#ff6b35] text-white text-sm rounded hover:bg-[#ff6b35]/80 transition-colors"
            disabled={!errors.some(error => error.status === 'pending')}
          >
            <ArrowPathIcon className="w-4 h-4 inline mr-1" />
            Retry All
          </button>
          <button
            onClick={() => errors.forEach(error => error.status === 'pending' && onSkip(error.id))}
            className="flex-1 px-3 py-2 bg-gray-600 text-white text-sm rounded hover:bg-gray-500 transition-colors"
            disabled={!errors.some(error => error.status === 'pending')}
          >
            Skip All
          </button>
        </div>
      </div>
    </div>
  );
}
