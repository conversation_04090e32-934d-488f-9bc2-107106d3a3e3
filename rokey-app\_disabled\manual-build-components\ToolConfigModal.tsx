'use client';

import { useState, useEffect } from 'react';
import Image from 'next/image';
import { Dialog, Transition, Listbox } from '@headlessui/react';
import { Fragment } from 'react';
import { XMarkIcon, LinkIcon, CheckCircleIcon, ExclamationTriangleIcon, ChevronUpDownIcon, CheckIcon } from '@heroicons/react/24/outline';
import { TOOL_DISPLAY_NAMES, TOOL_ICONS, TOOL_EMOJIS, TOOL_DESCRIPTIONS } from '@/lib/oauth/config';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';

interface ToolConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  nodeId: string;
  currentConfig: any;
  onConfigUpdate: (config: any) => void;
}

interface ToolStatus {
  tool_type: string;
  display_name: string;
  icon: string;
  description: string;
  is_connected: boolean;
  connection_status: string;
  provider_user_email?: string;
  provider_user_name?: string;
}

export default function ToolConfigModal({
  isOpen,
  onClose,
  nodeId,
  currentConfig,
  onConfigUpdate
}: ToolConfigModalProps) {
  const [selectedTool, setSelectedTool] = useState(currentConfig?.toolType || '');
  const [toolStatus, setToolStatus] = useState<ToolStatus | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [timeout, setTimeoutValue] = useState(currentConfig?.timeout || 30);
  const supabase = createSupabaseBrowserClient();

  // Available tools (excluding zapier as per user request)
  const availableTools = Object.keys(TOOL_DISPLAY_NAMES).filter(tool => tool !== 'zapier');

  // Fetch tool connection status
  const fetchToolStatus = async (toolType: string) => {
    if (!toolType) return;
    
    try {
      const response = await fetch(`/api/auth/tools/status?tool=${toolType}`);
      if (response.ok) {
        const status = await response.json();
        setToolStatus(status);
      }
    } catch (error) {
      console.error('Error fetching tool status:', error);
    }
  };

  // Handle tool selection change
  const handleToolChange = (toolType: string) => {
    setSelectedTool(toolType);
    setError(null);
    fetchToolStatus(toolType);
  };

  // Handle account linking
  const handleLinkAccount = async () => {
    if (!selectedTool) return;
    
    setIsConnecting(true);
    setError(null);
    
    try {
      // Determine the OAuth provider based on tool type
      let provider = 'google';
      if (selectedTool === 'notion') {
        provider = 'notion';
      }
      
      // Get authorization URL
      const response = await fetch(`/api/auth/tools/${provider}/authorize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          toolType: selectedTool,
          returnUrl: window.location.pathname
        })
      });
      
      if (!response.ok) {
        throw new Error('Failed to initiate OAuth flow');
      }
      
      const { authUrl } = await response.json();
      
      // Open OAuth flow in new window
      const popup = window.open(
        authUrl,
        'oauth-popup',
        'width=600,height=700,scrollbars=yes,resizable=yes'
      );
      
      // Listen for OAuth completion
      const checkClosed = setInterval(() => {
        if (popup?.closed) {
          clearInterval(checkClosed);
          setIsConnecting(false);
          // Refresh tool status
          if (selectedTool) {
            setTimeout(() => fetchToolStatus(selectedTool), 1000);
          }
        }
      }, 1000);
      
    } catch (error) {
      console.error('Error linking account:', error);
      setError(error instanceof Error ? error.message : 'Failed to link account');
      setIsConnecting(false);
    }
  };

  // Handle disconnect
  const handleDisconnect = async () => {
    if (!selectedTool) return;
    
    try {
      const response = await fetch(`/api/auth/tools/status?tool=${selectedTool}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        setToolStatus(null);
        fetchToolStatus(selectedTool);
      }
    } catch (error) {
      console.error('Error disconnecting tool:', error);
      setError('Failed to disconnect tool');
    }
  };

  // Save configuration
  const handleSave = () => {
    if (!selectedTool) {
      setError('Please select a tool');
      return;
    }
    
    const newConfig = {
      toolType: selectedTool,
      timeout,
      connectionStatus: toolStatus?.is_connected ? 'connected' : 'disconnected',
      isAuthenticated: toolStatus?.is_connected || false,
      providerUserEmail: toolStatus?.provider_user_email,
      providerUserName: toolStatus?.provider_user_name,
      toolConfig: {}
    };
    
    onConfigUpdate(newConfig);
    onClose();
  };

  // Load tool status on modal open
  useEffect(() => {
    if (isOpen && selectedTool) {
      fetchToolStatus(selectedTool);
    }
  }, [isOpen, selectedTool]);

  return (
    <Transition appear show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={onClose}>
        <Transition.Child
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-y-auto">
          <div className="flex min-h-full items-center justify-center p-4 text-center">
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-gray-800 p-6 text-left align-middle shadow-xl transition-all border border-gray-700">
                <div className="flex items-center justify-between mb-4">
                  <Dialog.Title as="h3" className="text-lg font-medium leading-6 text-white">
                    Configure Tool
                  </Dialog.Title>
                  <button
                    onClick={onClose}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    <XMarkIcon className="h-5 w-5" />
                  </button>
                </div>

                <div className="space-y-4">
                  {/* Tool Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Select Tool
                    </label>
                    <Listbox value={selectedTool} onChange={handleToolChange}>
                      <div className="relative">
                        <Listbox.Button className="relative w-full cursor-pointer rounded-md bg-gray-700 border border-gray-600 py-2 pl-3 pr-10 text-left text-white focus:outline-none focus:ring-2 focus:ring-cyan-500 focus:border-transparent">
                          <span className="flex items-center">
                            {selectedTool && TOOL_ICONS[selectedTool] ? (
                              <Image
                                src={TOOL_ICONS[selectedTool]}
                                alt=""
                                width={20}
                                height={20}
                                className="mr-3 h-5 w-5 flex-shrink-0 rounded-sm"
                              />
                            ) : null}
                            <span className="block truncate">
                              {selectedTool ? TOOL_DISPLAY_NAMES[selectedTool] : 'Choose a tool...'}
                            </span>
                          </span>
                          <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                            <ChevronUpDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                          </span>
                        </Listbox.Button>

                        <Transition
                          as={Fragment}
                          leave="transition ease-in duration-100"
                          leaveFrom="opacity-100"
                          leaveTo="opacity-0"
                        >
                          <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-gray-700 border border-gray-600 py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                            <Listbox.Option
                              className={({ active }) =>
                                `relative cursor-pointer select-none py-2 pl-3 pr-9 ${
                                  active ? 'bg-cyan-500 text-white' : 'text-gray-300'
                                }`
                              }
                              value=""
                            >
                              {({ selected, active }) => (
                                <>
                                  <div className="flex items-center">
                                    <div className="mr-3 h-5 w-5 flex-shrink-0" />
                                    <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                                      Choose a tool...
                                    </span>
                                  </div>
                                  {selected ? (
                                    <span className={`absolute inset-y-0 right-0 flex items-center pr-4 ${active ? 'text-white' : 'text-cyan-500'}`}>
                                      <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                    </span>
                                  ) : null}
                                </>
                              )}
                            </Listbox.Option>
                            {availableTools.map((tool) => (
                              <Listbox.Option
                                key={tool}
                                className={({ active }) =>
                                  `relative cursor-pointer select-none py-2 pl-3 pr-9 ${
                                    active ? 'bg-cyan-500 text-white' : 'text-gray-300'
                                  }`
                                }
                                value={tool}
                              >
                                {({ selected, active }) => (
                                  <>
                                    <div className="flex items-center">
                                      {TOOL_ICONS[tool] ? (
                                        <Image
                                          src={TOOL_ICONS[tool]}
                                          alt=""
                                          width={20}
                                          height={20}
                                          className="mr-3 h-5 w-5 flex-shrink-0 rounded-sm"
                                        />
                                      ) : (
                                        <div className="mr-3 h-5 w-5 flex-shrink-0" />
                                      )}
                                      <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                                        {TOOL_DISPLAY_NAMES[tool]}
                                      </span>
                                    </div>

                                    {selected ? (
                                      <span className={`absolute inset-y-0 right-0 flex items-center pr-4 ${active ? 'text-white' : 'text-cyan-500'}`}>
                                        <CheckIcon className="h-5 w-5" aria-hidden="true" />
                                      </span>
                                    ) : null}
                                  </>
                                )}
                              </Listbox.Option>
                            ))}
                          </Listbox.Options>
                        </Transition>
                      </div>
                    </Listbox>
                  </div>

                  {/* Tool Description */}
                  {selectedTool && (
                    <div className="p-3 bg-gray-700 rounded-md">
                      <p className="text-sm text-gray-300">
                        {TOOL_DESCRIPTIONS[selectedTool]}
                      </p>
                    </div>
                  )}

                  {/* Connection Status */}
                  {selectedTool && toolStatus && (
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-gray-700 rounded-md">
                        <div className="flex items-center gap-2">
                          {toolStatus.icon && toolStatus.icon.startsWith('http') ? (
                            <Image
                              src={toolStatus.icon}
                              alt={toolStatus.display_name}
                              width={20}
                              height={20}
                              className="rounded-sm"
                            />
                          ) : (
                            <span className="text-lg">{toolStatus.icon || '🔧'}</span>
                          )}
                          <div>
                            <div className="text-sm font-medium text-white">
                              {toolStatus.display_name}
                            </div>
                            {toolStatus.provider_user_email && (
                              <div className="text-xs text-gray-400">
                                {toolStatus.provider_user_email}
                              </div>
                            )}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          {toolStatus.is_connected ? (
                            <CheckCircleIcon className="h-5 w-5 text-green-400" />
                          ) : (
                            <ExclamationTriangleIcon className="h-5 w-5 text-yellow-400" />
                          )}
                          <span className={`text-xs ${
                            toolStatus.is_connected ? 'text-green-400' : 'text-yellow-400'
                          }`}>
                            {toolStatus.is_connected ? 'Connected' : 'Not Connected'}
                          </span>
                        </div>
                      </div>

                      {/* Connection Actions */}
                      <div className="flex gap-2">
                        {toolStatus.is_connected ? (
                          <button
                            onClick={handleDisconnect}
                            className="flex-1 px-3 py-2 bg-red-600 hover:bg-red-700 text-white text-sm rounded-md transition-colors"
                          >
                            Disconnect
                          </button>
                        ) : (
                          <button
                            onClick={handleLinkAccount}
                            disabled={isConnecting}
                            className="flex-1 px-3 py-2 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white text-sm rounded-md transition-colors flex items-center justify-center gap-2"
                          >
                            <LinkIcon className="h-4 w-4" />
                            {isConnecting ? 'Connecting...' : 'Link Account'}
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Timeout Setting */}
                  {selectedTool && (
                    <div>
                      <label className="block text-sm font-medium text-gray-300 mb-2">
                        Timeout (seconds)
                      </label>
                      <input
                        type="number"
                        min="5"
                        max="300"
                        value={timeout}
                        onChange={(e) => setTimeoutValue(parseInt(e.target.value) || 30)}
                        className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-cyan-500"
                      />
                    </div>
                  )}

                  {/* Error Display */}
                  {error && (
                    <div className="p-3 bg-red-900/20 border border-red-500/20 rounded-md">
                      <p className="text-sm text-red-400">{error}</p>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 mt-6">
                  <button
                    onClick={onClose}
                    className="flex-1 px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleSave}
                    disabled={!selectedTool}
                    className="flex-1 px-4 py-2 bg-cyan-600 hover:bg-cyan-700 disabled:bg-gray-600 text-white rounded-md transition-colors"
                  >
                    Save
                  </button>
                </div>
              </Dialog.Panel>
            </Transition.Child>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
}
