'use client';

import { CpuChipIcon } from '@heroicons/react/24/outline';
import BaseNode from './BaseNode';
import { WorkflowNode, PlannerNodeData } from '@/types/manualBuild';

interface PlannerNodeProps {
  data: WorkflowNode['data'];
}

export default function PlannerNode({ data }: PlannerNodeProps) {
  const config = data.config as PlannerNodeData['config'];
  const providerId = config?.providerId;
  const modelId = config?.modelId;
  const maxSubtasks = config?.maxSubtasks || 10;

  const getProviderName = (id: string) => {
    const providerMap: Record<string, string> = {
      'openai': 'OpenAI',
      'anthropic': 'Anthropic',
      'google': 'Google',
      'groq': 'Groq',
      'deepseek': 'DeepSeek',
      'openrouter': 'OpenRouter'
    };
    return providerMap[id] || id;
  };

  const getModelDisplayName = (modelId: string) => {
    if (!modelId) return '';
    // Extract model name from ID (e.g., 'gpt-4' from 'openai/gpt-4')
    const parts = modelId.split('/');
    return parts[parts.length - 1];
  };

  return (
    <BaseNode
      data={data}
      icon={CpuChipIcon}
      color="#8b5cf6"
      hasInput={false} // Planner has no input - it's a starting node
      hasOutput={true}
    >
      <div className="space-y-3">
        {providerId && modelId ? (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-sm font-medium text-white">
                {getProviderName(providerId)}
              </span>
              <span className="text-xs text-gray-400">
                {getModelDisplayName(modelId)}
              </span>
            </div>
            
            <div className="text-xs text-gray-400">
              Max subtasks: {maxSubtasks}
            </div>

            {config?.parameters?.temperature !== undefined && (
              <div className="text-xs text-gray-400">
                Temperature: {config.parameters.temperature}
              </div>
            )}


          </div>
        ) : (
          <div className="text-sm text-gray-400">
            Configure AI model for planning
          </div>
        )}

        <div className="text-xs text-purple-300 bg-purple-900/30 px-2 py-1 rounded">
          📋 Planning Agent
        </div>
      </div>
    </BaseNode>
  );
}
