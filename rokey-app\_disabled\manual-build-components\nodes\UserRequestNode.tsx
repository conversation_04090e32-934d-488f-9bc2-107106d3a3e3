'use client';

import { UserIcon } from '@heroicons/react/24/outline';
import BaseNode from './BaseNode';
import { WorkflowNode } from '@/types/manualBuild';

interface UserRequestNodeProps {
  data: WorkflowNode['data'];
}

export default function UserRequestNode({ data }: UserRequestNodeProps) {
  return (
    <BaseNode
      data={data}
      icon={UserIcon}
      color="#10b981"
      hasInput={false}
      hasOutput={true}
    >
      <div className="space-y-2">
        <div className="text-sm text-gray-300">
          Entry point for user input
        </div>
        <div className="text-xs text-gray-400">
          This node captures the initial user request and starts the workflow execution.
        </div>
      </div>
    </BaseNode>
  );
}
