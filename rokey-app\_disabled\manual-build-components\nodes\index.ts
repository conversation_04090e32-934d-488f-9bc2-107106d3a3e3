// Node Types Registry for React Flow
// This file exports all custom node components for the Manual Build workflow editor

import UserRequestNode from './UserRequestNode';
import ClassifierNode from './ClassifierNode';
import ProviderNode from './ProviderNode';
import VisionNode from './VisionNode';
import OutputNode from './OutputNode';
import RoleAgentNode from './RoleAgentNode';
import CentralRouterNode from './CentralRouterNode';

import ToolNode from './ToolNode';
import MemoryNode from './MemoryNode';

import PlannerNode from './PlannerNode';
import BrowsingNode from './BrowsingNode';

// Export all node types for React Flow
export const nodeTypes = {
  userRequest: UserRequestNode,
  classifier: ClassifierNode,
  provider: ProviderNode,
  vision: VisionNode,
  output: OutputNode,
  roleAgent: RoleAgentNode,
  centralRouter: CentralRouterNode,
  tool: ToolNode,
  memory: MemoryNode,
  planner: PlannerNode,
  browsing: BrowsingNode,
};

// Export individual components
export {
  UserRequestNode,
  ClassifierNode,
  ProviderNode,
  VisionNode,
  OutputNode,
  RoleAgentNode,
  CentralRouterNode,
  ToolNode,
  MemoryNode,
  PlannerNode,
  BrowsingNode,
};
