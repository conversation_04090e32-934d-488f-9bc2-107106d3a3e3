'use client';

import { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { ReactFlow, Background, Controls, MiniMap, useNodesState, useEdgesState, addEdge, Connection, Edge } from '@xyflow/react';
import '@xyflow/react/dist/style.css';

import { WorkflowNode, WorkflowEdge, ManualBuildWorkflow } from '@/types/manualBuild';
import WorkflowToolbar from '@/components/manual-build/WorkflowToolbar';
import NodePalette from '@/components/manual-build/NodePalette';
import NodeConfigPanel from '@/components/manual-build/NodeConfigPanel';
import ContextMenu from '@/components/manual-build/ContextMenu';
import { nodeTypes } from '@/components/manual-build/nodes';
import { useWorkflowWebSocket } from '@/hooks/useWorkflowWebSocket';
import ErrorBoundary from '@/components/manual-build/ErrorBoundary';
import ErrorRecoveryPanel from '@/components/manual-build/ErrorRecoveryPanel';
import WorkflowSharingModal from '@/components/manual-build/WorkflowSharingModal';
import WorkflowSaveModal from '@/components/manual-build/WorkflowSaveModal';

interface WorkflowEditorPageProps {
  params: Promise<{ workflowId: string }>;
}

export default function WorkflowEditorPage({ params }: WorkflowEditorPageProps) {
  const resolvedParams = useParams();
  const router = useRouter();
  const workflowId = resolvedParams?.workflowId as string;
  
  const [workflow, setWorkflow] = useState<ManualBuildWorkflow | null>(null);
  const [nodes, setNodes, onNodesChange] = useNodesState<WorkflowNode>([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState<WorkflowEdge>([]);
  const [selectedNode, setSelectedNode] = useState<WorkflowNode | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [contextMenu, setContextMenu] = useState<{
    id: string;
    type: 'node' | 'edge';
    nodeType?: string;
    x: number;
    y: number;
  } | null>(null);

  // Save modal state
  const [saveModal, setSaveModal] = useState<{
    isOpen: boolean;
    mode: 'save' | 'success' | 'error';
    apiKey?: string;
    workflowName?: string;
    errorMessage?: string;
  }>({
    isOpen: false,
    mode: 'save'
  });

  // Store new workflow ID for redirect after save
  const [newWorkflowId, setNewWorkflowId] = useState<string | null>(null);

  // WebSocket connection for real-time updates
  const [wsState, wsActions] = useWorkflowWebSocket(
    workflowId !== 'new' ? workflowId : null,
    {
      autoConnect: true,
      onEvent: (event) => {
        console.log('Workflow event received:', event);
        // Handle real-time events here
        if (event.type === 'workflow_started') {
          console.log('🚀 Workflow execution started');
        } else if (event.type === 'node_started') {
          console.log(`🔄 Node ${event.data.nodeType} started`);
        } else if (event.type === 'node_completed') {
          console.log(`✅ Node ${event.data.nodeType} completed in ${event.data.duration}ms`);
        } else if (event.type === 'workflow_completed') {
          console.log('🎉 Workflow execution completed');
        } else if (event.type === 'workflow_failed') {
          console.error('❌ Workflow execution failed:', event.data.error);
        }
      },
      onConnect: () => {
        console.log('🔗 Connected to workflow WebSocket');
      },
      onDisconnect: () => {
        console.log('🔌 Disconnected from workflow WebSocket');
      },
      onError: (error) => {
        console.error('❌ WebSocket error:', error);
      }
    }
  );

  // Error recovery state
  const [workflowErrors, setWorkflowErrors] = useState<any[]>([]);
  const [showErrorPanel, setShowErrorPanel] = useState(false);

  // Sharing state
  const [showSharingModal, setShowSharingModal] = useState(false);

  // Load workflow data
  useEffect(() => {
    if (workflowId === 'new') {
      initializeNewWorkflow();
    } else {
      loadWorkflow(workflowId);
    }
  }, [workflowId]);

  const initializeNewWorkflow = async () => {
    try {
      // Create default nodes for new workflow
      const defaultNodes: WorkflowNode[] = [
        {
          id: 'user-request',
          type: 'userRequest',
          position: { x: 50, y: 200 },
          data: {
            label: 'User Request',
            config: {},
            isConfigured: true,
            description: 'Starting point for user input'
          }
        },
        {
          id: 'classifier',
          type: 'classifier',
          position: { x: 350, y: 200 },
          data: {
            label: 'Classifier',
            config: {},
            isConfigured: true,
            description: 'Analyzes and categorizes the request'
          }
        },
        {
          id: 'output',
          type: 'output',
          position: { x: 950, y: 200 },
          data: {
            label: 'Output',
            config: {},
            isConfigured: true,
            description: 'Final response to the user'
          }
        }
      ];

      const defaultEdges: WorkflowEdge[] = [
        {
          id: 'e1',
          source: 'user-request',
          target: 'classifier',
          type: 'smoothstep',
          animated: true
        }
      ];

      setNodes(defaultNodes);
      setEdges(defaultEdges);
      setIsLoading(false);
    } catch (error) {
      console.error('Failed to initialize new workflow:', error);
      setIsLoading(false);
    }
  };

  const loadWorkflow = async (id: string) => {
    try {
      setIsLoading(true);
      console.log('Loading workflow with ID:', id);

      const response = await fetch(`/api/workflows?id=${id}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API response error:', response.status, errorText);
        throw new Error(`Failed to load workflow: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log('API response data:', data);

      const workflowData = data.workflow;

      if (workflowData) {
        // Set workflow metadata
        setWorkflow(workflowData);

        // Load nodes and edges into the React Flow canvas
        if (workflowData.nodes && Array.isArray(workflowData.nodes)) {
          console.log('Loading nodes:', workflowData.nodes.length);
          setNodes(workflowData.nodes);
        } else {
          console.warn('No nodes found in workflow data');
          setNodes([]);
        }

        if (workflowData.edges && Array.isArray(workflowData.edges)) {
          console.log('Loading edges:', workflowData.edges.length);
          setEdges(workflowData.edges);
        } else {
          console.warn('No edges found in workflow data');
          setEdges([]);
        }

        console.log('Workflow loaded successfully:', {
          id: workflowData.id,
          name: workflowData.name,
          nodeCount: workflowData.nodes?.length || 0,
          edgeCount: workflowData.edges?.length || 0
        });
      } else {
        console.error('No workflow data found in response');
        throw new Error('No workflow data found');
      }

    } catch (error) {
      console.error('Failed to load workflow:', error);
      // TODO: Show error toast or notification
    } finally {
      setIsLoading(false);
    }
  };

  const onConnect = useCallback(
    (params: Connection) => {
      const newEdge: Edge = {
        ...params,
        id: `e${edges.length + 1}`,
        type: 'smoothstep',
        animated: true
      };
      setEdges((eds) => addEdge(newEdge, eds));
      setIsDirty(true);
    },
    [edges.length, setEdges]
  );

  const onNodeClick = useCallback((event: React.MouseEvent, node: WorkflowNode) => {
    setSelectedNode(node);
  }, []);

  const onPaneClick = useCallback(() => {
    setSelectedNode(null);
    setContextMenu(null);
  }, []);

  const onNodeContextMenu = useCallback((event: React.MouseEvent, node: WorkflowNode) => {
    event.preventDefault();
    setContextMenu({
      id: node.id,
      type: 'node',
      nodeType: node.type,
      x: event.clientX,
      y: event.clientY,
    });
  }, []);

  const onEdgeContextMenu = useCallback((event: React.MouseEvent, edge: WorkflowEdge) => {
    event.preventDefault();
    setContextMenu({
      id: edge.id,
      type: 'edge',
      x: event.clientX,
      y: event.clientY,
    });
  }, []);

  const handleDeleteNode = useCallback((nodeId: string) => {
    // Don't delete core nodes
    const coreNodes = ['user-request', 'classifier', 'output'];
    if (coreNodes.includes(nodeId)) return;

    setNodes((nds) => nds.filter((node) => node.id !== nodeId));
    setEdges((eds) => eds.filter((edge) => edge.source !== nodeId && edge.target !== nodeId));
    setIsDirty(true);

    // Close config panel if deleted node was selected
    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
    }
  }, [selectedNode, setNodes, setEdges]);

  const handleDeleteEdge = useCallback((edgeId: string) => {
    setEdges((eds) => eds.filter((edge) => edge.id !== edgeId));
    setIsDirty(true);
  }, [setEdges]);

  const handleDuplicateNode = useCallback((nodeId: string) => {
    const nodeToDuplicate = nodes.find(n => n.id === nodeId);
    if (!nodeToDuplicate) return;

    const newNode: WorkflowNode = {
      ...nodeToDuplicate,
      id: `${nodeToDuplicate.type}-${Date.now()}`,
      position: {
        x: nodeToDuplicate.position.x + 50,
        y: nodeToDuplicate.position.y + 50,
      },
      data: {
        ...nodeToDuplicate.data,
        label: `${nodeToDuplicate.data.label} Copy`,
      }
    };

    setNodes((nds) => [...nds, newNode]);
    setIsDirty(true);
  }, [nodes, setNodes]);

  const handleConfigureNode = useCallback((nodeId: string) => {
    const node = nodes.find(n => n.id === nodeId);
    if (node) {
      setSelectedNode(node);
    }
  }, [nodes]);

  const handleSave = async () => {
    if (!workflow && workflowId === 'new') {
      // Show save dialog for new workflow
      setSaveModal({
        isOpen: true,
        mode: 'save'
      });
    } else {
      // Update existing workflow
      await handleUpdateWorkflow();
    }
  };

  const handleSaveWorkflow = async (name: string, description: string) => {
    setIsSaving(true);
    try {
      const response = await fetch('/api/workflows', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name,
          description,
          nodes,
          edges,
          settings: {}
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to save workflow');
      }

      const result = await response.json();

      // Show success modal with API key
      setSaveModal({
        isOpen: true,
        mode: 'success',
        apiKey: result.api_key,
        workflowName: name
      });

      // Store the workflow ID for later redirect when modal is closed
      setNewWorkflowId(result.workflow.id);

    } catch (error) {
      console.error('Failed to save workflow:', error);
      setSaveModal({
        isOpen: true,
        mode: 'error',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleUpdateWorkflow = async () => {
    if (!workflow) return;
    setIsSaving(true);
    try {
      const response = await fetch('/api/workflows', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          id: workflowId,
          name: workflow?.name,
          description: workflow?.description,
          nodes,
          edges,
          settings: workflow?.settings || {}
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Failed to update workflow');
      }

      setIsDirty(false);
      setSaveModal({
        isOpen: true,
        mode: 'success',
        workflowName: workflow?.name || 'Workflow'
      });

    } catch (error) {
      console.error('Failed to update workflow:', error);
      setSaveModal({
        isOpen: true,
        mode: 'error',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      setIsSaving(false);
    }
  };

  const handleTestInPlayground = () => {
    if (workflow?.id) {
      // Navigate to main playground with workflow pre-selected
      window.open(`/playground?workflow=${workflow.id}`, '_blank');
    } else {
      setSaveModal({
        isOpen: true,
        mode: 'error',
        errorMessage: 'Please save the workflow first to test it in the playground'
      });
    }
  };

  const handleAddNode = (nodeType: string, position: { x: number; y: number }) => {
    let defaultConfig = {};
    let isConfigured = true;

    // Set proper default config for specific node types
    if (nodeType === 'provider') {
      defaultConfig = {
        providerId: '',
        modelId: '',
        apiKey: '',
        parameters: {
          temperature: 1.0,
          maxTokens: undefined,
          topP: undefined,
          frequencyPenalty: undefined,
          presencePenalty: undefined,
        }
      };
      isConfigured = false;
    } else if (nodeType === 'centralRouter') {
      defaultConfig = {
        routingStrategy: 'smart',
        fallbackProvider: '',
        maxRetries: 3,
        timeout: 30000,
        enableCaching: true,
        debugMode: false
      };
      isConfigured = true;
    }

    const newNode: WorkflowNode = {
      id: `${nodeType}-${Date.now()}`,
      type: nodeType as any,
      position,
      data: {
        label: nodeType === 'centralRouter' ? 'Central Router' : nodeType.charAt(0).toUpperCase() + nodeType.slice(1),
        config: defaultConfig,
        isConfigured,
        description: `${nodeType} node`
      }
    };

    setNodes((nds) => [...nds, newNode]);
    setIsDirty(true);
  };

  const handleNodeUpdate = (nodeId: string, updates: Partial<WorkflowNode['data']>) => {
    setNodes((nds) =>
      nds.map((node) =>
        node.id === nodeId
          ? { ...node, data: { ...node.data, ...updates } }
          : node
      )
    );
    setIsDirty(true);
  };

  if (isLoading) {
    return (
      <div className="h-screen bg-[#040716] flex items-center justify-center">
        <div className="text-white">Loading workflow...</div>
      </div>
    );
  }

  return (
    <ErrorBoundary
      showDetails={process.env.NODE_ENV === 'development'}
      onError={(error, errorInfo) => {
        console.error('Workflow Editor Error:', error, errorInfo);
        // Add to error list for recovery panel
        const newError = {
          id: `error-${Date.now()}`,
          nodeId: 'editor',
          nodeType: 'editor',
          nodeLabel: 'Workflow Editor',
          message: error.message,
          timestamp: new Date().toISOString(),
          attempt: 1,
          maxRetries: 3,
          status: 'pending' as const,
          recoveryStrategies: [
            {
              type: 'retry' as const,
              description: 'Reload the editor',
              available: true,
              recommended: true
            }
          ]
        };
        setWorkflowErrors(prev => [...prev, newError]);
        setShowErrorPanel(true);
      }}
    >
      <div className="h-screen bg-[#040716] flex flex-col">
        {/* Toolbar */}
        <WorkflowToolbar
          workflow={workflow}
          isDirty={isDirty}
          isSaving={isSaving}
          onSave={handleSave}
          onExecute={handleTestInPlayground}
          onBack={() => router.push('/manual-build')}
          onShare={() => setShowSharingModal(true)}
        />

        <div className="flex-1 flex">
          {/* Node Palette */}
          <NodePalette onAddNode={handleAddNode} />

          {/* Main Canvas */}
          <div className="flex-1 relative manual-build-canvas">
            <ReactFlow
              nodes={nodes}
              edges={edges}
              onNodesChange={onNodesChange}
              onEdgesChange={onEdgesChange}
              onConnect={onConnect}
              onNodeClick={onNodeClick}
              onNodeContextMenu={onNodeContextMenu}
              onEdgeContextMenu={onEdgeContextMenu}
              onPaneClick={onPaneClick}
              nodeTypes={nodeTypes as any}
              fitView
              className="bg-[#040716]"
              defaultViewport={{ x: 0, y: 0, zoom: 0.8 }}
              minZoom={0.1}
              maxZoom={2}
              connectionLineStyle={{ stroke: '#ff6b35', strokeWidth: 2 }}
              defaultEdgeOptions={{
                style: { stroke: '#ff6b35', strokeWidth: 2 },
                type: 'smoothstep',
                animated: true,
              }}
              proOptions={{ hideAttribution: true }}
            >
              <Background
                color="#1f2937"
                gap={20}
                size={1}
              />
              <Controls
                className="bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm"
                showInteractive={false}
              />
              <MiniMap
                className="bg-gray-800/90 border border-gray-700/50 backdrop-blur-sm"
                nodeColor="#ff6b35"
                maskColor="rgba(0, 0, 0, 0.2)"
              />
            </ReactFlow>

            {/* Context Menu */}
            {contextMenu && (
              <ContextMenu
                id={contextMenu.id}
                type={contextMenu.type}
                nodeType={contextMenu.nodeType}
                top={contextMenu.y}
                left={contextMenu.x}
                onClose={() => setContextMenu(null)}
                onDelete={contextMenu.type === 'node' ? handleDeleteNode : handleDeleteEdge}
                onDuplicate={contextMenu.type === 'node' ? handleDuplicateNode : undefined}
                onConfigure={contextMenu.type === 'node' ? handleConfigureNode : undefined}
                onDisconnect={contextMenu.type === 'edge' ? handleDeleteEdge : undefined}
              />
            )}
          </div>

          {/* Configuration Panel */}
          {selectedNode && (
            <NodeConfigPanel
              node={selectedNode}
              onUpdate={(updates) => handleNodeUpdate(selectedNode.id, updates)}
              onClose={() => setSelectedNode(null)}
            />
          )}
        </div>

        {/* Error Recovery Panel */}
        <ErrorRecoveryPanel
          errors={workflowErrors}
          onRetry={(errorId) => {
            console.log('Retrying error:', errorId);
            // Implement retry logic
          }}
          onSkip={(errorId) => {
            console.log('Skipping error:', errorId);
            // Implement skip logic
          }}
          onManualFix={(errorId) => {
            console.log('Manual fix for error:', errorId);
            // Implement manual fix logic
          }}
          isVisible={showErrorPanel}
          onClose={() => setShowErrorPanel(false)}
        />

        {/* Workflow Sharing Modal */}
        {workflow && (
          <WorkflowSharingModal
            workflowId={workflow.id}
            workflowName={workflow.name}
            isOpen={showSharingModal}
            onClose={() => setShowSharingModal(false)}
          />
        )}

        {/* Workflow Save Modal */}
        <WorkflowSaveModal
          isOpen={saveModal.isOpen}
          mode={saveModal.mode}
          apiKey={saveModal.apiKey}
          workflowName={saveModal.workflowName}
          errorMessage={saveModal.errorMessage}
          isSaving={isSaving}
          onClose={() => {
            const wasSuccessfulSave = saveModal.mode === 'success' && saveModal.apiKey;
            setSaveModal({ isOpen: false, mode: 'save' });

            // Redirect to the saved workflow if this was a successful save
            if (wasSuccessfulSave && workflowId === 'new' && newWorkflowId) {
              router.push(`/manual-build/${newWorkflowId}`);
            }
          }}
          onSave={handleSaveWorkflow}
        />
      </div>
    </ErrorBoundary>
  );
}
