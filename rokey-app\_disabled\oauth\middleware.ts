// OAuth Authentication Middleware
// Provides automatic token refresh and authentication for tool API calls

import { getValidAccessToken, updateConnectionStatus } from './tokenManager';
import { TOOL_DISPLAY_NAMES } from './config';

export interface AuthenticatedRequest {
  headers: Record<string, string>;
  isAuthenticated: boolean;
  error?: string;
}

export interface ToolApiOptions {
  userId: string;
  toolType: string;
  additionalHeaders?: Record<string, string>;
  timeout?: number;
}

// Get authenticated headers for tool API calls
export async function getAuthenticatedHeaders(
  userId: string,
  toolType: string,
  additionalHeaders: Record<string, string> = {}
): Promise<AuthenticatedRequest> {
  try {
    console.log(`🔐 AUTH MIDDLEWARE: Getting authenticated headers for ${toolType}`);
    
    // Get valid access token (with automatic refresh)
    const accessToken = await getValidAccessToken(userId, toolType);
    
    if (!accessToken) {
      console.error(`🔐 AUTH MIDDLEWARE: No valid access token for ${toolType}`);
      await updateConnectionStatus(userId, toolType, 'expired');
      
      return {
        headers: {},
        isAuthenticated: false,
        error: `${TOOL_DISPLAY_NAMES[toolType] || toolType} connection expired. Please reconnect.`
      };
    }
    
    // Prepare headers based on tool type
    const headers: Record<string, string> = {
      'Authorization': `Bearer ${accessToken}`,
      'Content-Type': 'application/json',
      ...additionalHeaders
    };
    
    // Tool-specific header adjustments
    switch (toolType) {
      case 'notion':
        headers['Notion-Version'] = '2022-06-28';
        break;
      case 'google_drive':
      case 'google_docs':
      case 'google_sheets':
      case 'gmail':
      case 'calendar':
      case 'youtube':
        // Google APIs use standard Bearer token
        break;
      default:
        break;
    }
    
    console.log(`🔐 AUTH MIDDLEWARE: Headers prepared for ${toolType}`);
    
    return {
      headers,
      isAuthenticated: true
    };
    
  } catch (error) {
    console.error(`🔐 AUTH MIDDLEWARE: Error getting authenticated headers for ${toolType}:`, error);
    await updateConnectionStatus(userId, toolType, 'error');
    
    return {
      headers: {},
      isAuthenticated: false,
      error: 'Authentication error occurred'
    };
  }
}

// Make authenticated API request to tool
export async function makeAuthenticatedRequest(
  url: string,
  options: ToolApiOptions & {
    method?: string;
    body?: any;
  }
): Promise<Response> {
  const { userId, toolType, method = 'GET', body, additionalHeaders, timeout = 30000 } = options;
  
  console.log(`🔐 AUTH MIDDLEWARE: Making authenticated ${method} request to ${toolType}`);
  
  // Get authenticated headers
  const authResult = await getAuthenticatedHeaders(userId, toolType, additionalHeaders);
  
  if (!authResult.isAuthenticated) {
    throw new Error(authResult.error || 'Authentication failed');
  }
  
  // Prepare request options
  const requestOptions: RequestInit = {
    method,
    headers: authResult.headers,
    signal: AbortSignal.timeout(timeout)
  };
  
  if (body && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
    requestOptions.body = typeof body === 'string' ? body : JSON.stringify(body);
  }
  
  try {
    const response = await fetch(url, requestOptions);
    
    // Handle authentication errors
    if (response.status === 401 || response.status === 403) {
      console.error(`🔐 AUTH MIDDLEWARE: Authentication failed for ${toolType} (${response.status})`);
      await updateConnectionStatus(userId, toolType, 'expired');
      throw new Error(`${TOOL_DISPLAY_NAMES[toolType] || toolType} authentication failed. Please reconnect.`);
    }
    
    // Handle rate limiting
    if (response.status === 429) {
      console.warn(`🔐 AUTH MIDDLEWARE: Rate limited for ${toolType}`);
      const retryAfter = response.headers.get('Retry-After');
      throw new Error(`Rate limited. ${retryAfter ? `Retry after ${retryAfter} seconds.` : 'Please try again later.'}`);
    }
    
    console.log(`🔐 AUTH MIDDLEWARE: Request successful for ${toolType} (${response.status})`);
    return response;
    
  } catch (error) {
    if (error instanceof Error) {
      if (error.name === 'AbortError') {
        throw new Error(`Request timeout for ${TOOL_DISPLAY_NAMES[toolType] || toolType}`);
      }
      throw error;
    }
    throw new Error(`Request failed for ${TOOL_DISPLAY_NAMES[toolType] || toolType}`);
  }
}

// Wrapper for GET requests
export async function authenticatedGet(
  url: string,
  options: ToolApiOptions
): Promise<Response> {
  return makeAuthenticatedRequest(url, { ...options, method: 'GET' });
}

// Wrapper for POST requests
export async function authenticatedPost(
  url: string,
  body: any,
  options: ToolApiOptions
): Promise<Response> {
  return makeAuthenticatedRequest(url, { ...options, method: 'POST', body });
}

// Wrapper for PUT requests
export async function authenticatedPut(
  url: string,
  body: any,
  options: ToolApiOptions
): Promise<Response> {
  return makeAuthenticatedRequest(url, { ...options, method: 'PUT', body });
}

// Wrapper for PATCH requests
export async function authenticatedPatch(
  url: string,
  body: any,
  options: ToolApiOptions
): Promise<Response> {
  return makeAuthenticatedRequest(url, { ...options, method: 'PATCH', body });
}

// Wrapper for DELETE requests
export async function authenticatedDelete(
  url: string,
  options: ToolApiOptions
): Promise<Response> {
  return makeAuthenticatedRequest(url, { ...options, method: 'DELETE' });
}

// Check if user has access to a specific tool
export async function hasToolAccess(
  userId: string,
  toolType: string
): Promise<boolean> {
  try {
    const accessToken = await getValidAccessToken(userId, toolType);
    return !!accessToken;
  } catch (error) {
    console.error(`🔐 AUTH MIDDLEWARE: Error checking tool access for ${toolType}:`, error);
    return false;
  }
}

// Get all connected tools for a user
export async function getConnectedTools(userId: string): Promise<string[]> {
  try {
    const allTools = Object.keys(TOOL_DISPLAY_NAMES);
    const connectedTools: string[] = [];
    
    for (const toolType of allTools) {
      const hasAccess = await hasToolAccess(userId, toolType);
      if (hasAccess) {
        connectedTools.push(toolType);
      }
    }
    
    console.log(`🔐 AUTH MIDDLEWARE: User ${userId} has ${connectedTools.length} connected tools`);
    return connectedTools;
    
  } catch (error) {
    console.error('🔐 AUTH MIDDLEWARE: Error getting connected tools:', error);
    return [];
  }
}

// Validate tool connection before use
export async function validateToolConnection(
  userId: string,
  toolType: string
): Promise<{ isValid: boolean; error?: string }> {
  try {
    const authResult = await getAuthenticatedHeaders(userId, toolType);
    
    if (!authResult.isAuthenticated) {
      return {
        isValid: false,
        error: authResult.error || 'Tool not connected'
      };
    }
    
    return { isValid: true };
    
  } catch (error) {
    console.error(`🔐 AUTH MIDDLEWARE: Error validating tool connection for ${toolType}:`, error);
    return {
      isValid: false,
      error: 'Connection validation failed'
    };
  }
}
