/**
 * WebSocket Server for Real-time Workflow Updates
 * Provides live updates during Manual Build workflow execution
 */

import { createClient } from '@supabase/supabase-js';

export interface WorkflowWebSocketEvent {
  id: string;
  workflowId: string;
  executionId?: string;
  type: 'workflow_started' | 'workflow_completed' | 'workflow_failed' | 'node_started' | 'node_completed' | 'node_failed' | 'progress_update' | 'log_message' | 'connection_established';
  timestamp: string;
  data: any;
  userId: string;
}

export interface WorkflowConnection {
  workflowId: string;
  userId: string;
  controller: ReadableStreamDefaultController;
  lastEventId?: string;
  connectedAt: string;
}

// In-memory storage for active WebSocket connections
const activeConnections = new Map<string, WorkflowConnection>();

/**
 * Register a new WebSocket connection for workflow updates
 */
export function registerWorkflowConnection(
  connectionId: string,
  workflowId: string,
  userId: string,
  controller: ReadableStreamDefaultController
): void {
  const connection: WorkflowConnection = {
    workflowId,
    userId,
    controller,
    connectedAt: new Date().toISOString()
  };

  activeConnections.set(connectionId, connection);
  console.log(`[Workflow WebSocket] Registered connection ${connectionId} for workflow ${workflowId}`);

  // Send initial connection event
  const connectionEvent: WorkflowWebSocketEvent = {
    id: crypto.randomUUID(),
    workflowId,
    type: 'connection_established',
    timestamp: new Date().toISOString(),
    data: {
      message: '🔗 Connected to workflow real-time updates',
      workflowId,
      connectionId
    },
    userId
  };

  broadcastToConnection(connectionId, connectionEvent);
}

/**
 * Unregister a WebSocket connection
 */
export function unregisterWorkflowConnection(connectionId: string): void {
  const connection = activeConnections.get(connectionId);
  if (connection) {
    activeConnections.delete(connectionId);
    console.log(`[Workflow WebSocket] Unregistered connection ${connectionId} for workflow ${connection.workflowId}`);
  }
}

/**
 * Broadcast event to a specific connection
 */
export function broadcastToConnection(
  connectionId: string,
  event: WorkflowWebSocketEvent
): void {
  const connection = activeConnections.get(connectionId);
  
  if (connection) {
    try {
      const encoder = new TextEncoder();
      const eventData = `id: ${event.id}\nevent: ${event.type}\ndata: ${JSON.stringify(event)}\n\n`;
      connection.controller.enqueue(encoder.encode(eventData));
      connection.lastEventId = event.id;

      console.log(`[Workflow WebSocket] Sent ${event.type} to connection ${connectionId}`);
    } catch (error) {
      console.error(`[Workflow WebSocket] Error sending to connection ${connectionId}:`, error);
      // Remove dead connection
      activeConnections.delete(connectionId);
    }
  }
}

/**
 * Broadcast event to all connections for a specific workflow
 */
export function broadcastToWorkflow(
  workflowId: string,
  event: WorkflowWebSocketEvent
): void {
  const workflowConnections = Array.from(activeConnections.entries())
    .filter(([_, connection]) => connection.workflowId === workflowId);

  workflowConnections.forEach(([connectionId, _]) => {
    broadcastToConnection(connectionId, event);
  });

  console.log(`[Workflow WebSocket] Broadcasted ${event.type} to ${workflowConnections.length} connections for workflow ${workflowId}`);
}

/**
 * Broadcast event to all connections for a specific user
 */
export function broadcastToUser(
  userId: string,
  event: WorkflowWebSocketEvent
): void {
  const userConnections = Array.from(activeConnections.entries())
    .filter(([_, connection]) => connection.userId === userId);

  userConnections.forEach(([connectionId, _]) => {
    broadcastToConnection(connectionId, event);
  });

  console.log(`[Workflow WebSocket] Broadcasted ${event.type} to ${userConnections.length} connections for user ${userId}`);
}

/**
 * Get active connections count for a workflow
 */
export function getWorkflowConnectionsCount(workflowId: string): number {
  return Array.from(activeConnections.values())
    .filter(connection => connection.workflowId === workflowId).length;
}

/**
 * Get all active connections for monitoring
 */
export function getActiveConnections(): Map<string, WorkflowConnection> {
  return new Map(activeConnections);
}

/**
 * Emit workflow event to all relevant connections
 */
export function emitWorkflowEvent(
  workflowId: string,
  userId: string,
  eventType: WorkflowWebSocketEvent['type'],
  data: any,
  executionId?: string
): void {
  const event: WorkflowWebSocketEvent = {
    id: crypto.randomUUID(),
    workflowId,
    executionId,
    type: eventType,
    timestamp: new Date().toISOString(),
    data,
    userId
  };

  // Broadcast to all connections for this workflow
  broadcastToWorkflow(workflowId, event);

  // Store event in database for persistence (optional)
  storeWorkflowEvent(event).catch(error => {
    console.error('[Workflow WebSocket] Failed to store event:', error);
  });
}

/**
 * Store workflow event in database for persistence and replay
 */
async function storeWorkflowEvent(event: WorkflowWebSocketEvent): Promise<void> {
  try {
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    );

    await supabase
      .from('workflow_execution_logs')
      .insert({
        execution_id: event.executionId || event.workflowId,
        workflow_id: event.workflowId,
        node_id: event.data?.nodeId || 'system',
        node_type: event.data?.nodeType || 'system',
        log_level: event.type.includes('failed') ? 'error' : 'info',
        message: event.data?.message || `Workflow event: ${event.type}`,
        data: event.data,
        duration_ms: event.data?.duration,
        created_at: event.timestamp
      });

    console.log(`[Workflow WebSocket] Stored event ${event.type} for workflow ${event.workflowId}`);
  } catch (error) {
    console.error('[Workflow WebSocket] Failed to store event in database:', error);
  }
}

/**
 * Create a Server-Sent Events stream for workflow updates
 */
export function createWorkflowEventStream(
  workflowId: string,
  userId: string
): ReadableStream {
  const connectionId = crypto.randomUUID();
  const encoder = new TextEncoder();
  
  return new ReadableStream({
    start(controller) {
      registerWorkflowConnection(connectionId, workflowId, userId, controller);
    },
    
    cancel() {
      unregisterWorkflowConnection(connectionId);
      console.log(`[Workflow WebSocket] Stream cancelled for workflow ${workflowId}`);
    }
  });
}

/**
 * Cleanup inactive connections (called periodically)
 */
export function cleanupInactiveConnections(): void {
  const now = Date.now();
  const maxAge = 30 * 60 * 1000; // 30 minutes

  for (const [connectionId, connection] of activeConnections.entries()) {
    const connectionAge = now - new Date(connection.connectedAt).getTime();
    
    if (connectionAge > maxAge) {
      console.log(`[Workflow WebSocket] Cleaning up inactive connection ${connectionId}`);
      unregisterWorkflowConnection(connectionId);
    }
  }
}

// Cleanup inactive connections every 5 minutes
setInterval(cleanupInactiveConnections, 5 * 60 * 1000);
