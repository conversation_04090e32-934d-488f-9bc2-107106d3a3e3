/**
 * Real-time Workflow Execution Monitor
 * Provides live updates during workflow execution with WebSocket support
 */

import { createClient } from '@supabase/supabase-js';

export interface ExecutionUpdate {
  executionId: string;
  workflowId: string;
  status: 'starting' | 'running' | 'completed' | 'failed' | 'paused';
  currentNodeId?: string;
  currentNodeType?: string;
  progress: {
    nodesCompleted: number;
    totalNodes: number;
    percentage: number;
  };
  logs: ExecutionLog[];
  result?: any;
  error?: string;
  timestamp: string;
}

export interface ExecutionLog {
  id: string;
  nodeId: string;
  nodeType: string;
  level: 'info' | 'warning' | 'error' | 'success';
  message: string;
  data?: any;
  duration?: number;
  timestamp: string;
}

export class WorkflowExecutionMonitor {
  private static instance: WorkflowExecutionMonitor;
  private supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!
  );
  private activeExecutions = new Map<string, ExecutionUpdate>();
  private subscribers = new Map<string, Set<(update: ExecutionUpdate) => void>>();

  static getInstance(): WorkflowExecutionMonitor {
    if (!WorkflowExecutionMonitor.instance) {
      WorkflowExecutionMonitor.instance = new WorkflowExecutionMonitor();
    }
    return WorkflowExecutionMonitor.instance;
  }

  /**
   * Start monitoring a workflow execution
   */
  async startExecution(
    executionId: string,
    workflowId: string,
    userId: string,
    totalNodes: number
  ): Promise<void> {
    const execution: ExecutionUpdate = {
      executionId,
      workflowId,
      status: 'starting',
      progress: {
        nodesCompleted: 0,
        totalNodes,
        percentage: 0
      },
      logs: [],
      timestamp: new Date().toISOString()
    };

    this.activeExecutions.set(executionId, execution);

    // Store in database
    await this.supabase.from('workflow_executions').insert({
      id: executionId,
      workflow_id: workflowId,
      user_id: userId,
      status: 'running',
      trigger_type: 'manual',
      nodes_total: totalNodes,
      started_at: new Date().toISOString()
    });

    this.notifySubscribers(executionId, execution);
  }

  /**
   * Update execution progress
   */
  async updateProgress(
    executionId: string,
    nodeId: string,
    nodeType: string,
    status: 'starting' | 'completed' | 'failed',
    message: string,
    data?: any,
    duration?: number
  ): Promise<void> {
    const execution = this.activeExecutions.get(executionId);
    if (!execution) return;

    // Add log entry
    const log: ExecutionLog = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      nodeId,
      nodeType,
      level: status === 'failed' ? 'error' : status === 'completed' ? 'success' : 'info',
      message,
      data,
      duration,
      timestamp: new Date().toISOString()
    };

    execution.logs.push(log);
    execution.currentNodeId = nodeId;
    execution.currentNodeType = nodeType;

    if (status === 'completed') {
      execution.progress.nodesCompleted++;
      execution.progress.percentage = Math.round(
        (execution.progress.nodesCompleted / execution.progress.totalNodes) * 100
      );
    }

    execution.timestamp = new Date().toISOString();

    // Store log in database
    await this.supabase.from('workflow_execution_logs').insert({
      execution_id: executionId,
      workflow_id: execution.workflowId,
      node_id: nodeId,
      node_type: nodeType,
      log_level: log.level,
      message,
      data,
      duration_ms: duration,
      created_at: new Date().toISOString()
    });

    this.notifySubscribers(executionId, execution);
  }

  /**
   * Complete execution
   */
  async completeExecution(
    executionId: string,
    result: any,
    totalDuration: number
  ): Promise<void> {
    const execution = this.activeExecutions.get(executionId);
    if (!execution) return;

    execution.status = 'completed';
    execution.result = result;
    execution.progress.percentage = 100;
    execution.timestamp = new Date().toISOString();

    // Update database
    await this.supabase
      .from('workflow_executions')
      .update({
        status: 'completed',
        output_data: result,
        execution_time_ms: totalDuration,
        nodes_executed: execution.progress.nodesCompleted,
        completed_at: new Date().toISOString()
      })
      .eq('id', executionId);

    this.notifySubscribers(executionId, execution);

    // Clean up after 5 minutes
    setTimeout(() => {
      this.activeExecutions.delete(executionId);
      this.subscribers.delete(executionId);
    }, 5 * 60 * 1000);
  }

  /**
   * Fail execution
   */
  async failExecution(
    executionId: string,
    error: string,
    errorDetails?: any
  ): Promise<void> {
    const execution = this.activeExecutions.get(executionId);
    if (!execution) return;

    execution.status = 'failed';
    execution.error = error;
    execution.timestamp = new Date().toISOString();

    // Update database
    await this.supabase
      .from('workflow_executions')
      .update({
        status: 'failed',
        error_message: error,
        error_details: errorDetails,
        completed_at: new Date().toISOString()
      })
      .eq('id', executionId);

    this.notifySubscribers(executionId, execution);
  }

  /**
   * Subscribe to execution updates
   */
  subscribe(
    executionId: string,
    callback: (update: ExecutionUpdate) => void
  ): () => void {
    if (!this.subscribers.has(executionId)) {
      this.subscribers.set(executionId, new Set());
    }
    
    this.subscribers.get(executionId)!.add(callback);

    // Send current state if available
    const execution = this.activeExecutions.get(executionId);
    if (execution) {
      callback(execution);
    }

    // Return unsubscribe function
    return () => {
      const subs = this.subscribers.get(executionId);
      if (subs) {
        subs.delete(callback);
        if (subs.size === 0) {
          this.subscribers.delete(executionId);
        }
      }
    };
  }

  /**
   * Get execution status
   */
  getExecutionStatus(executionId: string): ExecutionUpdate | null {
    return this.activeExecutions.get(executionId) || null;
  }

  /**
   * Get execution history from database
   */
  async getExecutionHistory(
    workflowId: string,
    userId: string,
    limit = 10
  ): Promise<any[]> {
    const { data, error } = await this.supabase
      .from('workflow_executions')
      .select('*')
      .eq('workflow_id', workflowId)
      .eq('user_id', userId)
      .order('started_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Failed to fetch execution history:', error);
      return [];
    }

    return data || [];
  }

  /**
   * Get execution logs
   */
  async getExecutionLogs(executionId: string): Promise<ExecutionLog[]> {
    const { data, error } = await this.supabase
      .from('workflow_execution_logs')
      .select('*')
      .eq('execution_id', executionId)
      .order('created_at', { ascending: true });

    if (error) {
      console.error('Failed to fetch execution logs:', error);
      return [];
    }

    return data?.map(log => ({
      id: log.id,
      nodeId: log.node_id,
      nodeType: log.node_type,
      level: log.log_level,
      message: log.message,
      data: log.data,
      duration: log.duration_ms,
      timestamp: log.created_at
    })) || [];
  }

  private notifySubscribers(executionId: string, execution: ExecutionUpdate): void {
    const subscribers = this.subscribers.get(executionId);
    if (subscribers) {
      subscribers.forEach(callback => {
        try {
          callback(execution);
        } catch (error) {
          console.error('Error notifying subscriber:', error);
        }
      });
    }
  }
}

export const workflowMonitor = WorkflowExecutionMonitor.getInstance();
