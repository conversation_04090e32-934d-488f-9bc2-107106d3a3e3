// Tool Implementation Methods
// Contains the actual API implementations for each tool

import { authenticatedGet, authenticatedPost, authenticatedPut, authenticatedPatch, authenticatedDelete } from '@/lib/oauth/middleware';

// Google Drive API implementations
export class GoogleDriveAPI {
  static async listFiles(userId: string, params: any, timeout: number) {
    const url = 'https://www.googleapis.com/drive/v3/files';
    const queryParams = new URLSearchParams({
      pageSize: params.limit || '10',
      fields: 'files(id,name,mimeType,size,modifiedTime,webViewLink)',
      ...(params.query && { q: params.query })
    });

    const response = await authenticatedGet(`${url}?${queryParams}`, {
      userId,
      toolType: 'google_drive',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Drive API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      files: data.files,
      count: data.files?.length || 0
    };
  }

  static async getFile(userId: string, params: any, timeout: number) {
    const { fileId } = params;
    if (!fileId) throw new Error('File ID is required');

    const url = `https://www.googleapis.com/drive/v3/files/${fileId}`;
    const queryParams = new URLSearchParams({
      fields: 'id,name,mimeType,size,modifiedTime,webViewLink,parents'
    });

    const response = await authenticatedGet(`${url}?${queryParams}`, {
      userId,
      toolType: 'google_drive',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Drive API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      file: data
    };
  }

  static async createFile(userId: string, params: any, timeout: number) {
    const { name, content, mimeType = 'text/plain', parentId } = params;
    if (!name) throw new Error('File name is required');

    const metadata = {
      name,
      ...(parentId && { parents: [parentId] })
    };

    const url = 'https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart';
    
    // Create multipart body
    const boundary = '-------314159265358979323846';
    const delimiter = `\r\n--${boundary}\r\n`;
    const close_delim = `\r\n--${boundary}--`;

    let body = delimiter;
    body += 'Content-Type: application/json\r\n\r\n';
    body += JSON.stringify(metadata) + delimiter;
    body += `Content-Type: ${mimeType}\r\n\r\n`;
    body += content || '';
    body += close_delim;

    const response = await authenticatedPost(url, body, {
      userId,
      toolType: 'google_drive',
      additionalHeaders: {
        'Content-Type': `multipart/related; boundary="${boundary}"`
      },
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Drive API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      file: data
    };
  }

  static async searchFiles(userId: string, params: any, timeout: number) {
    const { query, limit = 10 } = params;
    if (!query) throw new Error('Search query is required');

    const url = 'https://www.googleapis.com/drive/v3/files';
    const queryParams = new URLSearchParams({
      q: `name contains '${query}' or fullText contains '${query}'`,
      pageSize: limit.toString(),
      fields: 'files(id,name,mimeType,size,modifiedTime,webViewLink)'
    });

    const response = await authenticatedGet(`${url}?${queryParams}`, {
      userId,
      toolType: 'google_drive',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Drive API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      files: data.files,
      query,
      count: data.files?.length || 0
    };
  }
}

// Google Docs API implementations
export class GoogleDocsAPI {
  static async createDocument(userId: string, params: any, timeout: number) {
    const { title } = params;
    if (!title) throw new Error('Document title is required');

    const url = 'https://docs.googleapis.com/v1/documents';
    const body = {
      title
    };

    const response = await authenticatedPost(url, body, {
      userId,
      toolType: 'google_docs',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Docs API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      document: data
    };
  }

  static async getDocument(userId: string, params: any, timeout: number) {
    const { documentId } = params;
    if (!documentId) throw new Error('Document ID is required');

    const url = `https://docs.googleapis.com/v1/documents/${documentId}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'google_docs',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Docs API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      document: data
    };
  }

  static async updateDocument(userId: string, params: any, timeout: number) {
    const { documentId, requests } = params;
    if (!documentId) throw new Error('Document ID is required');
    if (!requests) throw new Error('Update requests are required');

    const url = `https://docs.googleapis.com/v1/documents/${documentId}:batchUpdate`;
    const body = { requests };

    const response = await authenticatedPost(url, body, {
      userId,
      toolType: 'google_docs',
      timeout
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Google Docs API error ${response.status}:`, errorText);
      throw new Error(`Google Docs API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    return {
      success: true,
      result: data
    };
  }

  static async deleteDocument(userId: string, params: any, timeout: number) {
    const { documentId } = params;
    if (!documentId) throw new Error('Document ID is required');

    // Google Docs API doesn't have a direct delete endpoint
    // We need to use Google Drive API to delete the document file
    const url = `https://www.googleapis.com/drive/v3/files/${documentId}`;

    const response = await authenticatedDelete(url, {
      userId,
      toolType: 'google_docs',
      timeout
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Google Drive API error ${response.status}:`, errorText);
      throw new Error(`Google Drive API error: ${response.status} - ${errorText}`);
    }

    return {
      success: true,
      message: 'Document deleted successfully',
      documentId
    };
  }
}

// Google Sheets API implementations
export class GoogleSheetsAPI {
  static async createSpreadsheet(userId: string, params: any, timeout: number) {
    const { title, sheets } = params;
    if (!title) throw new Error('Spreadsheet title is required');

    const url = 'https://sheets.googleapis.com/v4/spreadsheets';
    const body = {
      properties: { title },
      ...(sheets && { sheets })
    };

    const response = await authenticatedPost(url, body, {
      userId,
      toolType: 'google_sheets',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Sheets API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      spreadsheet: data
    };
  }

  static async getSpreadsheet(userId: string, params: any, timeout: number) {
    const { spreadsheetId, ranges } = params;
    if (!spreadsheetId) throw new Error('Spreadsheet ID is required');

    let url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}`;
    if (ranges) {
      const rangeParams = Array.isArray(ranges) ? ranges.join('&ranges=') : ranges;
      url += `?ranges=${rangeParams}`;
    }

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'google_sheets',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Sheets API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      spreadsheet: data
    };
  }

  static async updateCells(userId: string, params: any, timeout: number) {
    const { spreadsheetId, range, values } = params;
    if (!spreadsheetId) throw new Error('Spreadsheet ID is required');
    if (!range) throw new Error('Range is required');
    if (!values) throw new Error('Values are required');

    const url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${range}?valueInputOption=RAW`;
    const body = { values };

    const response = await authenticatedPut(url, body, {
      userId,
      toolType: 'google_sheets',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Sheets API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      result: data
    };
  }

  static async readRange(userId: string, params: any, timeout: number) {
    const { spreadsheetId, range } = params;
    if (!spreadsheetId) throw new Error('Spreadsheet ID is required');
    if (!range) throw new Error('Range is required');

    const url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${range}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'google_sheets',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Sheets API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      values: data.values || [],
      range: data.range
    };
  }

  static async appendRow(userId: string, params: any, timeout: number) {
    const { spreadsheetId, range, values } = params;
    if (!spreadsheetId) throw new Error('Spreadsheet ID is required');
    if (!range) throw new Error('Range is required');
    if (!values) throw new Error('Values are required');

    const url = `https://sheets.googleapis.com/v4/spreadsheets/${spreadsheetId}/values/${range}:append?valueInputOption=RAW`;
    const body = { values: [values] };

    const response = await authenticatedPost(url, body, {
      userId,
      toolType: 'google_sheets',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Google Sheets API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      result: data
    };
  }
}

// Gmail API implementations
export class GmailAPI {
  static async sendEmail(userId: string, params: any, timeout: number) {
    const { to, subject, body, from } = params;
    if (!to) throw new Error('Recipient email is required');
    if (!subject) throw new Error('Email subject is required');
    if (!body) throw new Error('Email body is required');

    // Create email message
    const email = [
      `To: ${to}`,
      `Subject: ${subject}`,
      '',
      body
    ].join('\n');

    const encodedEmail = Buffer.from(email).toString('base64').replace(/\+/g, '-').replace(/\//g, '_');

    const url = 'https://gmail.googleapis.com/gmail/v1/users/me/messages/send';
    const requestBody = {
      raw: encodedEmail
    };

    const response = await authenticatedPost(url, requestBody, {
      userId,
      toolType: 'gmail',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Gmail API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      message: data,
      sent_to: to,
      subject
    };
  }

  static async listEmails(userId: string, params: any, timeout: number) {
    const { maxResults = 10, query } = params;

    let url = `https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults=${maxResults}`;
    if (query) {
      url += `&q=${encodeURIComponent(query)}`;
    }

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'gmail',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Gmail API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      messages: data.messages || [],
      resultSizeEstimate: data.resultSizeEstimate
    };
  }

  static async getEmail(userId: string, params: any, timeout: number) {
    const { messageId } = params;
    if (!messageId) throw new Error('Message ID is required');

    const url = `https://gmail.googleapis.com/gmail/v1/users/me/messages/${messageId}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'gmail',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Gmail API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      message: data
    };
  }

  static async searchEmails(userId: string, params: any, timeout: number) {
    const { query, maxResults = 10 } = params;
    if (!query) throw new Error('Search query is required');

    const url = `https://gmail.googleapis.com/gmail/v1/users/me/messages?maxResults=${maxResults}&q=${encodeURIComponent(query)}`;

    const response = await authenticatedGet(url, {
      userId,
      toolType: 'gmail',
      timeout
    });

    if (!response.ok) {
      throw new Error(`Gmail API error: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      messages: data.messages || [],
      query,
      resultSizeEstimate: data.resultSizeEstimate
    };
  }
}
