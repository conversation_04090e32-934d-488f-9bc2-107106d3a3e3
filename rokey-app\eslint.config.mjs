// @ts-check
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import ts from "typescript-eslint";
import { FlatCompat } from "@eslint/eslintrc";
import { fixupConfigRules } from "@eslint/compat";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
  allConfig: js.configs.all,
});

// Apply compatibility fixes for Next.js ESLint rules
const patchedConfig = fixupConfigRules([
  ...compat.extends("next/core-web-vitals")
]);

const config = [
  ...patchedConfig,
  ...ts.configs.recommended,
  {
    rules: {
      // TypeScript rules - warnings only
      "@typescript-eslint/no-unused-vars": "warn",
      "@typescript-eslint/no-explicit-any": "warn",
      "@typescript-eslint/no-require-imports": "warn",

      // React rules - warnings only
      "react/no-unescaped-entities": "warn",
      "react-hooks/exhaustive-deps": "warn",

      // Next.js rules - warnings only
      "@next/next/no-img-element": "warn",

      // General rules - warnings only
      "prefer-const": "warn",
    },
  },
  {
    ignores: [
      ".next/*",
      "node_modules/*",
      "_disabled/*",
      "*.config.js",
      "*.config.mjs",
      "clear-dev-cache.js",
      "fix-cache-issues.js",
      "fix-route-params.js",
      "network-diagnostic.js",
      "scripts/*"
    ]
  },
];

export default config;
