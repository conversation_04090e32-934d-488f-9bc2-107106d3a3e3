'use client';

import { useState, useEffect, useCallback, Suspense } from 'react';
import { useRouter } from 'next/navigation';
import {
  ChartBarIcon,
  KeyIcon,
  BeakerIcon,
  DocumentTextIcon,
  ArrowTrendingUpIcon,
  ClockIcon,
  CpuChipIcon,
  GlobeAltIcon,
  CurrencyDollarIcon,
  CheckCircleIcon,
  PlusIcon,
  ExclamationTriangleIcon,
  ChartPieIcon,
  FunnelIcon,
  ArrowPathIcon,
  HandThumbUpIcon
} from '@heroicons/react/24/outline';
import { useSubscription } from '@/hooks/useSubscription';

interface AnalyticsSummary {
  total_requests: number;
  successful_requests: number;
  success_rate: number;
  total_cost: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_tokens: number;
  average_cost_per_request: number;
  average_latency?: number;
}

interface AnalyticsData {
  summary: AnalyticsSummary;
  grouped_data: any[];
  filters: any;
}

interface CustomConfig {
  id: string;
  name: string;
}

interface Trend {
  percentage: number;
  isPositive: boolean;
}

const AnalyticsPageContent = () => {
  const router = useRouter();
  const { subscriptionStatus, usageStatus, user } = useSubscription();
  
  // State management
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [previousPeriodData, setPreviousPeriodData] = useState<AnalyticsData | null>(null);
  const [modelAnalytics, setModelAnalytics] = useState<AnalyticsData | null>(null);
  const [customConfigs, setCustomConfigs] = useState<CustomConfig[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Filter states
  const [timeRange, setTimeRange] = useState('30');
  const [selectedConfig, setSelectedConfig] = useState('');
  const [selectedProvider, setSelectedProvider] = useState('');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [monthlyBudget, setMonthlyBudget] = useState(100);
  const [showFilters, setShowFilters] = useState(false);

  // Utility functions
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  };

  const calculateTrend = (current: number, previous: number): Trend => {
    if (previous === 0) return { percentage: 0, isPositive: true };
    const change = ((current - previous) / previous) * 100;
    return {
      percentage: Math.abs(change),
      isPositive: change >= 0
    };
  };

  // Data fetching
  const fetchAnalyticsData = useCallback(async () => {
    if (!user?.id) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const params = new URLSearchParams({
        startDate: startDate || new Date(Date.now() - parseInt(timeRange) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        endDate: endDate || new Date().toISOString().split('T')[0],
        groupBy: 'provider'
      });
      
      if (selectedConfig) params.append('customApiConfigId', selectedConfig);
      
      const response = await fetch(`/api/analytics/summary?${params}`);
      if (!response.ok) throw new Error('Failed to fetch analytics');
      
      const data = await response.json();
      setAnalyticsData(data);
      
      // Fetch previous period for comparison
      const prevParams = new URLSearchParams({
        startDate: new Date(Date.now() - parseInt(timeRange) * 2 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        endDate: new Date(Date.now() - parseInt(timeRange) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        groupBy: 'provider'
      });
      
      if (selectedConfig) prevParams.append('customApiConfigId', selectedConfig);
      
      const prevResponse = await fetch(`/api/analytics/summary?${prevParams}`);
      if (prevResponse.ok) {
        const prevData = await prevResponse.json();
        setPreviousPeriodData(prevData);
      }
      
      // Fetch model analytics
      const modelParams = new URLSearchParams(params);
      modelParams.set('groupBy', 'model');
      
      const modelResponse = await fetch(`/api/analytics/summary?${modelParams}`);
      if (modelResponse.ok) {
        const modelData = await modelResponse.json();
        setModelAnalytics(modelData);
      }
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [user?.id, timeRange, selectedConfig, startDate, endDate]);

  const fetchCustomConfigs = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      const response = await fetch('/api/custom-configs');
      if (response.ok) {
        const configs = await response.json();
        setCustomConfigs(configs);
      }
    } catch (err) {
      console.error('Failed to fetch custom configs:', err);
    }
  }, [user?.id]);

  const resetFilters = () => {
    setTimeRange('30');
    setSelectedConfig('');
    setSelectedProvider('');
    setStartDate('');
    setEndDate('');
    setMonthlyBudget(100);
  };

  useEffect(() => {
    fetchCustomConfigs();
  }, [fetchCustomConfigs]);

  useEffect(() => {
    fetchAnalyticsData();
  }, [fetchAnalyticsData]);

  const summary = analyticsData?.summary;
  const costEfficiency = summary ? summary.total_cost / Math.max(summary.successful_requests, 1) : 0;
  const projectedMonthlyCost = summary ? (summary.total_cost / parseInt(timeRange)) * 30 : 0;

  // Calculate trends vs previous period
  const previousSummary = previousPeriodData?.summary;
  const costTrend = previousSummary ? calculateTrend(summary?.total_cost || 0, previousSummary.total_cost) : null;
  const requestTrend = previousSummary ? calculateTrend(summary?.total_requests || 0, previousSummary.total_requests) : null;

  return (
    <div className="min-h-screen bg-[#040716] text-white">
      {/* Header Section - Following reference design */}
      <div className="border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-8">
              <h1 className="text-2xl font-semibold text-white">Analytics</h1>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <span>🔍</span>
                <input 
                  type="text" 
                  placeholder="Search Filter"
                  className="bg-transparent border-none outline-none text-gray-400 placeholder-gray-500"
                />
              </div>
              <select
                value={timeRange}
                onChange={(e) => setTimeRange(e.target.value)}
                className="px-3 py-1 bg-gray-800 border border-gray-700 rounded text-sm text-white focus:outline-none focus:border-cyan-500"
              >
                <option value="7">2023-07-06 - Dec 2023</option>
                <option value="30">Last 30 days</option>
                <option value="90">Last 90 days</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs - Following reference design */}
      <div className="border-b border-gray-800">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex items-center space-x-8">
            <button className="flex items-center space-x-2 py-4 border-b-2 border-cyan-500 text-cyan-500">
              <span>👁️</span>
              <span className="text-sm font-medium">Overview</span>
            </button>
            <button className="flex items-center space-x-2 py-4 text-gray-400 hover:text-white transition-colors">
              <span>👥</span>
              <span className="text-sm">Users</span>
            </button>
            <button className="flex items-center space-x-2 py-4 text-gray-400 hover:text-white transition-colors">
              <span>⚠️</span>
              <span className="text-sm">Errors</span>
            </button>
            <button className="flex items-center space-x-2 py-4 text-gray-400 hover:text-white transition-colors">
              <span>🗄️</span>
              <span className="text-sm">Cache</span>
            </button>
            <button className="flex items-center space-x-2 py-4 text-gray-400 hover:text-white transition-colors">
              <span>💬</span>
              <span className="text-sm">Feedback</span>
            </button>
            <button className="flex items-center space-x-2 py-4 text-gray-400 hover:text-white transition-colors">
              <span>📊</span>
              <span className="text-sm">Metadata</span>
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 py-6 space-y-6">
        {/* Main Metrics Grid - Following reference design */}
        {summary && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {/* Total Request Made */}
            <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm text-gray-400 mb-1">Total Request Made</p>
                  <div className="flex items-center space-x-3">
                    <span className="text-3xl font-bold text-white">
                      {formatNumber(summary.total_requests)}
                    </span>
                    {requestTrend && (
                      <span className={`text-sm px-2 py-1 rounded ${
                        requestTrend.isPositive ? 'text-green-400 bg-green-400/10' : 'text-red-400 bg-red-400/10'
                      }`}>
                        {requestTrend.isPositive ? '+' : ''}{requestTrend.percentage.toFixed(1)}%
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-gray-500">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                </div>
              </div>
            </div>

            {/* Average Latency */}
            <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm text-gray-400 mb-1">Average Latency</p>
                  <div className="flex items-center space-x-3">
                    <span className="text-3xl font-bold text-white">
                      {summary.average_cost_per_request ? (summary.average_cost_per_request * 1000).toFixed(0) : '0'}ms
                    </span>
                    <span className="text-sm text-orange-400 bg-orange-400/10 px-2 py-1 rounded">
                      +20.34%
                    </span>
                  </div>
                </div>
                <div className="text-gray-500">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
                  </svg>
                </div>
              </div>
            </div>

            {/* User Feedback */}
            <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm text-gray-400 mb-1">User Feedback</p>
                  <div className="flex items-center space-x-3">
                    <span className="text-3xl font-bold text-white">
                      {summary.success_rate.toFixed(1)}%
                    </span>
                    <span className="text-sm text-red-400 bg-red-400/10 px-2 py-1 rounded">
                      -1.34%
                    </span>
                  </div>
                </div>
                <div className="text-gray-500">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 10.5a1.5 1.5 0 113 0v6a1.5 1.5 0 01-3 0v-6zM6 10.333v5.43a2 2 0 001.106 1.79l.05.025A4 4 0 008.943 18h5.416a2 2 0 001.962-1.608l1.2-6A2 2 0 0015.56 8H12V4a2 2 0 00-2-2 1 1 0 00-1 1v.667a4 4 0 01-.8 2.4L6.8 7.933a4 4 0 00-.8 2.4z"/>
                  </svg>
                </div>
              </div>
            </div>

            {/* Total Cost */}
            <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <p className="text-sm text-gray-400 mb-1">Total Cost</p>
                  <div className="flex items-center space-x-3">
                    <span className="text-3xl font-bold text-white">
                      {formatCurrency(summary.total_cost)}
                    </span>
                    {costTrend && (
                      <span className={`text-sm px-2 py-1 rounded ${
                        costTrend.isPositive ? 'text-red-400 bg-red-400/10' : 'text-green-400 bg-green-400/10'
                      }`}>
                        {costTrend.isPositive ? '+' : ''}{costTrend.percentage.toFixed(1)}%
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-gray-500">
                  <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"/>
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z" clipRule="evenodd"/>
                  </svg>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Charts Section - Following reference layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Cost Chart */}
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-white">Cost</h3>
                <div className="flex items-center space-x-2 mt-2">
                  <span className="text-3xl font-bold text-white">
                    {formatCurrency(summary?.total_cost || 0)}
                  </span>
                  <span className="text-sm text-red-400 bg-red-400/10 px-2 py-1 rounded">
                    -10.46%
                  </span>
                </div>
              </div>
              <div className="text-gray-500">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"/>
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"/>
                </svg>
              </div>
            </div>

            {/* Cost Bar Chart */}
            <div className="h-48 flex items-end space-x-2">
              {Array.from({ length: 30 }, (_, i) => (
                <div
                  key={i}
                  className="flex-1 bg-green-500 rounded-t"
                  style={{
                    height: `${Math.random() * 100 + 20}%`,
                    opacity: 0.7 + Math.random() * 0.3
                  }}
                />
              ))}
            </div>
          </div>

          {/* Latency Chart */}
          <div className="bg-gray-900 rounded-lg p-6 border border-gray-800">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h3 className="text-lg font-semibold text-white">Latency</h3>
                <div className="flex items-center space-x-2 mt-2">
                  <span className="text-3xl font-bold text-white">
                    {summary?.average_cost_per_request ? (summary.average_cost_per_request * 1000).toFixed(0) : '0'}ms
                  </span>
                  <span className="text-sm text-orange-400 bg-orange-400/10 px-2 py-1 rounded">
                    +112%
                  </span>
                </div>
              </div>
              <div className="text-gray-500">
                <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd"/>
                </svg>
              </div>
            </div>

            {/* Latency Line Chart */}
            <div className="h-48 relative">
              <svg className="w-full h-full" viewBox="0 0 300 150">
                <defs>
                  <linearGradient id="latencyGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                    <stop offset="0%" stopColor="#f59e0b" stopOpacity="0.3"/>
                    <stop offset="100%" stopColor="#f59e0b" stopOpacity="0"/>
                  </linearGradient>
                </defs>
                <path
                  d="M0,120 Q50,80 100,90 T200,70 T300,85"
                  stroke="#f59e0b"
                  strokeWidth="3"
                  fill="none"
                  className="drop-shadow-sm"
                />
                <path
                  d="M0,120 Q50,80 100,90 T200,70 T300,85 L300,150 L0,150 Z"
                  fill="url(#latencyGradient)"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
