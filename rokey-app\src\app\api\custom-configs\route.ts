import { type NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { canPerformAction, getTierConfig } from '@/lib/stripe-client';

// POST /api/custom-configs
// Creates a new custom API configuration
export async function POST(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  // Get authenticated user (more secure than getSession)
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Authentication error in POST /api/custom-configs:', authError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to create configurations.' }, { status: 401 });
  }

  try {
    const { name } = await request.json();

    if (!name || typeof name !== 'string' || name.trim().length === 0) {
      return NextResponse.json({ error: 'Configuration name is required and must be a non-empty string' }, { status: 400 });
    }
    if (name.length > 255) {
      return NextResponse.json({ error: 'Configuration name must be 255 characters or less' }, { status: 400 });
    }

    // Check user's subscription tier and configuration limits
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('tier')
      .eq('user_id', user.id)
      .eq('status', 'active')
      .single();

    const userTier = subscription?.tier || 'free';

    // Count current configurations for this user
    const { count: currentConfigCount } = await supabase
      .from('custom_api_configs')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', user.id);

    // Check if user can create more configurations
    if (!canPerformAction(userTier as any, 'create_config', currentConfigCount || 0)) {
      const tierConfig = getTierConfig(userTier as any);
      return NextResponse.json({
        error: `You have reached the maximum number of configurations (${tierConfig.limits.configurations}) for your ${userTier} plan. Please upgrade to create more configurations.`
      }, { status: 403 });
    }

    const { data, error } = await supabase
      .from('custom_api_configs')
      .insert([
        { name, user_id: user.id }, // Use authenticated user's ID
      ])
      .select()
      .single();

    if (error) {
      console.error('Supabase error creating custom config:', error);
      return NextResponse.json({ error: 'Failed to create custom API configuration', details: error.message }, { status: 500 });
    }

    return NextResponse.json(data, { status: 201 });
  } catch (e: any) {
    console.error('Error in POST /api/custom-configs:', e);
    if (e.name === 'SyntaxError') { // Likely JSON parsing error
        return NextResponse.json({ error: 'Invalid request body: Malformed JSON.' }, { status: 400 });
    }
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
}

// GET /api/custom-configs
// Lists all custom API configurations (for the authenticated user in M13)
export async function GET(request: NextRequest) {
  const supabase = createSupabaseServerClientFromRequest(request);

  // Get authenticated user (more secure than getSession)
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    console.error('Authentication error in GET /api/custom-configs:', authError);
    return NextResponse.json({ error: 'Unauthorized: You must be logged in to view configurations.' }, { status: 401 });
  }

  try {
    // Phase 2A Optimization: Optimized query with only needed fields and caching
    const { data, error } = await supabase
      .from('custom_api_configs')
      .select('id, name, created_at, updated_at, routing_strategy, browsing_enabled, browsing_models') // Include browsing fields
      .eq('user_id', user.id) // Filter by authenticated user's ID
      .order('created_at', { ascending: false })
      .limit(100); // Reasonable limit to prevent large responses

    if (error) {
      console.error('Supabase error fetching custom configs:', error);
      return NextResponse.json({ error: 'Failed to fetch custom API configurations', details: error.message }, { status: 500 });
    }

    const response = NextResponse.json(data || [], { status: 200 });

    // Phase 2A Optimization: Add aggressive caching headers
    const isPrefetch = request.headers.get('X-Prefetch') === 'true';
    const cacheMaxAge = isPrefetch ? 600 : 120; // 10 minutes for prefetch, 2 minutes for regular

    response.headers.set('Cache-Control', `private, max-age=${cacheMaxAge}, stale-while-revalidate=300`);
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('Vary', 'X-Prefetch');

    return response;
  } catch (e: any) {
    console.error('Error in GET /api/custom-configs:', e);
    return NextResponse.json({ error: 'An unexpected error occurred', details: e.message }, { status: 500 });
  }
} 