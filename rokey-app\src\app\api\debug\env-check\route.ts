import { NextRequest, NextResponse } from 'next/server';

/**
 * Debug endpoint to check environment variable availability
 * Only accessible with proper API key for security
 */
export async function GET(request: NextRequest) {
  try {
    // Check for API key authentication
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.ROKEY_API_ACCESS_TOKEN;
    
    if (!expectedToken || authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check environment variables
    const envCheck = {
      NODE_ENV: process.env.NODE_ENV,
      VERCEL_ENV: process.env.VERCEL_ENV,
      RESEND_API_KEY_EXISTS: !!process.env.RESEND_API_KEY,
      RESEND_API_KEY_LENGTH: process.env.RESEND_API_KEY?.length || 0,
      RESEND_API_KEY_PREFIX: process.env.RESEND_API_KEY?.substring(0, 8) || 'N/A',
      ROKEY_API_ACCESS_TOKEN_EXISTS: !!process.env.ROKEY_API_ACCESS_TOKEN,
      SUPABASE_SERVICE_ROLE_KEY_EXISTS: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      timestamp: new Date().toISOString()
    };

    console.log('🔍 ENV-CHECK: Environment variables status:', envCheck);

    return NextResponse.json({
      success: true,
      environment: envCheck
    });

  } catch (error) {
    console.error('❌ ENV-CHECK: Error checking environment:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
