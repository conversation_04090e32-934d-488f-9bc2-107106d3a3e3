// Debug endpoint to check workflow tool detection
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';

export async function POST(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createSupabaseServerClientFromRequest(request);
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const { nodes, edges } = await request.json();

    // Find all tool nodes in the workflow
    const toolNodes = nodes.filter((node: any) => node.type === 'tool');
    
    const toolAnalysis = toolNodes.map((toolNode: any) => {
      const config = toolNode.data.config;
      return {
        nodeId: toolNode.id,
        toolType: config?.toolType,
        isAuthenticated: config?.isAuthenticated,
        connectionStatus: config?.connectionStatus,
        providerUserEmail: config?.providerUserEmail,
        isConnectedForClassification: config?.isAuthenticated && config?.connectionStatus === 'connected'
      };
    });

    // Simulate the same logic as getAvailableToolsFromWorkflow
    const availableTools = toolNodes
      .filter((toolNode: any) => toolNode.data.config?.toolType)
      .map((toolNode: any) => {
        const config = toolNode.data.config;
        const toolDisplayNames: Record<string, string> = {
          google_drive: 'Google Drive',
          google_docs: 'Google Docs',
          google_sheets: 'Google Sheets',
          gmail: 'Gmail',
          calendar: 'Google Calendar',
          youtube: 'YouTube',
          notion: 'Notion',
          supabase: 'Supabase'
        };

        const toolName = toolDisplayNames[config.toolType] || config.toolType;
        const isConnected = config.isAuthenticated && config.connectionStatus === 'connected';
        
        return {
          id: toolNode.id,
          name: toolName,
          type: config.toolType,
          status: isConnected ? 'connected' : 'disconnected'
        };
      });

    const connectedTools = availableTools.filter((t: any) => t.status === 'connected');

    return NextResponse.json({
      success: true,
      toolNodes: toolAnalysis,
      availableTools,
      connectedTools,
      connectedCount: connectedTools.length,
      toolInfoForPrompt: connectedTools.length > 0
        ? connectedTools.map((t: any) => `- ${t.name} (${t.type})`).join('\n')
        : 'No tools connected to this workflow'
    });

  } catch (error) {
    console.error('Error in workflow tools debug:', error);
    return NextResponse.json(
      { error: 'Failed to analyze workflow tools' },
      { status: 500 }
    );
  }
}
