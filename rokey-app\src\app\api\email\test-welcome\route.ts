import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientFromRequest } from '@/lib/supabase/server';
import { sendWelcomeEmail } from '@/lib/email/welcomeEmail';

/**
 * Test endpoint for welcome emails - only for development/testing
 */
export async function POST(request: NextRequest) {
  try {
    // Only allow in development or with proper API key
    const authHeader = request.headers.get('authorization');
    const expectedToken = process.env.ROKEY_API_ACCESS_TOKEN;
    
    if (process.env.NODE_ENV === 'production' && authHeader !== `Bearer ${expectedToken}`) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { userEmail, userName, userTier } = body;

    if (!userEmail) {
      return NextResponse.json(
        { error: 'userEmail is required' },
        { status: 400 }
      );
    }

    console.log('🧪 TEST-WELCOME: Sending test welcome email to:', userEmail);

    // Send test welcome email
    const success = await sendWelcomeEmail({
      userEmail,
      userName: userName || 'Test User',
      userTier: userTier || 'free'
    });

    if (success) {
      console.log('✅ TEST-WELCOME: Successfully sent test email');
      return NextResponse.json({ 
        success: true, 
        message: 'Test welcome email sent successfully' 
      });
    } else {
      console.error('❌ TEST-WELCOME: Failed to send test email');
      return NextResponse.json(
        { error: 'Failed to send test welcome email' },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('❌ TEST-WELCOME: Unexpected error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
