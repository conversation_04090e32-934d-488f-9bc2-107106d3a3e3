import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClientOnRequest } from '@/lib/supabase/server';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const memoryName = searchParams.get('memoryName');
    const nodeId = searchParams.get('nodeId');

    if (!memoryName || !nodeId) {
      return NextResponse.json(
        { error: 'Missing memoryName or nodeId parameter' },
        { status: 400 }
      );
    }

    const supabase = await createSupabaseServerClientOnRequest();

    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Query memory entries for this specific memory instance
    const { data: memoryEntries, error: queryError } = await supabase
      .from('workflow_memory')
      .select('*')
      .eq('memory_name', memoryName)
      .eq('node_id', nodeId)
      .eq('user_id', user.id);

    if (queryError) {
      console.error('Error querying memory stats:', queryError);
      return NextResponse.json(
        { error: 'Failed to fetch memory stats' },
        { status: 500 }
      );
    }

    // Calculate stats
    const entriesCount = memoryEntries?.length || 0;
    let totalSizeKB = 0;
    let lastUpdate = 'Never';

    if (memoryEntries && memoryEntries.length > 0) {
      // Calculate total size
      totalSizeKB = memoryEntries.reduce((total, entry) => {
        return total + (entry.metadata?.size_kb || 0);
      }, 0);

      // Find most recent update
      const sortedEntries = memoryEntries.sort((a, b) => 
        new Date(b.metadata?.updated_at || b.created_at).getTime() - 
        new Date(a.metadata?.updated_at || a.created_at).getTime()
      );
      
      if (sortedEntries[0]) {
        const updateTime = new Date(sortedEntries[0].metadata?.updated_at || sortedEntries[0].created_at);
        lastUpdate = updateTime.toLocaleTimeString();
      }
    }

    // Format size
    const totalSizeMB = totalSizeKB / 1024;
    const formattedSize = totalSizeMB < 0.1 ? 
      `${totalSizeKB.toFixed(1)}KB` : 
      `${totalSizeMB.toFixed(1)}MB`;

    const stats = {
      entriesCount,
      totalSize: formattedSize,
      lastUpdate
    };

    return NextResponse.json(stats);

  } catch (error) {
    console.error('Memory stats API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
