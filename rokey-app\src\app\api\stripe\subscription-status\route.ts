import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { STRIPE_ENV_INFO } from '@/lib/stripe-config';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const userId = searchParams.get('userId');

    console.log('Subscription status API called for user:', userId);

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId parameter' },
        { status: 400 }
      );
    }

    // Check for network connectivity issues
    if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {
      console.log('Network connectivity issue detected, returning fallback response');
      return NextResponse.json({
        hasActiveSubscription: true,
        tier: 'free',
        status: 'active',
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        isFree: true,
        fallback: true,
        message: 'Using fallback due to network connectivity issues'
      });
    }

    // Get user's subscription and profile
    const { data: subscription, error: subscriptionError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('subscription_tier, updated_at')
      .eq('id', userId)
      .single();

    console.log('GET subscription-status - Data fetched:', {
      userId,
      subscriptionFound: !!subscription,
      subscriptionTier: subscription?.tier,
      subscriptionStatus: subscription?.status,
      subscriptionUpdatedAt: subscription?.updated_at,
      profileTier: profile?.subscription_tier,
      profileUpdatedAt: profile?.updated_at,
      subscriptionError: subscriptionError?.message,
      profileError: profileError?.message
    });

    if (profileError) {
      console.error('Error fetching user profile:', profileError);
      return NextResponse.json(
        { error: 'User profile not found' },
        { status: 404 }
      );
    }

    // If no subscription found, user is on free tier
    if (subscriptionError || !subscription) {
      const tier = profile.subscription_tier || 'free';
      console.log('GET subscription-status - No active subscription found:', {
        userId,
        subscriptionError: subscriptionError?.message,
        profileTier: profile.subscription_tier,
        finalTier: tier
      });
      return NextResponse.json({
        hasActiveSubscription: tier !== 'free',
        tier,
        status: null,
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        isFree: tier === 'free',
      });
    }

    // Check if subscription is active
    const isActive = subscription.status === 'active';
    const currentPeriodEnd = new Date(subscription.current_period_end);
    const isExpired = currentPeriodEnd < new Date();

    console.log('GET subscription-status - Active subscription found:', {
      userId,
      tier: subscription.tier,
      status: subscription.status,
      isActive,
      isExpired,
      hasActiveSubscription: isActive && !isExpired
    });

    return NextResponse.json({
      hasActiveSubscription: isActive && !isExpired,
      tier: subscription.tier,
      status: subscription.status,
      currentPeriodEnd: subscription.current_period_end,
      currentPeriodStart: subscription.current_period_start,
      cancelAtPeriodEnd: subscription.cancel_at_period_end,
      stripeCustomerId: subscription.stripe_customer_id,
      stripeSubscriptionId: subscription.stripe_subscription_id,
      isFree: subscription.is_free_tier || false,
    });

  } catch (error) {
    console.error('Error fetching subscription status:', error);

    // If there's a network error, return fallback response
    if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {
      console.log('Network error detected, returning fallback response');
      return NextResponse.json({
        hasActiveSubscription: true,
        tier: 'free',
        status: 'active',
        currentPeriodEnd: null,
        cancelAtPeriodEnd: false,
        isFree: true,
        fallback: true,
        message: 'Using fallback due to network error'
      });
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { userId } = await req.json();

    if (!userId) {
      return NextResponse.json(
        { error: 'Missing userId' },
        { status: 400 }
      );
    }

    // Check for network connectivity issues
    if (process.env.SUPABASE_SERVER_CONNECTIVITY_ISSUE === 'true') {
      console.log('Network connectivity issue detected, returning fallback usage response');
      return NextResponse.json({
        tier: 'free',
        usage: {
          configurations: 0,
          apiKeys: 0,
          apiRequests: 0,
        },
        limits: getTierLimits('free'),
        canCreateConfig: true,
        canCreateApiKey: true,
        fallback: true,
        message: 'Using fallback due to network connectivity issues'
      });
    }

    // Get current usage for the user
    const currentMonth = new Date().toISOString().slice(0, 7); // YYYY-MM format

    const { data: usage, error: usageError } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('month_year', currentMonth)
      .single();

    // Get user's current tier - check subscriptions table first, then user_profiles
    // First try to get active subscription
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('tier, status, updated_at, stripe_subscription_id')
      .eq('user_id', userId)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(1)
      .single();

    // If no active subscription, get the most recent subscription regardless of status
    // This helps handle cases where subscription status might be temporarily in transition
    let fallbackSubscription = null;
    if (subError) {
      const { data: recentSub } = await supabase
        .from('subscriptions')
        .select('tier, status, updated_at, stripe_subscription_id')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false })
        .limit(1)
        .single();

      fallbackSubscription = recentSub;
    }

    const { data: profile, error: profError } = await supabase
      .from('user_profiles')
      .select('subscription_tier, updated_at')
      .eq('id', userId)
      .single();

    // Also check for ANY subscription (not just active) for debugging
    const { data: allSubscriptions } = await supabase
      .from('subscriptions')
      .select('tier, status, updated_at, stripe_subscription_id')
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    // Use subscription tier if available and active, otherwise fall back to profile tier
    // Priority: 1) Active subscription, 2) Recent subscription (if profile matches), 3) Profile tier
    let tier: string;
    if (subscription?.tier && subscription?.status === 'active') {
      tier = subscription.tier;
    } else if (fallbackSubscription?.tier && profile?.subscription_tier === fallbackSubscription.tier) {
      // Use fallback subscription tier only if it matches the profile tier (indicates recent update)
      tier = fallbackSubscription.tier;
    } else {
      tier = profile?.subscription_tier || 'free';
    }

    console.log('POST subscription-status - User tier determination:', {
      userId,
      activeSubscriptionTier: subscription?.tier,
      activeSubscriptionStatus: subscription?.status,
      activeSubscriptionUpdatedAt: subscription?.updated_at,
      activeSubscriptionId: subscription?.stripe_subscription_id,
      fallbackSubscriptionTier: fallbackSubscription?.tier,
      fallbackSubscriptionStatus: fallbackSubscription?.status,
      fallbackSubscriptionUpdatedAt: fallbackSubscription?.updated_at,
      profileTier: profile?.subscription_tier,
      profileUpdatedAt: profile?.updated_at,
      finalTier: tier,
      tierDeterminationLogic: subscription?.tier && subscription?.status === 'active'
        ? 'Using active subscription'
        : fallbackSubscription?.tier && profile?.subscription_tier === fallbackSubscription.tier
        ? 'Using fallback subscription (matches profile)'
        : 'Using profile tier',
      allSubscriptionsCount: allSubscriptions?.length || 0,
      allSubscriptions: allSubscriptions?.map(s => ({
        tier: s.tier,
        status: s.status,
        updatedAt: s.updated_at,
        stripeId: s.stripe_subscription_id
      })),
      subscriptionError: subError?.message,
      profileError: profError?.message
    });

    // Get configuration and API key counts
    const { count: configCount } = await supabase
      .from('custom_api_configs')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    const { count: apiKeyCount } = await supabase
      .from('api_keys')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId);

    // Note: Workflow features will be added in future updates

    // Calculate tier limits
    const limits = getTierLimits(tier);

    return NextResponse.json({
      tier,
      usage: {
        configurations: configCount || 0,
        apiKeys: apiKeyCount || 0,
        apiRequests: usage?.api_requests_count || 0,
      },
      limits,
      canCreateConfig: (configCount || 0) < limits.configurations,
      canCreateApiKey: (apiKeyCount || 0) < limits.apiKeysPerConfig,
    });

  } catch (error) {
    console.error('Error fetching usage status:', error);

    // If there's a network error, return fallback response
    if (error instanceof Error && (error.message.includes('fetch failed') || error.message.includes('network'))) {
      console.log('Network error detected, returning fallback usage response');
      return NextResponse.json({
        tier: 'free',
        usage: {
          configurations: 0,
          apiKeys: 0,
          apiRequests: 0,
        },
        limits: getTierLimits('free'),
        canCreateConfig: true,
        canCreateApiKey: true,
        fallback: true,
        message: 'Using fallback due to network error'
      });
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

function getTierLimits(tier: string) {
  switch (tier) {
    case 'free':
      return {
        configurations: 1,
        apiKeysPerConfig: 3,
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: false,
        canUseCustomRoles: false,
        maxCustomRoles: 0,
        canUsePromptEngineering: false,
        canUseKnowledgeBase: false,
        knowledgeBaseDocuments: 0,
        canUseSemanticCaching: false,
      };
    case 'starter':
      return {
        configurations: 15,
        apiKeysPerConfig: 5,
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: true,
        canUseCustomRoles: true,
        maxCustomRoles: 3,
        canUsePromptEngineering: true,
        canUseKnowledgeBase: false,
        knowledgeBaseDocuments: 0,
        canUseSemanticCaching: false,
      };
    case 'professional':
      return {
        configurations: 999999, // Unlimited
        apiKeysPerConfig: 999999, // Unlimited
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: true,
        canUseCustomRoles: true,
        maxCustomRoles: 999999,
        canUsePromptEngineering: true,
        canUseKnowledgeBase: true,
        knowledgeBaseDocuments: 5,
        canUseSemanticCaching: true,
      };
    case 'enterprise':
      return {
        configurations: 999999, // Unlimited
        apiKeysPerConfig: 999999, // Unlimited
        apiRequests: 999999, // Unlimited
        canUseAdvancedRouting: true,
        canUseCustomRoles: true,
        maxCustomRoles: 999999,
        canUsePromptEngineering: true,
        canUseKnowledgeBase: true,
        knowledgeBaseDocuments: 999999,
        canUseSemanticCaching: true,
      };
    default:
      return {
        configurations: 1,
        apiKeysPerConfig: 3,
        apiRequests: 999999,
        canUseAdvancedRouting: false,
        canUseCustomRoles: false,
        maxCustomRoles: 0,
        canUsePromptEngineering: false,
        canUseKnowledgeBase: false,
        knowledgeBaseDocuments: 0,
        canUseSemanticCaching: false,
      };
  }
}
