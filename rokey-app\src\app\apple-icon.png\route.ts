import { NextRequest, NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';

export async function GET(request: NextRequest) {
  try {
    // Serve the glow logo as Apple icon
    const logoPath = path.join(process.cwd(), 'public', 'Rou<PERSON>ey_Logo_GLOW.png');
    const logoBuffer = fs.readFileSync(logoPath);
    
    return new NextResponse(logoBuffer, {
      status: 200,
      headers: {
        'Content-Type': 'image/png',
        'Cache-Control': 'public, max-age=31536000, immutable',
      },
    });
  } catch (error) {
    console.error('Error serving Apple icon:', error);
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return new NextResponse('Apple icon error: ' + errorMessage, { status: 500 });
  }
}
