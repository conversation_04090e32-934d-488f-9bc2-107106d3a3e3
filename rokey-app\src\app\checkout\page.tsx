'use client';

import { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { createSupabaseBrowserClient } from '@/lib/supabase/client';
import Image from 'next/image';
import Link from 'next/link';
import dynamic from 'next/dynamic';

function CheckoutPageContent() {
  console.log('🔥 CheckoutPageContent function called');
  return <ActualCheckoutContent />;
}

function ActualCheckoutContent() {
  // Immediate debug log
  console.log('🔥 ActualCheckoutContent function called');

  // React hooks must be at the top level
  const router = useRouter();
  const searchParams = useSearchParams();
  const supabase = createSupabaseBrowserClient();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [user, setUser] = useState<any>(null);
  const [mounted, setMounted] = useState(false);

  console.log('🔥 ActualCheckoutContent - all hooks initialized');

  const selectedPlan = searchParams.get('plan') || 'starter';
  const userId = searchParams.get('user_id');
  const email = searchParams.get('email');
  const isSignup = searchParams.get('signup') === 'true';

  // Debug the URL params immediately
  console.log('🔍 ActualCheckoutContent URL params parsed:', { selectedPlan, userId, email, isSignup });

  useEffect(() => {
    console.log('🚀 ActualCheckoutContent first useEffect - component mounting...');
    fetch('/api/debug/checkout', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        action: 'COMPONENT_MOUNT',
        message: 'ActualCheckoutContent component is mounting'
      })
    }).catch(() => {});
    console.log('🚀 ActualCheckoutContent - setting mounted to true');
    setMounted(true);
  }, []);

  useEffect(() => {
    console.log('🚀 ActualCheckoutContent second useEffect - mounted:', mounted);
    if (!mounted) {
      console.log('🚀 ActualCheckoutContent - not mounted yet, returning');
      return;
    }

    console.log('=== CHECKOUT PAGE MOUNTED ===');
    console.log('URL params:', { selectedPlan, userId, email, isSignup });
    console.log('Current URL:', window.location.href);

    // Check localStorage for pending signup
    const pendingSignup = localStorage.getItem('pending_signup');
    console.log('localStorage pending_signup:', pendingSignup ? 'FOUND' : 'NOT FOUND');
    if (pendingSignup) {
      console.log('Pending signup data:', JSON.parse(pendingSignup));
    }

    // Add debug function to window for manual testing
    (window as any).debugCheckout = () => {
      console.log('=== DEBUG CHECKOUT ===');
      console.log('localStorage pending_signup:', localStorage.getItem('pending_signup'));
      console.log('URL params:', { selectedPlan, userId, email, isSignup });
    };

    // Listen for auth state changes to handle recent sign-ins
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state change detected:', event, !!session);
        if (event === 'SIGNED_IN' && session) {
          console.log('User just signed in, proceeding with checkout...');
          // Small delay to ensure session is fully established
          setTimeout(() => {
            initializeCheckout();
          }, 100);
        }
      }
    );

    initializeCheckout();

    // Cleanup subscription
    return () => subscription.unsubscribe();
  }, [mounted]);

  const initializeCheckout = async () => {
    try {
      // Send debug info to server for terminal logging
      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'CHECKOUT_INITIALIZATION',
          urlParams: { selectedPlan, userId, email, isSignup },
          currentUrl: window.location.href
        })
      }).catch(() => {}); // Don't fail if debug endpoint fails

      console.log('=== CHECKOUT INITIALIZATION ===');
      console.log('URL params:', { selectedPlan, userId, email, isSignup });

      // Try to get user with retry logic for recent sign-ins (more secure than getSession)
      let user = null;
      let authError = null;
      let retryCount = 0;
      const maxRetries = 3;

      while (!user && retryCount < maxRetries) {
        const result = await supabase.auth.getUser();
        user = result.data.user;
        authError = result.error;

        if (!user && retryCount < maxRetries - 1) {
          console.log(`User not found, retrying in 1 second... (attempt ${retryCount + 1}/${maxRetries})`);
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        retryCount++;
      }

      console.log('User check:', { hasUser: !!user, error: authError, retryCount });

      // Debug user details
      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'USER_CHECK',
          hasUser: !!user,
          authError: authError?.message,
          userId: user?.id,
          userEmail: user?.email,
          retryCount: retryCount,
          maxRetries: maxRetries
        })
      }).catch(() => {});

      if (authError || !user) {
        console.log('No valid user found after', retryCount, 'attempts');
        console.log('Auth error:', authError);
        console.log('User data:', user);

        await fetch('/api/debug/checkout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'AUTH_FAILED',
            error: authError?.message || 'No user found',
            userId: userId,
            email: email,
            hasUserId: !!userId,
            hasEmail: !!email,
            retryCount: retryCount,
            redirecting: true
          })
        }).catch(() => {});

        // If we have a userId from URL params, this means user was created but not signed in
        // This is the new pending user flow - proceed with checkout without authentication
        if (userId && email) {
          console.log('Pending user detected, proceeding with checkout without authentication');

          await fetch('/api/debug/checkout', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              action: 'PENDING_USER_CHECKOUT',
              userId,
              email,
              plan: selectedPlan,
              message: 'Proceeding with checkout for pending user'
            })
          }).catch(() => {});

          // Create checkout session for pending user
          try {
            await createCheckoutSessionForPendingUser(userId, email);
          } catch (error) {
            console.error('Error creating checkout for pending user:', error);
            setError('Failed to create checkout session. Please try again.');
          }
          return;
        } else {
          setError('Authentication required. Please sign up first. Click "Try Again" to go to sign up page.');
          // Don't auto-redirect to prevent loops
          // setTimeout(() => router.push(`/auth/signup?plan=${selectedPlan}`), 3000);
        }
        return;
      }

      setUser(user);
      console.log('Set user state:', user);

      // Check if user has payment_pending status (new signup)
      const userMetadata = user.user_metadata;
      const paymentStatus = userMetadata?.payment_status;

      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'USER_FOUND',
          userId: user.id,
          email: user.email,
          paymentStatus,
          userMetadata
        })
      }).catch(() => {});

      console.log('Processing checkout for user:', user.id);
      console.log('Payment status:', paymentStatus);

      // Debug before calling createCheckoutSession
      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'CALLING_CREATE_CHECKOUT_SESSION',
          userId: user.id,
          selectedPlan,
          aboutToCall: true
        })
      }).catch(() => {});

      console.log('About to call createCheckoutSession...');

      try {
        // Create checkout session for authenticated user
        // Pass the user directly instead of relying on state
        await createCheckoutSession(user.id, user);
        console.log('createCheckoutSession call completed successfully');
      } catch (checkoutError) {
        console.error('Error in createCheckoutSession:', checkoutError);

        await fetch('/api/debug/checkout', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'CREATE_CHECKOUT_SESSION_ERROR',
            error: checkoutError instanceof Error ? checkoutError.message : String(checkoutError),
            stack: checkoutError instanceof Error ? checkoutError.stack : undefined
          })
        }).catch(() => {});

        throw checkoutError; // Re-throw to be caught by outer catch
      }

    } catch (error) {
      console.error('Checkout initialization error:', error);
      await fetch('/api/debug/checkout', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'ERROR',
          error: error instanceof Error ? error.message : String(error)
        })
      }).catch(() => {});
      setError('Failed to initialize checkout. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const createCheckoutSessionForPendingUser = async (userId: string, userEmail: string) => {
    try {
      const priceId = getPriceId(selectedPlan);
      console.log('Creating checkout session for pending user:', {
        userId,
        userEmail,
        plan: selectedPlan,
        priceId: priceId
      });

      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: priceId,
          tier: selectedPlan,
          userId: userId,
          userEmail: userEmail,
          signup: false, // User already exists, just not signed in
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      if (data.url) {
        console.log('Redirecting to Stripe checkout:', data.url);
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL received');
      }

    } catch (error) {
      console.error('Pending user checkout session error:', error);
      throw error;
    }
  };

  const createCheckoutSessionForSignup = async (userData: any) => {
    try {
      const requestData = {
        priceId: getPriceId(selectedPlan),
        tier: selectedPlan,
        userEmail: userData.email,
        signup: true, // Flag to indicate this is a signup
        pendingUserData: userData
      };

      console.log('Creating checkout session for signup:', requestData);
      console.log('Environment check:', {
        hasStarterPrice: !!process.env.NEXT_PUBLIC_STRIPE_STARTER_PRICE_ID,
        hasProfessionalPrice: !!process.env.NEXT_PUBLIC_STRIPE_PROFESSIONAL_PRICE_ID,
        hasEnterprisePrice: !!process.env.NEXT_PUBLIC_STRIPE_ENTERPRISE_PRICE_ID
      });

      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData),
      });

      const data = await response.json();

      console.log('Checkout session response:', { ok: response.ok, status: response.status, data });

      if (!response.ok) {
        console.error('Checkout session creation failed:', data);
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL received');
      }

    } catch (error) {
      console.error('Signup checkout session error:', error);
      setError(error instanceof Error ? error.message : 'Failed to start checkout');
    }
  };

  const createCheckoutSession = async (userId: string, sessionUser?: any) => {
    console.log('🚀 createCheckoutSession function called with userId:', userId);

    try {
      // Use passed sessionUser or fallback to state user
      const currentUser = sessionUser || user;

      // Get email from multiple sources
      const userEmail = currentUser?.email || currentUser?.user_metadata?.email || email;

      console.log('Email extraction debug:', {
        currentUserEmail: currentUser?.email,
        currentUserMetadataEmail: currentUser?.user_metadata?.email,
        urlEmail: email,
        finalEmail: userEmail,
        currentUserObject: currentUser
      });

      if (!userEmail) {
        console.error('No email found in any source:', {
          currentUser,
          stateUser: user,
          urlEmail: email
        });
        throw new Error('User email not found');
      }

      console.log('Creating checkout session with:', {
        priceId: getPriceId(selectedPlan),
        tier: selectedPlan,
        userId: userId,
        userEmail: userEmail
      });

      const response = await fetch('/api/stripe/create-checkout-session', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          priceId: getPriceId(selectedPlan),
          tier: selectedPlan,
          userId: userId,
          userEmail: userEmail,
          signup: false, // This is for existing authenticated users
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to create checkout session');
      }

      // Redirect to Stripe Checkout
      if (data.url) {
        window.location.href = data.url;
      } else {
        throw new Error('No checkout URL received');
      }

    } catch (error) {
      console.error('Checkout session error:', error);
      setError(error instanceof Error ? error.message : 'Failed to start checkout');
    }
  };

  const getPriceId = (plan: string): string => {
    // Import the client-side price ID function
    const { getPriceIdForTier } = require('@/lib/stripe-client-config');
    try {
      return getPriceIdForTier(plan) || 'price_professional';
    } catch (error) {
      console.error('Error getting price ID for tier:', plan, error);
      return 'price_professional';
    }
  };

  const getPlanPrice = (plan: string): string => {
    switch (plan.toLowerCase()) {
      case 'free':
        return '$0';
      case 'starter':
        return '$19';
      case 'professional':
        return '$49';
      case 'enterprise':
        return '$149';
      default:
        return '$49';
    }
  };

  // Show loading until component is mounted (prevents hydration issues)
  if (!mounted) {
    console.log('⏳ Component not mounted yet, showing loading...');
    return (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center" style={{
        background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
      }}>
        {/* Subtle grid background */}
        <div
          className="absolute inset-0 opacity-[0.03]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }}
        />
        <div className="text-center relative z-10">
          <div className="w-8 h-8 border-2 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-300">Loading checkout...</p>
        </div>
      </div>
    );
  }

  console.log('✅ Component mounted, proceeding with render...');

  if (error) {
    return (
      <div className="fixed inset-0 z-[9999] flex items-center justify-center px-4" style={{
        background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
      }}>
        {/* Subtle grid background */}
        <div
          className="absolute inset-0 opacity-[0.03]"
          style={{
            backgroundImage: `
              linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
              linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
            `,
            backgroundSize: '60px 60px'
          }}
        />
        <div className="max-w-md w-full text-center relative z-10">
          <div className="w-16 h-16 bg-red-500/20 rounded-full flex items-center justify-center mx-auto mb-6 border border-red-500/30">
            <svg className="w-8 h-8 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold text-white mb-4">Checkout Error</h1>
          <p className="text-gray-300 mb-6">{error}</p>
          <Link
            href={`/auth/signup?plan=${selectedPlan}`}
            className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-semibold rounded-lg hover:from-[#e55a2b] hover:to-[#e6821a] transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            Try Again
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center px-4" style={{
      background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
    }}>
      {/* Subtle grid background */}
      <div
        className="absolute inset-0 opacity-[0.03]"
        style={{
          backgroundImage: `
            linear-gradient(rgba(255, 107, 53, 0.1) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255, 107, 53, 0.1) 1px, transparent 1px)
          `,
          backgroundSize: '60px 60px'
        }}
      />

      <div className="max-w-md w-full text-center relative z-10">
        {/* Logo with glow effect */}
        <div className="flex justify-center mb-8">
          <div className="relative inline-block">
            <Image
              src="/RouKey_Logo_GLOW.png"
              alt="RouKey"
              width={64}
              height={64}
              className="w-16 h-16 transition-transform duration-300 hover:scale-110"
              priority
            />
            {/* Enhanced glow effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full opacity-30 blur-xl animate-pulse"></div>
            <div className="absolute inset-0 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] rounded-full opacity-20 blur-2xl animate-pulse" style={{ animationDelay: '1s' }}></div>
          </div>
        </div>

        {/* RouKey text */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">RouKey</h1>
        </div>

        {/* Loading State */}
        <div className="bg-white/5 backdrop-blur-sm rounded-2xl border border-white/10 p-8 shadow-2xl">
          <div className="w-12 h-12 border-4 border-[#ff6b35] border-t-transparent rounded-full animate-spin mx-auto mb-6"></div>

          <h2 className="text-2xl font-bold text-white mb-4">Setting up your subscription...</h2>

          <div className="bg-gradient-to-r from-[#ff6b35]/20 to-[#f7931e]/20 border border-[#ff6b35]/30 rounded-xl p-4 mb-6 backdrop-blur-sm">
            <div className="flex items-center justify-center space-x-2 mb-2">
              <div className="w-2 h-2 bg-[#ff6b35] rounded-full"></div>
              <span className="text-[#ff6b35] font-semibold">
                {selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1)} Plan
              </span>
              <div className="w-2 h-2 bg-[#ff6b35] rounded-full"></div>
            </div>
            <p className="text-2xl font-bold text-white">{getPlanPrice(selectedPlan)}/month</p>
          </div>

          <p className="text-gray-300 mb-4">
            You'll be redirected to Stripe to complete your payment securely.
          </p>

          <p className="text-sm text-gray-400">
            After payment, you'll verify your email and gain access to your dashboard.
          </p>
        </div>

        {/* Security Notice */}
        <div className="mt-6 flex items-center justify-center space-x-2 text-sm text-gray-400">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
          </svg>
          <span>Secured by Stripe</span>
        </div>
      </div>
    </div>
  );
}

export default function CheckoutPage() {
  console.log('🚀 CheckoutPage main function called');
  return <CheckoutPageContent />;
}
