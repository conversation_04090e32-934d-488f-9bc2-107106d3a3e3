'use client';

import { useState, useEffect } from 'react';
import { PlayI<PERSON>, ClockIcon, CheckCircleIcon, XCircleIcon, EyeIcon } from '@heroicons/react/24/outline';

interface SavedWorkflow {
  id: string;
  name: string;
  description?: string;
  is_active: boolean;
  version: number;
  created_at: string;
  updated_at: string;
  node_count: number;
  edge_count: number;
}

interface ExecutionResult {
  execution_id: string;
  status: 'running' | 'completed' | 'failed';
  progress?: {
    nodesCompleted: number;
    totalNodes: number;
    percentage: number;
  };
  result?: any;
  error?: string;
  timestamp: string;
}

export default function WorkflowPlaygroundPage() {
  const [workflows, setWorkflows] = useState<SavedWorkflow[]>([]);
  const [selectedWorkflow, setSelectedWorkflow] = useState<SavedWorkflow | null>(null);
  const [input, setInput] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<ExecutionResult | null>(null);
  const [executionLogs, setExecutionLogs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadWorkflows();
  }, []);

  const loadWorkflows = async () => {
    try {
      const response = await fetch('/api/workflows');
      if (response.ok) {
        const data = await response.json();
        setWorkflows(data.workflows || []);
      }
    } catch (error) {
      console.error('Failed to load workflows:', error);
    } finally {
      setLoading(false);
    }
  };

  const executeWorkflow = async () => {
    if (!selectedWorkflow || !input.trim()) return;

    setIsExecuting(true);
    setExecutionResult(null);
    setExecutionLogs([]);

    try {
      // Get workflow details including API key
      const workflowResponse = await fetch(`/api/workflows?id=${selectedWorkflow.id}`);
      if (!workflowResponse.ok) {
        throw new Error('Failed to get workflow details');
      }

      const workflowData = await workflowResponse.json();
      const apiKey = workflowData.api_key_info?.key_prefix + '_' + workflowData.api_key_info?.encrypted_suffix;

      // Execute via API (simulating external API usage)
      const response = await fetch('/api/workflows/execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': apiKey // Use the workflow's persistent API key
        },
        body: JSON.stringify({
          input: input.trim(),
          options: {
            enableLogs: true,
            enableProgress: true
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Execution failed');
      }

      const result = await response.json();
      setExecutionResult({
        execution_id: result.execution_id,
        status: 'completed',
        result: result.result,
        timestamp: result.executed_at
      });

      // Get execution logs
      await loadExecutionLogs(result.execution_id);

    } catch (error) {
      setExecutionResult({
        execution_id: 'error',
        status: 'failed',
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString()
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const loadExecutionLogs = async (executionId: string) => {
    try {
      // This would be implemented to fetch logs from the monitoring system
      // For now, we'll simulate some logs
      setExecutionLogs([
        {
          id: '1',
          nodeId: 'user-request-1',
          nodeType: 'userRequest',
          level: 'info',
          message: 'Processing user input',
          timestamp: new Date().toISOString()
        },
        {
          id: '2',
          nodeId: 'browsing-1',
          nodeType: 'browsing',
          level: 'info',
          message: 'Starting intelligent browsing',
          timestamp: new Date().toISOString()
        },
        {
          id: '3',
          nodeId: 'browsing-1',
          nodeType: 'browsing',
          level: 'success',
          message: 'Browsing completed successfully',
          timestamp: new Date().toISOString()
        }
      ]);
    } catch (error) {
      console.error('Failed to load execution logs:', error);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-[#040716] flex items-center justify-center">
        <div className="text-white">Loading workflows...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#040716] text-white">
      <div className="container mx-auto px-6 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">Workflow Playground</h1>
          <p className="text-gray-400">Test your workflows with real inputs and see live results</p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Workflow Selection */}
          <div className="lg:col-span-1">
            <div className="bg-gray-900/50 rounded-lg border border-gray-700/50 p-6">
              <h2 className="text-xl font-semibold mb-4">Select Workflow</h2>
              
              {workflows.length === 0 ? (
                <div className="text-center py-8">
                  <p className="text-gray-400 mb-4">No workflows found</p>
                  <a
                    href="/manual-build"
                    className="text-[#ff6b35] hover:underline"
                  >
                    Create your first workflow
                  </a>
                </div>
              ) : (
                <div className="space-y-3">
                  {workflows.map((workflow) => (
                    <div
                      key={workflow.id}
                      onClick={() => setSelectedWorkflow(workflow)}
                      className={`p-4 rounded-lg border cursor-pointer transition-all ${
                        selectedWorkflow?.id === workflow.id
                          ? 'border-[#ff6b35] bg-[#ff6b35]/10'
                          : 'border-gray-700 hover:border-gray-600'
                      }`}
                    >
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="font-medium">{workflow.name}</h3>
                        <div className={`w-2 h-2 rounded-full ${
                          workflow.is_active ? 'bg-green-500' : 'bg-gray-500'
                        }`} />
                      </div>
                      
                      {workflow.description && (
                        <p className="text-sm text-gray-400 mb-2">{workflow.description}</p>
                      )}
                      
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>{workflow.node_count} nodes</span>
                        <span>v{workflow.version}</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Input & Execution */}
          <div className="lg:col-span-2">
            <div className="bg-gray-900/50 rounded-lg border border-gray-700/50 p-6">
              <h2 className="text-xl font-semibold mb-4">Test Execution</h2>
              
              {!selectedWorkflow ? (
                <div className="text-center py-12">
                  <p className="text-gray-400">Select a workflow to start testing</p>
                </div>
              ) : (
                <div className="space-y-6">
                  {/* Input */}
                  <div>
                    <label className="block text-sm font-medium text-gray-300 mb-2">
                      Input for "{selectedWorkflow.name}"
                    </label>
                    <textarea
                      value={input}
                      onChange={(e) => setInput(e.target.value)}
                      placeholder="Enter your request or input for the workflow..."
                      rows={4}
                      className="w-full px-4 py-3 bg-gray-800 border border-gray-600 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-[#ff6b35] focus:border-transparent"
                    />
                  </div>

                  {/* Execute Button */}
                  <button
                    onClick={executeWorkflow}
                    disabled={isExecuting || !input.trim()}
                    className="w-full bg-[#ff6b35] hover:bg-[#e55a2b] disabled:bg-gray-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-200 flex items-center justify-center gap-2"
                  >
                    {isExecuting ? (
                      <>
                        <ClockIcon className="w-5 h-5 animate-spin" />
                        Executing...
                      </>
                    ) : (
                      <>
                        <PlayIcon className="w-5 h-5" />
                        Execute Workflow
                      </>
                    )}
                  </button>

                  {/* Results */}
                  {executionResult && (
                    <div className="space-y-4">
                      <div className={`p-4 rounded-lg border ${
                        executionResult.status === 'completed'
                          ? 'border-green-500 bg-green-900/20'
                          : executionResult.status === 'failed'
                          ? 'border-red-500 bg-red-900/20'
                          : 'border-yellow-500 bg-yellow-900/20'
                      }`}>
                        <div className="flex items-center gap-2 mb-2">
                          {executionResult.status === 'completed' && (
                            <CheckCircleIcon className="w-5 h-5 text-green-500" />
                          )}
                          {executionResult.status === 'failed' && (
                            <XCircleIcon className="w-5 h-5 text-red-500" />
                          )}
                          {executionResult.status === 'running' && (
                            <ClockIcon className="w-5 h-5 text-yellow-500 animate-spin" />
                          )}
                          <span className="font-medium capitalize">{executionResult.status}</span>
                        </div>

                        {executionResult.error && (
                          <p className="text-red-300 text-sm">{executionResult.error}</p>
                        )}

                        {executionResult.result && (
                          <div>
                            <h4 className="font-medium mb-2">Result:</h4>
                            <pre className="text-sm bg-gray-800 p-3 rounded overflow-auto max-h-64">
                              {JSON.stringify(executionResult.result, null, 2)}
                            </pre>
                          </div>
                        )}
                      </div>

                      {/* Execution Logs */}
                      {executionLogs.length > 0 && (
                        <div className="bg-gray-800 rounded-lg p-4">
                          <h4 className="font-medium mb-3 flex items-center gap-2">
                            <EyeIcon className="w-4 h-4" />
                            Execution Logs
                          </h4>
                          <div className="space-y-2 max-h-64 overflow-y-auto">
                            {executionLogs.map((log) => (
                              <div key={log.id} className="text-sm">
                                <span className={`inline-block w-2 h-2 rounded-full mr-2 ${
                                  log.level === 'success' ? 'bg-green-500' :
                                  log.level === 'error' ? 'bg-red-500' :
                                  log.level === 'warning' ? 'bg-yellow-500' :
                                  'bg-blue-500'
                                }`} />
                                <span className="text-gray-400">[{log.nodeType}]</span>
                                <span className="ml-2">{log.message}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
