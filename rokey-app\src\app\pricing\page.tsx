'use client';

import React, { Suspense, useState, useRef } from 'react';
import { motion } from 'framer-motion';
import { CheckIcon, StarIcon, XMarkIcon, BoltIcon, ShieldCheckIcon, SparklesIcon, CpuChipIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';
import LandingNavbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';


const pricingTiers = [
  {
    name: "Free",
    price: 0,
    description: "Perfect for getting started and testing RouKey",
    features: [
      "Unlimited API requests",
      "1 Custom Configuration",
      "3 API Keys per config",
      "All 300+ AI models",
      "Strict fallback routing only",
      "Basic analytics only",
      "No custom roles, basic router only",
      "Limited logs",
      "Community support"
    ],
    notIncluded: [
      "Advanced routing strategies",
      "Custom roles",
      "Prompt engineering",
      "Knowledge base",
      "Semantic caching"
    ],
    cta: "Start free trial",
    popular: false,
    icon: BoltIcon,
    gradient: "from-blue-500 to-purple-600"
  },
  {
    name: "Starter",
    price: 20,
    description: "Perfect for individual developers and small projects",
    features: [
      "Unlimited API requests",
      "15 Custom Configurations",
      "5 API Keys per config",
      "All 300+ AI models",
      "Intelligent routing strategies",
      "Up to 3 custom roles",
      "Intelligent role routing",
      "Prompt engineering (no file upload)",
      "Enhanced logs and analytics",
      "Community support"
    ],
    notIncluded: [
      "Knowledge base",
      "Semantic caching",
      "Priority support"
    ],
    cta: "Get Started",
    popular: false,
    icon: CpuChipIcon,
    gradient: "from-orange-500 to-orange-600"
  },
  {
    name: "Professional",
    price: 50,
    description: "Ideal for growing businesses and development teams",
    features: [
      "Unlimited API requests",
      "Unlimited Custom Configurations",
      "Unlimited API Keys per config",
      "All 300+ AI models",
      "All advanced routing strategies",
      "Unlimited custom roles",
      "Prompt engineering + Knowledge base (5 documents)",
      "Semantic caching",
      "Advanced analytics and logging",
      "Priority email support"
    ],
    notIncluded: [
      "Custom integrations",
      "Phone support",
      "SLA guarantee"
    ],
    cta: "Get Started",
    popular: true,
    icon: StarIcon,
    gradient: "from-purple-500 to-purple-600"
  }
];

const comparisonFeatures = [
  {
    category: "Core features",
    features: [
      { name: "Leading-edge UI", free: "✓", starter: "✓", pro: "✓" },
      { name: "All integrations", free: "✓", starter: "✓", pro: "✓" },
      { name: "Streaming operations", free: "✓", starter: "✓", pro: "✓" },
      { name: "Analytics & Monitoring", free: "Basic", starter: "Enhanced", pro: "Advanced" }
    ]
  },
  {
    category: "Developer tools",
    features: [
      { name: "API Requests per month", free: "Unlimited", starter: "Unlimited", pro: "Unlimited" },
      { name: "Custom Configurations", free: "1", starter: "15", pro: "Unlimited" },
      { name: "API Keys per config", free: "3", starter: "5", pro: "Unlimited" },
      { name: "Supported AI Models", free: "300+", starter: "300+", pro: "300+" },
      { name: "Routing Strategies", free: "Strict fallback only", starter: "Intelligent routing", pro: "All strategies" },
      { name: "Custom Roles", free: "✗", starter: "Up to 3", pro: "Unlimited" },
      { name: "Intelligent Role Routing", free: "✗", starter: "✓", pro: "✓" }
    ]
  },
  {
    category: "Advanced features",
    features: [
      { name: "Prompt Engineering", free: "✗", starter: "✓ (no upload)", pro: "✓ + Knowledge base" },
      { name: "Knowledge Base Documents", free: "✗", starter: "✗", pro: "5 documents" },
      { name: "Semantic Caching", free: "✗", starter: "✗", pro: "✓" },
      { name: "Support Level", free: "Community", starter: "Community", pro: "Priority Email" }
    ]
  }
];

// Helper function to render feature values with proper styling
const renderFeatureValue = (value: string) => {
  if (value === "✓") {
    return <CheckIcon className="h-5 w-5 text-green-400 mx-auto" />;
  }
  if (value === "✗") {
    return <XMarkIcon className="h-5 w-5 text-red-400 mx-auto" />;
  }
  return <span className="text-gray-300">{value}</span>;
};

function PricingPageContent() {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  // Check for subscription required message
  const urlParams = typeof window !== 'undefined' ? new URLSearchParams(window.location.search) : null;
  const message = urlParams?.get('message');
  const plan = urlParams?.get('plan');
  const showSubscriptionRequired = message === 'subscription_required' || message === 'subscription_check_failed';
  const showCompletePayment = message === 'complete_payment';

  return (
    <div className="min-h-screen" style={{
      background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
    }}>
      <LandingNavbar />

      {/* Subscription Required Alert */}
      {showSubscriptionRequired && (
        <div className="bg-[#ff6b35] text-white py-3 px-4 text-center">
          <p className="text-sm font-medium">
            🔒 Active subscription required to access the dashboard. Please choose a plan below to continue.
          </p>
        </div>
      )}

      {/* Complete Payment Alert */}
      {showCompletePayment && (
        <div className="bg-amber-500 text-white py-3 px-4 text-center">
          <p className="text-sm font-medium">
            ⚠️ Please complete your payment to activate your {plan ? plan.charAt(0).toUpperCase() + plan.slice(1) : ''} plan. Your account is currently pending payment.
          </p>
        </div>
      )}

      <main className="pt-20 relative">
        {/* Enhanced Grid Background */}
        <EnhancedGridBackground
          gridSize={50}
          opacity={0.03}
          color="#ff6b35"
          variant="premium"
          animated={true}
          className="fixed inset-0"
        />

        {/* Hero Section */}
        <section className="py-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.h1
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6"
            >
              Universal AI access with
              <br />
              <span className="text-transparent bg-clip-text bg-gradient-to-r from-orange-400 to-orange-600">
                your own keys
              </span>
            </motion.h1>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.05 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto mb-8"
            >
              Choose the plan that fits your needs. All plans include intelligent routing to 300+ AI models with complete cost transparency.
            </motion.p>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
              className="flex items-center justify-center gap-6 mb-12"
            >
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-white rounded-full"></div>
                <span className="text-white text-sm">Monthly</span>
              </div>
              <div className="flex items-center gap-2 opacity-60">
                <div className="w-3 h-3 bg-white/30 rounded-full"></div>
                <span className="text-white/60 text-sm">Annually</span>
              </div>
            </motion.div>
          </div>
        </section>

        {/* Pricing Cards */}
        <section className="pb-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
              {pricingTiers.slice(0, 3).map((tier, index) => {
                const IconComponent = tier.icon;
                return (
                  <motion.div
                    key={tier.name}
                    initial={{ opacity: 0, y: 30 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className={`relative rounded-3xl p-8 backdrop-blur-sm border ${
                      tier.popular
                        ? 'bg-white/10 border-orange-500/50 shadow-2xl shadow-orange-500/20'
                        : 'bg-white/5 border-white/10 hover:bg-white/10'
                    } transition-all duration-300 hover:transform hover:scale-105`}
                  >
                    {tier.popular && (
                      <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                        <div className="bg-gradient-to-r from-orange-500 to-orange-600 text-white px-4 py-1 rounded-full text-sm font-medium flex items-center shadow-lg">
                          <StarIcon className="h-4 w-4 mr-1" />
                          Pro
                        </div>
                      </div>
                    )}

                    <div className="text-center mb-8">
                      <div className={`inline-flex items-center justify-center w-12 h-12 rounded-xl bg-gradient-to-r ${tier.gradient} mb-4`}>
                        <IconComponent className="h-6 w-6 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold text-white mb-2">{tier.name}</h3>
                      <p className="text-gray-300 mb-6 text-sm">{tier.description}</p>
                      <div className="flex items-baseline justify-center">
                        <span className="text-4xl font-bold text-white">${tier.price}</span>
                        <span className="text-gray-400 ml-2">per month, billed monthly</span>
                      </div>
                    </div>

                    <div className="space-y-4 mb-8">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-white mb-1">
                          {tier.name === 'Free' ? 'Unlimited' : tier.name === 'Starter' ? 'Unlimited' : 'Unlimited'}
                        </div>
                        <div className="text-gray-400 text-sm">
                          {tier.name === 'Free' ? 'API requests to 300+ models' : tier.name === 'Starter' ? 'API requests to 300+ models' : 'API requests to 300+ models'}
                        </div>
                        <div className="text-gray-400 text-xs mt-1">
                          {tier.name === 'Free' ? 'with your own API keys' : tier.name === 'Starter' ? 'with intelligent routing' : 'with advanced orchestration'}
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Configurations:</span>
                          <span className="text-white">{tier.name === 'Free' ? '1' : tier.name === 'Starter' ? '15' : tier.name === 'Professional' ? 'Unlimited' : 'Unlimited'}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">API Keys per config:</span>
                          <span className="text-white">{tier.name === 'Free' ? '3' : tier.name === 'Starter' ? '5' : tier.name === 'Professional' ? 'Unlimited' : 'Unlimited'}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-400">Custom Roles:</span>
                          <span className="text-white">{tier.name === 'Free' ? 'None' : tier.name === 'Starter' ? 'Up to 3' : 'Unlimited'}</span>
                        </div>
                      </div>
                    </div>

                    <Link
                      href={tier.name === 'Free' ? '/auth/signup?plan=free' : `/auth/signup?plan=${tier.name.toLowerCase()}`}
                      className={`block w-full text-center py-3 px-6 rounded-xl font-semibold transition-all duration-300 ${
                        tier.popular
                          ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white hover:from-orange-600 hover:to-orange-700 shadow-lg hover:shadow-xl'
                          : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl'
                      }`}
                    >
                      {tier.cta}
                    </Link>
                  </motion.div>
                );
              })}
            </div>


          </div>
        </section>

        {/* Looking for something else section */}
        <section className="py-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold text-white mb-4">Looking for something else?</h2>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              >
                <h3 className="text-lg font-semibold text-white mb-3">BYOK Framework</h3>
                <p className="text-gray-300 text-sm mb-4">
                  Bring Your Own Keys - maintain complete control over your API costs.
                </p>
                <p className="text-gray-400 text-xs mb-4">
                  RouKey never marks up your API costs. You pay providers directly
                  while getting intelligent routing and cost optimization features.
                </p>
                <Link
                  href="/docs"
                  className="inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors"
                >
                  Learn more
                </Link>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.2 }}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              >
                <h3 className="text-lg font-semibold text-white mb-3">Intelligent Routing</h3>
                <p className="text-gray-300 text-sm mb-4">
                  Smart AI model selection based on complexity, cost, and performance requirements.
                </p>
                <p className="text-gray-400 text-xs mb-4">
                  Automatically route simple tasks to cost-effective models and complex tasks
                  to premium models for optimal performance and cost savings.
                </p>
                <Link
                  href="/features"
                  className="inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors"
                >
                  Learn more
                </Link>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl p-6 hover:bg-white/10 transition-all duration-300"
              >
                <h3 className="text-lg font-semibold text-white mb-3">Smart Cost Optimization</h3>
                <p className="text-gray-300 text-sm mb-4">
                  Save up to 70% on AI costs with intelligent routing strategies.
                </p>
                <p className="text-gray-400 text-xs mb-4">
                  Route simple tasks to cheaper models and complex tasks to
                  premium models automatically.
                </p>
                <Link
                  href="/routing-strategies"
                  className="inline-flex items-center text-orange-400 hover:text-orange-300 text-sm font-medium transition-colors"
                >
                  View strategies
                </Link>
              </motion.div>
            </div>
          </div>
        </section>

        {/* Feature Comparison Table */}
        <section className="py-20 relative">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-16"
            >
              <h2 className="text-3xl font-bold text-white mb-4">What's included?</h2>
            </motion.div>

            <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-2xl overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-white/5">
                    <tr>
                      <th className="px-6 py-4 text-left text-sm font-semibold text-white">Features</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-white">Free</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-white">Starter</th>
                      <th className="px-6 py-4 text-center text-sm font-semibold text-white bg-orange-500/10">Pro</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-white/10">
                    {comparisonFeatures.map((category, categoryIndex) => (
                      <React.Fragment key={category.category}>
                        <tr className="bg-white/5">
                          <td colSpan={4} className="px-6 py-3 text-sm font-semibold text-white">
                            {category.category}
                          </td>
                        </tr>
                        {category.features.map((feature, featureIndex) => (
                          <tr key={`${categoryIndex}-${featureIndex}`} className="hover:bg-white/5">
                            <td className="px-6 py-4 text-sm text-gray-300">{feature.name}</td>
                            <td className="px-6 py-4 text-sm text-center">{renderFeatureValue(feature.free)}</td>
                            <td className="px-6 py-4 text-sm text-center">{renderFeatureValue(feature.starter)}</td>
                            <td className="px-6 py-4 text-sm text-center bg-orange-500/5">{renderFeatureValue(feature.pro)}</td>
                          </tr>
                        ))}
                      </React.Fragment>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-20 relative">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-left mb-16"
            >
              <div className="flex items-center gap-2 mb-4">
                <div className="w-6 h-6 rounded bg-orange-500/20 flex items-center justify-center">
                  <span className="text-orange-400 text-xs font-bold">?</span>
                </div>
                <span className="text-orange-400 text-sm font-medium">FAQs</span>
              </div>
              <h2 className="text-3xl font-bold text-white mb-4">Frequently asked questions</h2>
            </motion.div>

            <div className="space-y-4">
              {[
                {
                  question: "How does RouKey's BYOK model work?",
                  answer: "With Bring Your Own Keys (BYOK), you provide your own API keys from providers like OpenAI, Anthropic, Google, etc. RouKey intelligently routes your requests to the optimal model while you pay providers directly. No markup, complete cost transparency, and you maintain full control over your API spending."
                },
                {
                  question: "What makes RouKey different from other AI gateways?",
                  answer: "RouKey is the only AI gateway with intelligent multi-agent orchestration, proprietary AI classification for role-based routing, and advanced cost optimization. Unlike simple proxies, RouKey uses its proprietary orchestration engine for complex workflows and can save up to 70% on AI costs through smart routing strategies."
                },
                {
                  question: "Can I cancel my subscription at any time?",
                  answer: "Yes, you can cancel your RouKey subscription at any time through your account settings. Your access will continue until the end of your current billing period, and you won't be charged for the next cycle. Your configurations and API keys remain accessible during the billing period."
                },
                {
                  question: "How secure are my API keys with RouKey?",
                  answer: "Your API keys are encrypted using industry-standard AES-256 encryption and stored securely. RouKey follows a BYOK model, meaning you maintain control of your keys. We never store or log your actual AI responses, and all communications are encrypted in transit with SOC 2 compliance."
                },
                {
                  question: "What routing strategies does RouKey support?",
                  answer: "RouKey offers multiple intelligent routing strategies: Intelligent Role Routing (AI-powered classification), Complexity-Based Routing (cost optimization), Strict Fallback (ordered failover), Smart Cost Optimization, and A/B Testing. Each strategy is designed for different use cases and optimization goals."
                },
                {
                  question: "Do I need technical knowledge to use RouKey?",
                  answer: "RouKey is designed for developers but offers different complexity levels. The Free plan provides simple fallback routing that requires minimal setup. Advanced features like multi-agent workflows and custom roles are available for teams that need sophisticated AI orchestration."
                },
                {
                  question: "What's included in the multi-agent workflows?",
                  answer: "RouKey's multi-agent workflows use our proprietary orchestration engine for advanced coordination including sequential workflows, parallel execution, supervisor patterns, and hierarchical coordination. Features include memory persistence, real-time streaming, tool calling, and comprehensive error handling for complex AI tasks."
                },
                {
                  question: "How does RouKey handle rate limits and failures?",
                  answer: "RouKey automatically handles rate limits and API failures through intelligent fallback mechanisms. When one provider hits limits or fails, RouKey seamlessly routes to alternative models. This ensures high availability and reduces the impact of individual provider issues on your applications."
                }
              ].map((faq, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-6 hover:bg-white/10 transition-all duration-300"
                >
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold text-white pr-4">{faq.question}</h3>
                    <div className="text-orange-400 text-xl font-light">+</div>
                  </div>
                  <p className="text-gray-300 text-sm mt-4 leading-relaxed">{faq.answer}</p>
                </motion.div>
              ))}
            </div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}

export default function PricingPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center" style={{
        background: `radial-gradient(ellipse at center, #1C051C 0%, #040716 70%)`
      }}>
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-orange-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-300">Loading pricing...</p>
        </div>
      </div>
    }>
      <PricingPageContent />
    </Suspense>
  );
}
