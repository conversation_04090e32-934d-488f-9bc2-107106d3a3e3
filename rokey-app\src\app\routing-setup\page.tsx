'use client';

import { useEffect, useState } from 'react';
import Link from 'next/link';
import {
  CogIcon,
  ChevronRightIcon,
  BoltIcon,
  CircleStackIcon,
  ArrowPathIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';
import { useRoutingSetupPrefetch } from '@/hooks/useRoutingSetupPrefetch';

interface CustomApiConfigSummary {
  id: string;
  name: string;
  created_at: string;
  routing_strategy: string | null;
}

export default function RoutingSetupIndexPage() {
  const [configs, setConfigs] = useState<CustomApiConfigSummary[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  // Prefetch hook for routing setup pages
  const { createHoverPrefetch } = useRoutingSetupPrefetch();

  useEffect(() => {
    async function fetchConfigs() {
      setIsLoading(true);
      setError(null);
      try {
        const response = await fetch('/api/custom-configs');
        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || 'Failed to fetch configurations');
        }
        const data: CustomApiConfigSummary[] = await response.json();
        setConfigs(data);
      } catch (err: any) {
        setError(err.message);
        setConfigs([]);
      } finally {
        setIsLoading(false);
      }
    }
    fetchConfigs();
  }, []);

  return (
    <div className="min-h-screen w-full bg-[#040716] text-white overflow-x-hidden">
      {/* Header Section - Following analytics page design */}
      <div className="border-b border-gray-800/50">
        <div className="w-full px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-semibold text-white mb-2">
                Advanced Routing Setup
              </h1>
              <p className="text-sm text-gray-400 max-w-2xl">
                Configure intelligent routing strategies for your API configurations with enterprise-grade precision
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="w-full px-4 sm:px-6 lg:px-8 py-8">
        {isLoading && (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-orange-500/20 border-t-orange-500 rounded-full animate-spin mx-auto mb-3"></div>
              <p className="text-gray-400 text-sm">Loading configurations...</p>
            </div>
          </div>
        )}

        {error && (
          <div className="bg-red-900/20 border border-red-500/30 rounded-xl p-4 mb-6 backdrop-blur-sm">
            <div className="flex items-center space-x-3">
              <div className="w-6 h-6 bg-red-500/20 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-red-300 text-sm">Error Loading Configurations</h3>
                <p className="text-red-400 text-xs mt-0.5">{error}</p>
              </div>
            </div>
          </div>
        )}

        {!isLoading && !error && configs.length === 0 && (
          <div className="text-center py-12">
            <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-xl p-8 max-w-sm mx-auto">
              <div className="w-12 h-12 bg-orange-500/20 rounded-full flex items-center justify-center mx-auto mb-4 border border-orange-500/30">
                <CogIcon className="w-6 h-6 text-orange-400" />
              </div>
              <h3 className="text-h4 text-white mb-2">
                No Configurations Found
              </h3>
              <p className="text-body-sm text-gray-400 mb-4 leading-relaxed">
                Create your first Custom API Configuration to start setting up intelligent routing strategies
              </p>
              <Link
                href="/my-models"
                className="btn-primary inline-flex items-center text-sm"
              >
                <CogIcon className="w-4 h-4 mr-2" />
                Create Configuration
              </Link>
            </div>
          </div>
        )}

        {!isLoading && !error && configs.length > 0 && (
          <div className="grid gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
            {configs.map((config, index) => {
              // Color rotation system - cycle through vibrant colors
              const colors = [
                { bg: 'bg-gradient-to-br from-pink-500 to-rose-600', icon: 'text-white', text: 'text-white' },
                { bg: 'bg-gradient-to-br from-blue-500 to-blue-600', icon: 'text-white', text: 'text-white' },
                { bg: 'bg-gradient-to-br from-emerald-500 to-green-600', icon: 'text-white', text: 'text-white' },
                { bg: 'bg-gradient-to-br from-amber-500 to-orange-600', icon: 'text-white', text: 'text-white' },
                { bg: 'bg-gradient-to-br from-purple-500 to-violet-600', icon: 'text-white', text: 'text-white' },
                { bg: 'bg-gradient-to-br from-cyan-500 to-teal-600', icon: 'text-white', text: 'text-white' },
                { bg: 'bg-gradient-to-br from-indigo-500 to-blue-700', icon: 'text-white', text: 'text-white' },
                { bg: 'bg-gradient-to-br from-red-500 to-pink-600', icon: 'text-white', text: 'text-white' }
              ];
              const colorScheme = colors[index % colors.length];

              // Icon selection based on routing strategy
              const getStrategyIcon = (strategy: string) => {
                switch (strategy) {
                  case 'intelligent_role':
                    return BoltIcon;
                  case 'complexity_round_robin':
                    return CircleStackIcon;
                  case 'strict_fallback':
                    return ShieldCheckIcon;
                  case 'auto_optimal':
                    return ArrowPathIcon;
                  default:
                    return CogIcon;
                }
              };

              const StrategyIcon = getStrategyIcon(config.routing_strategy || 'none');

              return (
                <Link
                  key={config.id}
                  href={`/routing-setup/${config.id}?from=routing-setup`}
                  className="group block transition-all duration-300 hover:scale-[1.02] hover:shadow-2xl"
                  {...createHoverPrefetch(config.id)}
                >
                  <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-800/50 rounded-2xl shadow-lg overflow-hidden h-80 hover:border-gray-700/50 transition-all duration-300">
                    {/* Colored Header Section */}
                    <div className={`${colorScheme.bg} p-8 h-48 flex flex-col items-center justify-center relative`}>
                      {/* Icon */}
                      <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
                        <StrategyIcon className={`w-10 h-10 ${colorScheme.icon}`} />
                      </div>

                      {/* Strategy Badge */}
                      <div className="bg-white/20 backdrop-blur-sm px-4 py-2 rounded-full">
                        <span className={`text-sm font-bold ${colorScheme.text}`}>
                          {config.routing_strategy === 'none' || !config.routing_strategy ? 'Default' :
                           config.routing_strategy === 'intelligent_role' ? 'Smart Role' :
                           config.routing_strategy === 'complexity_round_robin' ? 'Complexity' :
                           config.routing_strategy === 'strict_fallback' ? 'Fallback' :
                           config.routing_strategy === 'agent_mode' ? 'Agent Mode' :
                           config.routing_strategy}
                        </span>
                      </div>

                      {/* Hover Arrow */}
                      <ChevronRightIcon className={`w-6 h-6 ${colorScheme.text} absolute top-4 right-4 group-hover:translate-x-1 transition-transform duration-300`} />
                    </div>

                    {/* Dark Content Section */}
                    <div className="p-6 h-32 flex flex-col justify-between bg-gray-900/70">
                      <div>
                        <h3 className="text-lg font-bold text-white group-hover:text-gray-200 transition-colors duration-200 line-clamp-2 leading-tight">
                          {config.name}
                        </h3>
                        <p className="text-sm text-gray-400 mt-2">
                          Advanced routing configuration with intelligent strategies
                        </p>
                      </div>

                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-500 font-medium">
                          Created {new Date(config.created_at).toLocaleDateString()}
                        </span>
                        <button className="px-3 py-1 text-xs font-medium text-gray-300 bg-gray-800/50 rounded-md hover:bg-gray-700/50 transition-colors duration-200 border border-gray-700/50">
                          Configure
                        </button>
                      </div>
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>
        )}
      </div>
    </div>
  );
} 