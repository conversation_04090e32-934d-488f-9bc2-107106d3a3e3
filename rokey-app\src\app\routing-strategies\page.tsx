'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import Landing<PERSON>avbar from '@/components/landing/LandingNavbar';
import Footer from '@/components/landing/Footer';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';
import {
  Cog6ToothIcon,
  BoltIcon,
  CircleStackIcon,
  ListBulletIcon,
  CurrencyDollarIcon,
  BeakerIcon,
  ArrowRightIcon,
  CheckIcon,
  SparklesIcon,
  ChartBarIcon,
  ClockIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import InstantLink from '@/components/ui/InstantLink';

const routingStrategies = [
  {
    id: 'intelligent_role',
    name: "Smart Role Routing",
    shortDescription: 'AI-powered intelligent classification',
    description: "RouKey's flagship feature uses advanced AI to analyze requests and automatically route them to the optimal model based on task type and complexity.",
    icon: BoltIcon,
    features: [
      'Intelligent request classification',
      'Automatic role detection',
      'Context-aware routing',
      'Seamless fallback handling'
    ],
    useCase: 'Ideal for applications requiring different AI capabilities - coding, writing, analysis, or general chat.',
    performance: 'Up to 40% better response quality with optimal model selection',
    complexity: 'Advanced',
    color: 'from-[#ff6b35] to-[#f7931e]',
    badge: 'FLAGSHIP'
  },
  {
    id: 'cost_optimization',
    name: 'Cost-Optimized Routing',
    shortDescription: 'Minimize costs while maintaining quality',
    description: "Smart routing that prioritizes cost-effective models while maintaining response quality through intelligent model selection and caching.",
    icon: CurrencyDollarIcon,
    features: [
      'Cost-aware model selection',
      'Semantic caching integration',
      'Quality threshold maintenance',
      'Real-time cost tracking'
    ],
    useCase: 'Perfect for high-volume applications where cost optimization is critical without sacrificing quality.',
    performance: 'Up to 70% cost reduction with maintained quality',
    complexity: 'Intermediate',
    color: 'from-emerald-500 to-emerald-600',
    badge: 'POPULAR'
  },
  {
    id: 'load_balancing',
    name: 'Smart Load Balancing',
    shortDescription: 'Intelligent distribution across providers',
    description: "Advanced load balancing with health monitoring, automatic failover, and intelligent distribution based on provider performance and availability.",
    icon: Cog6ToothIcon,
    features: [
      'Health-aware distribution',
      'Automatic failover',
      'Performance monitoring',
      'Zero-downtime switching'
    ],
    useCase: 'Essential for production applications requiring maximum uptime and reliability.',
    performance: 'Excellent reliability with 99.9% uptime guarantee',
    complexity: 'Beginner',
    color: 'from-blue-500 to-blue-600'
  },
  {
    id: 'agent_mode',
    name: 'Agent Mode',
    shortDescription: 'Collaborative multi-agent intelligence',
    description: 'RouKey assigns up to 5 agents hierarchically. Simple tasks use Agent 1 only. Medium tasks use Agent 1 + Agent 2 for cross-checking. Complex tasks use all agents collaboratively with supervisor synthesis for maximum quality.',
    icon: UserGroupIcon,
    features: [
      'Complexity-based agent scaling',
      'Hierarchical agent assignments',
      'Cross-checking and validation',
      'Supervisor synthesis with citations'
    ],
    useCase: 'Perfect for applications requiring maximum quality through collaborative intelligence and peer review.',
    performance: 'Highest quality results through multi-agent collaboration',
    complexity: 'Expert',
    color: 'from-purple-500 to-purple-600',
    badge: 'NEW'
  },
  {
    id: 'multi_agent',
    name: 'Multi-Agent Orchestration',
    shortDescription: 'Coordinate multiple AI agents',
    description: 'RouKey\'s advanced orchestration system coordinates multiple specialized AI agents to handle complex workflows requiring different expertise areas.',
    icon: UserGroupIcon,
    features: [
      'Sequential workflows',
      'Parallel agent execution',
      'Supervisor coordination',
      'Memory persistence'
    ],
    useCase: 'Perfect for complex tasks requiring multiple AI specializations like research, analysis, and content creation.',
    performance: 'Superior results through specialized agent coordination',
    complexity: 'Expert',
    color: 'from-purple-500 to-purple-600',
    badge: 'UNIQUE'
  },
  {
    id: 'performance_routing',
    name: 'Performance-Optimized Routing',
    shortDescription: 'Speed and latency optimization',
    description: 'Intelligent routing that prioritizes response speed and minimizes latency by selecting the fastest available models and providers.',
    icon: ClockIcon,
    features: [
      'Latency-aware routing',
      'Speed optimization',
      'Real-time performance monitoring',
      'Geographic proximity routing'
    ],
    useCase: 'Ideal for real-time applications where response speed is critical, such as live chat or interactive systems.',
    performance: 'Up to 60% faster response times',
    complexity: 'Intermediate',
    color: 'from-cyan-500 to-cyan-600'
  },
  {
    id: 'analytics_routing',
    name: 'Analytics-Driven Routing',
    shortDescription: 'Data-driven optimization',
    description: 'Advanced routing that uses historical performance data and analytics to continuously optimize model selection and improve results over time.',
    icon: ChartBarIcon,
    features: [
      'Historical performance analysis',
      'Continuous learning optimization',
      'Quality trend monitoring',
      'Predictive model selection'
    ],
    useCase: 'Best for mature applications with substantial usage data that want to leverage analytics for optimal routing decisions.',
    performance: 'Continuously improving performance through data insights',
    complexity: 'Expert',
    color: 'from-indigo-500 to-indigo-600'
  }
];

export default function RoutingStrategiesPage() {
  const [selectedStrategy, setSelectedStrategy] = useState(routingStrategies[0]); // Default to smart role routing

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#040716] to-[#1C051C] relative overflow-hidden">
      {/* Enhanced Grid Background */}
      <EnhancedGridBackground
        gridSize={45}
        opacity={0.06}
        color="#ff6b35"
        variant="premium"
        animated={true}
        className="absolute inset-0"
      />

      {/* Center Gradient */}
      <div className="absolute inset-0 bg-gradient-radial from-[#1C051C]/30 via-transparent to-transparent"></div>

      <LandingNavbar />

      <main className="pt-20 relative z-10">
        {/* Hero Section */}
        <section className="py-20">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="text-center mb-20"
            >
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-white mb-6">
                Intelligent AI
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-[#ff6b35] to-[#f7931e]">
                  {" "}Routing Strategies
                </span>
              </h1>
              <p className="text-xl text-gray-300 mb-12 max-w-4xl mx-auto leading-relaxed">
                Choose from RouKey's advanced routing strategies to optimize your AI applications for cost, performance,
                or specialized workflows. Each strategy is designed for different use cases and complexity levels.
              </p>

              {/* Strategy Overview Pills */}
              <div className="flex flex-wrap justify-center gap-4 mb-16">
                <div className="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20">
                  <span className="text-white font-medium">🎯 Smart Role Detection</span>
                </div>
                <div className="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20">
                  <span className="text-white font-medium">💰 Cost Optimization</span>
                </div>
                <div className="bg-white/10 backdrop-blur-sm px-6 py-3 rounded-full border border-white/20">
                  <span className="text-white font-medium">⚡ Performance Tuning</span>
                </div>
              </div>
            </motion.div>

            {/* Routing Strategies Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
              {routingStrategies.map((strategy, index) => (
                <motion.div
                  key={strategy.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  onClick={() => setSelectedStrategy(strategy)}
                  className={`bg-white/5 backdrop-blur-sm rounded-2xl p-8 border border-white/10 hover:border-white/20 transition-all duration-300 group cursor-pointer ${
                    selectedStrategy.id === strategy.id ? 'border-[#ff6b35]/50 bg-white/10' : ''
                  }`}
                >
                  {/* Badge */}
                  {strategy.badge && (
                    <div className="absolute top-4 right-4 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] px-3 py-1 rounded-full text-xs font-bold text-white">
                      {strategy.badge}
                    </div>
                  )}

                  <div className="flex items-center mb-6">
                    <div className={`bg-gradient-to-r ${strategy.color} p-3 rounded-xl mr-4`}>
                      <strategy.icon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h3 className="text-lg font-bold text-white">{strategy.name}</h3>
                      <p className="text-gray-400 text-sm">{strategy.shortDescription}</p>
                    </div>
                  </div>

                  <p className="text-gray-300 mb-6 leading-relaxed text-sm">{strategy.description}</p>

                  <div className="space-y-2 mb-6">
                    {strategy.features.slice(0, 3).map((feature, idx) => (
                      <div key={idx} className="flex items-center text-gray-400 text-sm">
                        <CheckIcon className="h-4 w-4 text-[#ff6b35] mr-2 flex-shrink-0" />
                        {feature}
                      </div>
                    ))}
                  </div>

                  <div className="pt-4 border-t border-white/10">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-400">Complexity:</span>
                      <span className={`font-medium ${
                        strategy.complexity === 'Beginner' ? 'text-green-400' :
                        strategy.complexity === 'Intermediate' ? 'text-yellow-400' :
                        strategy.complexity === 'Advanced' ? 'text-orange-400' : 'text-red-400'
                      }`}>
                        {strategy.complexity}
                      </span>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </section>

        {/* Selected Strategy Details */}
        {selectedStrategy && (
          <section className="py-20 bg-gradient-to-r from-[#ff6b35]/10 to-[#f7931e]/10 backdrop-blur-sm">
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
              <motion.div
                key={selectedStrategy.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6 }}
                className="bg-white/10 backdrop-blur-sm rounded-2xl p-8 border border-white/20"
              >
                <div className="flex items-center mb-6">
                  <div className={`bg-gradient-to-r ${selectedStrategy.color} p-4 rounded-xl mr-6`}>
                    <selectedStrategy.icon className="h-8 w-8 text-white" />
                  </div>
                  <div>
                    <h2 className="text-2xl font-bold text-white mb-2">{selectedStrategy.name}</h2>
                    <p className="text-gray-300">{selectedStrategy.shortDescription}</p>
                  </div>
                  {selectedStrategy.badge && (
                    <div className="ml-auto bg-gradient-to-r from-[#ff6b35] to-[#f7931e] px-4 py-2 rounded-full text-sm font-bold text-white">
                      {selectedStrategy.badge}
                    </div>
                  )}
                </div>

                <p className="text-gray-300 mb-8 text-lg leading-relaxed">{selectedStrategy.description}</p>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                  <div>
                    <h3 className="text-lg font-semibold text-white mb-4">Key Features</h3>
                    <ul className="space-y-3">
                      {selectedStrategy.features.map((feature, idx) => (
                        <li key={idx} className="flex items-center text-gray-300">
                          <CheckIcon className="h-5 w-5 text-[#ff6b35] mr-3 flex-shrink-0" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <h4 className="font-semibold text-white mb-2">Best Use Case</h4>
                      <p className="text-gray-300 text-sm leading-relaxed">{selectedStrategy.useCase}</p>
                    </div>

                    <div>
                      <h4 className="font-semibold text-white mb-2">Performance</h4>
                      <p className="text-gray-300 text-sm leading-relaxed">{selectedStrategy.performance}</p>
                    </div>

                    <div>
                      <h4 className="font-semibold text-white mb-2">Complexity Level</h4>
                      <span className={`inline-block px-3 py-1 rounded-full text-sm font-medium ${
                        selectedStrategy.complexity === 'Beginner' ? 'bg-green-500/20 text-green-400 border border-green-500/30' :
                        selectedStrategy.complexity === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-400 border border-yellow-500/30' :
                        selectedStrategy.complexity === 'Advanced' ? 'bg-orange-500/20 text-orange-400 border border-orange-500/30' :
                        'bg-red-500/20 text-red-400 border border-red-500/30'
                      }`}>
                        {selectedStrategy.complexity}
                      </span>
                    </div>
                  </div>
                </div>
              </motion.div>
            </div>
          </section>
        )}

        {/* CTA Section */}
        <section className="py-20">
          <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
                Ready to Implement Smart Routing?
              </h2>
              <p className="text-xl text-gray-300 mb-12 max-w-2xl mx-auto">
                Start with any routing strategy and switch anytime. RouKey makes it easy to optimize your AI applications
                for cost, performance, or specialized workflows.
              </p>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row gap-6 justify-center">
                <InstantLink
                  href="/pricing"
                  className="inline-flex items-center px-12 py-5 bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white font-bold rounded-2xl hover:shadow-2xl hover:scale-105 transition-all duration-200 text-xl"
                >
                  <SparklesIcon className="mr-3 h-6 w-6" />
                  Start Building Now
                </InstantLink>
                <InstantLink
                  href="/features"
                  className="inline-flex items-center px-12 py-5 bg-white/10 backdrop-blur-sm text-white font-bold rounded-2xl border-2 border-white/20 hover:border-white/40 hover:bg-white/20 transition-all duration-200 text-xl"
                >
                  <BoltIcon className="mr-3 h-6 w-6" />
                  Explore Features
                </InstantLink>
              </div>
            </motion.div>
          </div>
        </section>
      </main>

      <Footer />
    </div>
  );
}
