'use client';

import { useState } from 'react';

export default function TestFullBrowsingPage() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testComplexBrowsingWorkflow = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('🚀 Starting complex browsing workflow test...');

      // Create a test workflow with all connected nodes
      const testWorkflow = {
        id: `test_workflow_${Date.now()}`,
        nodes: [
          {
            id: 'user-request-1',
            type: 'userRequest',
            position: { x: 100, y: 100 },
            data: {
              label: 'User Request',
              config: {},
              isConfigured: true
            }
          },
          {
            id: 'memory-1',
            type: 'memory',
            position: { x: 100, y: 200 },
            data: {
              label: 'Memory Brain',
              config: {
                memoryName: 'browsing_memory',
                maxSize: 10240,
                encryption: true
              },
              isConfigured: true
            }
          },
          {
            id: 'planner-1',
            type: 'planner',
            position: { x: 300, y: 200 },
            data: {
              label: 'AI Planner',
              config: {
                modelId: 'gemini-pro',
                providerId: 'google',
                maxSubtasks: 5
              },
              isConfigured: true
            }
          },
          {
            id: 'browsing-1',
            type: 'browsing',
            position: { x: 500, y: 200 },
            data: {
              label: 'Intelligent Browsing',
              config: {
                maxSites: 5,
                timeout: 30,
                enableScreenshots: true,
                enableFormFilling: true,
                searchEngines: ['google'],
                maxDepth: 2
              },
              isConfigured: true
            }
          },
          {
            id: 'provider-1',
            type: 'provider',
            position: { x: 700, y: 200 },
            data: {
              label: 'AI Provider',
              config: {
                providerId: 'google',
                modelId: 'gemini-pro',
                apiKey: 'test-key'
              },
              isConfigured: true
            }
          }
        ],
        edges: [
          {
            id: 'e1',
            source: 'user-request-1',
            target: 'browsing-1',
            type: 'smoothstep'
          },
          {
            id: 'e2',
            source: 'memory-1',
            target: 'browsing-1',
            type: 'smoothstep'
          },
          {
            id: 'e3',
            source: 'planner-1',
            target: 'browsing-1',
            sourceHandle: 'output',
            targetHandle: 'planner',
            type: 'smoothstep'
          },
          {
            id: 'e4',
            source: 'browsing-1',
            target: 'provider-1',
            type: 'smoothstep'
          }
        ]
      };

      // Test with a complex browsing task
      const userInput = "Find the latest iPhone 15 Pro prices from multiple retailers and compare features";

      console.log('📊 Executing workflow with nodes:', testWorkflow.nodes.length);
      console.log('🔗 Workflow edges:', testWorkflow.edges.length);
      console.log('💬 User input:', userInput);

      const response = await fetch('/api/manual-build/execute-workflow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          workflowId: testWorkflow.id,
          nodes: testWorkflow.nodes,
          edges: testWorkflow.edges,
          userInput
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.details || 'Workflow execution failed');
      }

      const workflowResult = await response.json();
      console.log('✅ Workflow execution completed:', workflowResult);

      setResult({
        workflow: testWorkflow,
        execution: workflowResult,
        userInput,
        timestamp: new Date().toISOString()
      });

    } catch (err) {
      console.error('❌ Test failed:', err);
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#040716] text-white p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">🌐 Full Browsing System Test</h1>
        
        <div className="mb-8 p-6 bg-gray-800/50 rounded-lg border border-gray-700">
          <h2 className="text-xl font-semibold mb-4">Test Scenario</h2>
          <p className="text-gray-300 mb-4">
            This test creates a complete browsing workflow with all nodes connected:
          </p>
          <ul className="list-disc list-inside text-gray-300 space-y-2">
            <li><strong>User Request Node:</strong> Provides the browsing task</li>
            <li><strong>Memory Node:</strong> Tracks progress and stores context</li>
            <li><strong>Planner Node:</strong> Creates intelligent browsing plans</li>
            <li><strong>Browsing Node:</strong> Executes multi-step browsing with smart completion</li>
            <li><strong>AI Provider Node:</strong> Processes and synthesizes results</li>
          </ul>
        </div>

        <div className="mb-8">
          <button
            onClick={testComplexBrowsingWorkflow}
            disabled={loading}
            className="bg-[#ff6b35] hover:bg-[#e55a2b] disabled:bg-gray-600 text-white px-8 py-4 rounded-lg font-medium transition-colors duration-200 text-lg"
          >
            {loading ? '🔄 Running Complex Browsing Test...' : '🚀 Test Full Browsing System'}
          </button>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-900/50 border border-red-500 rounded-lg">
            <h3 className="text-lg font-semibold text-red-400 mb-2">❌ Test Failed</h3>
            <p className="text-red-300">{error}</p>
          </div>
        )}

        {result && (
          <div className="space-y-6">
            <div className="bg-green-900/30 border border-green-500 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-green-400 mb-4">✅ Test Completed Successfully</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-white mb-2">Workflow Configuration</h4>
                  <div className="text-sm text-gray-300 bg-gray-800 p-3 rounded">
                    <div>Nodes: {result.workflow.nodes.length}</div>
                    <div>Edges: {result.workflow.edges.length}</div>
                    <div>Task: {result.userInput}</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-medium text-white mb-2">Execution Status</h4>
                  <div className="text-sm text-gray-300 bg-gray-800 p-3 rounded">
                    <div>Status: {result.execution.success ? '✅ Success' : '❌ Failed'}</div>
                    <div>Executed At: {new Date(result.execution.executedAt).toLocaleString()}</div>
                    <div>Workflow ID: {result.execution.workflowId}</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="bg-gray-800 p-6 rounded-lg">
              <h3 className="text-lg font-semibold mb-4">📊 Detailed Results</h3>
              <pre className="text-sm overflow-auto max-h-96 bg-gray-900 p-4 rounded">
                {JSON.stringify(result.execution.result, null, 2)}
              </pre>
            </div>

            <div className="bg-blue-900/30 border border-blue-500 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-blue-400 mb-4">🧠 How It Works</h3>
              <div className="text-sm text-gray-300 space-y-2">
                <p><strong>1. User Request:</strong> Provides the browsing task to the system</p>
                <p><strong>2. Memory Connection:</strong> Browsing node connects to memory for persistent tracking</p>
                <p><strong>3. AI Planning:</strong> Planner creates detailed multi-step browsing strategy</p>
                <p><strong>4. Intelligent Browsing:</strong> Executes searches, analyzes results, selects best sites</p>
                <p><strong>5. Smart Completion:</strong> Detects when sufficient information is gathered</p>
                <p><strong>6. AI Processing:</strong> Provider node synthesizes and presents final results</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
