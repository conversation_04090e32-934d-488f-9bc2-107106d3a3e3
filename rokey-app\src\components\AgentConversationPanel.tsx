'use client';

import React, { useState, useEffect, useRef } from 'react';
import { 
  ChevronDownIcon,
  ChevronUpIcon,
  ChatBubbleLeftRightIcon,
  SparklesIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline';

interface ConversationMessage {
  id: string;
  speaker: string;
  message: string;
  timestamp: string;
  action?: string;
  context?: any;
}

interface AgentConversationPanelProps {
  messages: ConversationMessage[];
  isActive: boolean;
  className?: string;
}

export const AgentConversationPanel: React.FC<AgentConversationPanelProps> = ({
  messages,
  isActive,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [animatedMessages, setAnimatedMessages] = useState<ConversationMessage[]>([]);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Animate messages in one by one
  useEffect(() => {
    if (messages.length === 0) {
      setAnimatedMessages([]);
      return;
    }

    // Add messages with a slight delay for animation effect
    const timeouts: NodeJS.Timeout[] = [];
    
    messages.forEach((message, index) => {
      if (index >= animatedMessages.length) {
        const timeout = setTimeout(() => {
          setAnimatedMessages(prev => [...prev, message]);
        }, index * 200); // 200ms delay between messages
        timeouts.push(timeout);
      }
    });

    return () => {
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [messages, animatedMessages.length]);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    if (messagesEndRef.current && isExpanded) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [animatedMessages, isExpanded]);

  // Auto-expand when first message arrives
  useEffect(() => {
    if (messages.length > 0 && !isExpanded) {
      setIsExpanded(true);
    }
  }, [messages.length, isExpanded]);

  const getRoleEmoji = (speaker: string): string => {
    if (speaker === 'supervisor') return '👑';
    
    const emojiMap: Record<string, string> = {
      'brainstorming_ideation': '🧠',
      'coding_backend': '💻',
      'coding_frontend': '🎨',
      'writing': '✍️',
      'research_synthesis': '🔍',
      'summarization_briefing': '📋',
      'translation_localization': '🌐',
      'data_extraction_structuring': '📊',
      'general_chat': '💬'
    };
    return emojiMap[speaker] || '🤖';
  };

  const getRoleColor = (speaker: string): string => {
    if (speaker === 'supervisor') return 'from-purple-500 to-indigo-600';
    
    const colorMap: Record<string, string> = {
      'brainstorming_ideation': 'from-yellow-400 to-orange-500',
      'coding_backend': 'from-blue-500 to-cyan-600',
      'coding_frontend': 'from-pink-500 to-rose-600',
      'writing': 'from-green-500 to-emerald-600',
      'research_synthesis': 'from-indigo-500 to-purple-600',
      'summarization_briefing': 'from-gray-500 to-slate-600',
      'translation_localization': 'from-teal-500 to-cyan-600',
      'data_extraction_structuring': 'from-amber-500 to-yellow-600',
      'general_chat': 'from-gray-400 to-gray-600'
    };
    return colorMap[speaker] || 'from-gray-400 to-gray-600';
  };

  const formatRoleName = (speaker: string): string => {
    if (speaker === 'supervisor') return 'Supervisor';
    
    return speaker
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  const formatTimestamp = (timestamp: string): string => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', second: '2-digit' });
  };

  if (!isActive && messages.length === 0) {
    return null;
  }

  return (
    <div className={`bg-white border border-gray-200 rounded-xl shadow-lg overflow-hidden transition-all duration-300 ${className}`}>
      {/* Header */}
      <div 
        className="bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-gray-200 p-4 cursor-pointer hover:from-indigo-100 hover:to-purple-100 transition-colors duration-200"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <ChatBubbleLeftRightIcon className="w-6 h-6 text-indigo-600" />
              {isActive && (
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse" />
              )}
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 flex items-center space-x-2">
                <span>Agent Conversation</span>
                {isActive && <SparklesIcon className="w-4 h-4 text-indigo-500 animate-pulse" />}
              </h3>
              <p className="text-sm text-gray-600 flex items-center space-x-1">
                <UserGroupIcon className="w-4 h-4" />
                <span>{messages.length} messages</span>
                {isActive && <span className="text-green-600">• Live</span>}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            {messages.length > 0 && (
              <div className="text-xs text-gray-500 bg-white px-2 py-1 rounded-full">
                {messages.length} msg{messages.length !== 1 ? 's' : ''}
              </div>
            )}
            {isExpanded ? (
              <ChevronUpIcon className="w-5 h-5 text-gray-500" />
            ) : (
              <ChevronDownIcon className="w-5 h-5 text-gray-500" />
            )}
          </div>
        </div>
      </div>

      {/* Messages Container */}
      {isExpanded && (
        <div
          ref={containerRef}
          className="max-h-96 overflow-y-auto bg-gray-50 conversation-scroll"
        >
          {animatedMessages.length === 0 ? (
            <div className="p-6 text-center text-gray-500">
              <ChatBubbleLeftRightIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p className="text-sm">Waiting for agent conversation to begin...</p>
            </div>
          ) : (
            <div className="p-4 space-y-3">
              {animatedMessages.map((message, index) => (
                <div 
                  key={message.id}
                  className="animate-fade-in-up"
                  style={{ 
                    animationDelay: `${index * 100}ms`,
                    animationFillMode: 'both'
                  }}
                >
                  <div className={`flex items-start space-x-3 ${
                    message.speaker === 'supervisor' ? 'flex-row' : 'flex-row'
                  }`}>
                    {/* Avatar */}
                    <div className={`flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-br ${getRoleColor(message.speaker)} flex items-center justify-center text-white text-sm font-medium shadow-md`}>
                      {getRoleEmoji(message.speaker)}
                    </div>
                    
                    {/* Message Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-sm font-semibold text-gray-900">
                          {formatRoleName(message.speaker)}
                        </span>
                        <span className="text-xs text-gray-500">
                          {formatTimestamp(message.timestamp)}
                        </span>
                        {message.action && (
                          <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
                            {message.action}
                          </span>
                        )}
                      </div>
                      
                      <div className={`p-3 rounded-lg shadow-sm ${
                        message.speaker === 'supervisor' 
                          ? 'bg-purple-50 border border-purple-200' 
                          : 'bg-white border border-gray-200'
                      }`}>
                        <p className="text-sm text-gray-800 leading-relaxed">
                          {message.message}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// CSS for animations (add to your global CSS)
const styles = `
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fade-in-up 0.3s ease-out;
}
`;

export default AgentConversationPanel;
