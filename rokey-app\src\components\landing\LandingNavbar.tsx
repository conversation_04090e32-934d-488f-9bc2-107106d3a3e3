'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { prefetcher } from '@/utils/cacheStrategy';
import Image from 'next/image';
import { motion } from 'framer-motion';
import { Bars3Icon, XMarkIcon, ChevronDownIcon } from '@heroicons/react/24/outline';
import InstantLink from '@/components/ui/InstantLink';
import { useInstantNavigation } from '@/hooks/useInstantNavigation';

export default function LandingNavbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);
  const [isVisible, setIsVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);

  const toggleDropdown = (dropdown: string) => {
    setDropdownOpen(dropdownOpen === dropdown ? null : dropdown);
  };

  const handleSignOut = async () => {
    try {
      const { createSupabaseBrowserClient } = await import('@/lib/supabase/client');
      const supabase = createSupabaseBrowserClient();
      await supabase.auth.signOut();
      setIsAuthenticated(false);
    } catch (error) {
      console.error('Sign out error:', error);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      setDropdownOpen(null);
    };

    if (dropdownOpen) {
      document.addEventListener('click', handleClickOutside);
      return () => document.removeEventListener('click', handleClickOutside);
    }
  }, [dropdownOpen]);

  // Initialize instant navigation
  useInstantNavigation();

  // Check authentication state specifically for landing page
  useEffect(() => {
    const checkAuth = async () => {
      try {
        const { createSupabaseBrowserClient } = await import('@/lib/supabase/client');
        const supabase = createSupabaseBrowserClient();

        const { data: { user } } = await supabase.auth.getUser();
        console.log('Landing navbar auth check:', !!user);
        setIsAuthenticated(!!user);
      } catch (error) {
        console.error('Landing navbar auth error:', error);
        setIsAuthenticated(false);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();

    // Listen for auth changes
    const setupAuthListener = async () => {
      try {
        const { createSupabaseBrowserClient } = await import('@/lib/supabase/client');
        const supabase = createSupabaseBrowserClient();

        const { data: { subscription } } = supabase.auth.onAuthStateChange(
          (event, session) => {
            console.log('Landing navbar auth change:', event, !!session);
            setIsAuthenticated(!!session?.user);
          }
        );

        return () => subscription.unsubscribe();
      } catch (error) {
        console.error('Landing navbar auth listener error:', error);
      }
    };

    const cleanup = setupAuthListener();
    return () => {
      cleanup?.then(fn => fn?.());
    };
  }, []);

  // Handle scroll-based navbar visibility
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY;

      // Show navbar when at top of page
      if (currentScrollY < 10) {
        setIsVisible(true);
      }
      // Hide when scrolling down, show when scrolling up
      else if (currentScrollY > lastScrollY && currentScrollY > 100) {
        setIsVisible(false);
      } else if (currentScrollY < lastScrollY) {
        setIsVisible(true);
      }

      setLastScrollY(currentScrollY);
    };

    // Throttle scroll events for better performance
    let ticking = false;
    const throttledHandleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          handleScroll();
          ticking = false;
        });
        ticking = true;
      }
    };

    window.addEventListener('scroll', throttledHandleScroll, { passive: true });
    return () => window.removeEventListener('scroll', throttledHandleScroll);
  }, [lastScrollY]);

  return (
    <motion.nav
      className="fixed top-4 left-0 right-0 z-50 px-6"
      initial={{ y: 0, opacity: 1 }}
      animate={{
        y: isVisible ? 0 : -100,
        opacity: isVisible ? 1 : 0
      }}
      transition={{
        duration: 0.3,
        ease: [0.25, 0.46, 0.45, 0.94] // Custom easing for smooth animation
      }}
    >
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        {/* Left - Logo in its own container */}
        <div className="bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-1">
          <Link href="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <Image
                src="/RouKey_Logo_NOGLOW.png"
                alt="RouKey"
                width={44}
                height={44}
                className="h-11 w-11 transition-opacity duration-200"
              />
              <Image
                src="/RouKey_Logo_GLOW.png"
                alt="RouKey Glow"
                width={44}
                height={44}
                className="h-11 w-11 absolute top-0 left-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
              />
            </div>
            <span className="text-lg font-bold text-white">RouKey</span>
          </Link>
        </div>

        {/* Center - Navigation in its own container */}
        <div className="bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-6 py-3">
          <div className="flex items-center space-x-6">
            {/* Features Dropdown */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDropdown('features');
                }}
                className="flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium"
              >
                <span>Features</span>
                <ChevronDownIcon className="w-3 h-3" />
              </button>
              {dropdownOpen === 'features' && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50">
                  <InstantLink href="/features" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Product overview
                  </InstantLink>
                  <InstantLink href="/routing-strategies" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Routing
                  </InstantLink>
                </div>
              )}
            </div>

            {/* Docs Dropdown */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDropdown('docs');
                }}
                className="flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium"
              >
                <span>Docs</span>
                <ChevronDownIcon className="w-3 h-3" />
              </button>
              {dropdownOpen === 'docs' && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50">
                  <InstantLink href="/docs#overview" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Overview
                  </InstantLink>
                  <InstantLink href="/docs#features" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Features
                  </InstantLink>
                  <InstantLink href="/docs#api-reference" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    API Reference
                  </InstantLink>
                  <InstantLink href="/docs#use-cases" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Use Cases
                  </InstantLink>
                  <InstantLink href="/docs#faq" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    FAQ
                  </InstantLink>
                </div>
              )}
            </div>

            {/* About Dropdown */}
            <div className="relative">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  toggleDropdown('about');
                }}
                className="flex items-center space-x-1 text-gray-300 hover:text-white transition-colors text-sm font-medium"
              >
                <span>About</span>
                <ChevronDownIcon className="w-3 h-3" />
              </button>
              {dropdownOpen === 'about' && (
                <div className="absolute top-full left-0 mt-2 w-48 bg-black/95 backdrop-blur-sm border border-gray-700 rounded-lg shadow-xl py-2 z-50">
                  <InstantLink href="/about-developer" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    About Developer
                  </InstantLink>
                  <InstantLink href="/about" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    About RouKey
                  </InstantLink>
                  <InstantLink href="/contact" className="block px-4 py-2 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-colors text-sm">
                    Contact
                  </InstantLink>
                </div>
              )}
            </div>

            {/* Pricing */}
            <InstantLink
              href="/pricing"
              className="text-gray-300 hover:text-white transition-colors text-sm font-medium"
            >
              Pricing
            </InstantLink>
          </div>
        </div>

        {/* Right - Auth buttons in their own container */}
        <div className="bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-1">
          <div className="flex items-center space-x-3">
            {!loading && (
              isAuthenticated ? (
                <InstantLink
                  href="/dashboard"
                  className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold"
                >
                  Dashboard
                </InstantLink>
              ) : (
                <>
                  <InstantLink
                    href="/auth/signin"
                    className="text-gray-300 hover:text-white transition-colors text-sm font-medium"
                  >
                    Sign In
                  </InstantLink>
                  
                  {/* N8N-Style Get Started Button */}
                  <InstantLink
                    href="/pricing"
                    className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-white rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105 group overflow-hidden bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-400 hover:to-red-400"
                  >
                    <span className="relative z-10 font-semibold">Get Started</span>
                    <div className="absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 group-hover:via-white/20 group-hover:to-white/30 transition-all duration-200"></div>
                  </InstantLink>
                </>
              )
            )}
          </div>
        </div>

        {/* Mobile menu button */}
        <div className="lg:hidden bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl px-4 py-2">
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="text-gray-300 hover:text-white transition-colors"
          >
            {isMenuOpen ? (
              <XMarkIcon className="h-6 w-6" />
            ) : (
              <Bars3Icon className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Navigation */}
      {isMenuOpen && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="lg:hidden mt-4 bg-black/95 backdrop-blur-md border border-gray-800 rounded-xl shadow-2xl mx-4 sm:mx-6 py-4"
        >
          <div className="flex flex-col space-y-4 px-6">
            <InstantLink href="/features" className="text-gray-300 hover:text-white transition-colors duration-100">
              Features
            </InstantLink>
            <InstantLink href="/docs" className="text-gray-300 hover:text-white transition-colors duration-100">
              Docs
            </InstantLink>
            <InstantLink href="/about" className="text-gray-300 hover:text-white transition-colors duration-100">
              About
            </InstantLink>
            <InstantLink href="/pricing" className="text-gray-300 hover:text-white transition-colors duration-100">
              Pricing
            </InstantLink>
            {!loading && (
              isAuthenticated ? (
                <InstantLink
                  href="/dashboard"
                  className="bg-gradient-to-r from-[#ff6b35] to-[#f7931e] text-white px-6 py-2 rounded-lg hover:shadow-lg hover:shadow-orange-500/25 transition-all duration-100 font-semibold text-center"
                >
                  Dashboard
                </InstantLink>
              ) : (
                <div className="flex flex-col space-y-3">
                  <InstantLink
                    href="/auth/signin"
                    className="text-gray-300 hover:text-white transition-colors text-center"
                  >
                    Sign In
                  </InstantLink>
                  <InstantLink
                    href="/pricing"
                    className="bg-gradient-to-r from-amber-500 to-orange-500 text-white px-6 py-2 rounded-lg text-center font-medium"
                  >
                    Get Started
                  </InstantLink>
                </div>
              )
            )}
          </div>
        </motion.div>
      )}
    </motion.nav>
  );
}
