'use client';

import { motion } from 'framer-motion';
import { CheckIcon, StarIcon } from '@heroicons/react/24/outline';
import Link from 'next/link';

const pricingTiers = [
  {
    name: "Starter",
    price: 20,
    description: "Perfect for individual developers and small projects",
    features: [
      "Unlimited API requests",
      "15 Custom Configurations",
      "5 API Keys per config",
      "All 300+ AI models",
      "Intelligent routing strategies",
      "Enhanced analytics",
      "Community support"
    ],
    cta: "Get Started",
    popular: false
  },
  {
    name: "Professional",
    price: 50,
    description: "Ideal for growing businesses and development teams",
    features: [
      "Unlimited API requests",
      "Unlimited Custom Configurations",
      "Unlimited API Keys per config",
      "All 300+ AI models",
      "All advanced routing strategies",
      "Advanced analytics and logging",
      "Knowledge base (5 documents)",
      "Semantic caching",
      "Priority email support"
    ],
    cta: "Get Started",
    popular: true
  },
  {
    name: "Enterprise",
    price: 299,
    description: "For large organizations with high-volume needs",
    features: [
      "Unlimited API requests",
      "Unlimited configurations",
      "Unlimited API keys",
      "All 300+ models + priority access",
      "Custom routing rules",
      "Enterprise analytics (1-year history)",
      "Advanced SLA monitoring",
      "Team management (coming soon)",
      "Dedicated support + phone"
    ],
    cta: "Get Started",
    popular: false
  }
];

export default function PricingSection() {
  return (
    <section id="pricing" className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Section Header */}
        <div className="text-center mb-16">
          <motion.h2
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4"
          >
            Simple, Transparent Pricing
          </motion.h2>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.1 }}
            className="text-xl text-gray-600 max-w-3xl mx-auto"
          >
            Choose the perfect plan for your needs. All plans include unlimited access to 300+ AI models with intelligent routing. You only pay for your own API usage.
          </motion.p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
          {pricingTiers.map((tier, index) => (
            <motion.div
              key={tier.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              viewport={{ once: true }}
              transition={{ delay: index * 0.1 }}
              className={`relative bg-white rounded-2xl border-2 p-8 ${
                tier.popular 
                  ? 'border-[#ff6b35] shadow-xl scale-105' 
                  : 'border-gray-200 shadow-sm'
              }`}
            >
              {/* Popular Badge */}
              {tier.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <div className="bg-[#ff6b35] text-white px-4 py-1 rounded-full text-sm font-medium flex items-center">
                    <StarIcon className="h-4 w-4 mr-1" />
                    Most Popular
                  </div>
                </div>
              )}

              {/* Header */}
              <div className="text-center mb-8">
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{tier.name}</h3>
                <p className="text-gray-600 mb-4">{tier.description}</p>
                <div className="flex items-baseline justify-center">
                  <span className="text-4xl font-bold text-gray-900">${tier.price}</span>
                  <span className="text-gray-600 ml-2">/month</span>
                </div>
              </div>

              {/* Features */}
              <ul className="space-y-4 mb-8">
                {tier.features.map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-start">
                    <CheckIcon className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>

              {/* CTA Button */}
              <Link
                href={`/auth/signup?plan=${tier.name.toLowerCase()}`}
                className={`block w-full text-center py-3 px-6 rounded-lg font-semibold transition-all duration-200 ${
                  tier.popular
                    ? 'bg-[#ff6b35] text-white hover:bg-[#e55a2b] shadow-lg hover:shadow-xl'
                    : 'bg-gray-100 text-gray-900 hover:bg-gray-200'
                }`}
              >
                {tier.cta}
              </Link>
            </motion.div>
          ))}
        </div>

        {/* FAQ Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="mt-20 text-center"
        >
          <h3 className="text-2xl font-bold text-gray-900 mb-8">Frequently Asked Questions</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto text-left">
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">What happens if I exceed my request limit?</h4>
              <p className="text-gray-600">We'll notify you when you approach your limit. You can upgrade your plan or purchase additional requests as needed.</p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Can I change plans anytime?</h4>
              <p className="text-gray-600">Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately with prorated billing.</p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">Do you offer custom enterprise plans?</h4>
              <p className="text-gray-600">Absolutely! Contact our sales team for custom pricing based on your specific volume and feature requirements.</p>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-2">How does billing work?</h4>
              <p className="text-gray-600">All plans are billed monthly. You can upgrade or downgrade your plan at any time with prorated billing.</p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
