// True Agent Collaboration - LangGraph Conversation Nodes
// These nodes handle different phases of real agent-to-agent conversation

import { StateGraph, START, END } from '@langchain/langgraph';
import { BaseMessage, HumanMessage, AIMessage } from '@langchain/core/messages';
import { ConversationStateManager, ConversationTurn, CollaborationState } from './ConversationState';
import { executeProviderRequest } from '../providers/executeProviderRequest';
import { decrypt } from '../encryption';

export interface ApiKey {
  id: string;
  provider: string;
  predefined_model_id: string;
  encrypted_api_key: string;
  label: string;
}

export interface ConversationGraphState {
  messages: BaseMessage[];
  conversationManager: ConversationStateManager;
  agentApiKeys: Record<string, ApiKey>;
  customApiConfigId: string;
  currentAgent?: string;
  nextAction?: 'continue_discussion' | 'advance_phase' | 'complete';
  iterationCount: number;
  maxIterations: number;
}

export class ConversationNodes {
  
  /**
   * Brainstorming Phase Node - Agents share initial ideas and explore the problem space
   */
  static createBrainstormingNode() {
    return async (state: ConversationGraphState): Promise<Partial<ConversationGraphState>> => {
      console.log(`[Brainstorming] Starting brainstorming phase`);
      
      const manager = state.conversationManager;
      const agentKeys = Object.keys(state.agentApiKeys);
      
      // Each agent contributes initial ideas
      for (const agentId of agentKeys) {
        const agentKey = state.agentApiKeys[agentId];
        const contextSummary = manager.generateContextSummary();
        
        const brainstormPrompt = `${contextSummary}

[BRAINSTORMING PHASE - INITIAL IDEAS]
You are ${manager.getState().agentRoles[agentId]} participating in a collaborative brainstorming session.

Original Request: "${manager.getState().userPrompt}"

Your task in this brainstorming phase:
1. **ANALYZE** the request and share your initial understanding
2. **PROPOSE** creative approaches and ideas
3. **IDENTIFY** key challenges and opportunities
4. **ASK QUESTIONS** that will help the team understand the problem better
5. **BUILD** on any ideas already shared by other agents

${manager.getRecentTurns(3).length > 0 ? 'Respond to and build on the ideas already shared by your teammates.' : 'You are starting the brainstorming - share your initial thoughts and ideas.'}

Focus on generating diverse, creative ideas rather than detailed solutions. Be collaborative and engaging!`;

        try {
          const result = await executeProviderRequest(
            agentKey.provider,
            agentKey.predefined_model_id,
            await decrypt(agentKey.encrypted_api_key),
            {
              custom_api_config_id: state.customApiConfigId,
              messages: [{ role: 'user', content: brainstormPrompt }],
              temperature: 0.8, // High creativity for brainstorming
              max_tokens: 2000,
              stream: false,
              role: 'orchestration'
            }
          );

          if (result.success && result.responseData?.choices?.[0]?.message?.content) {
            const response = result.responseData.choices[0].message.content;
            
            // Add to conversation
            manager.addTurn({
              agent: agentId,
              agentLabel: agentKey.label,
              message: response,
              messageType: 'proposal',
              respondingTo: manager.getRecentTurns(1)[0]?.id // Respond to most recent if exists
            });
            
            console.log(`[Brainstorming] ✅ ${agentKey.label} contributed ideas (${response.length} chars)`);
          } else {
            console.error(`[Brainstorming] ❌ ${agentKey.label} failed to contribute`);
          }
        } catch (error) {
          console.error(`[Brainstorming] Error with ${agentKey.label}:`, error);
        }
      }
      
      // Update phase progress
      manager.setPhaseProgress('brainstorming', 1.0);
      manager.updateQualityMetrics();
      
      return {
        nextAction: 'advance_phase',
        iterationCount: state.iterationCount + 1
      };
    };
  }

  /**
   * Debate Phase Node - Agents challenge ideas, identify conflicts, and refine thinking
   */
  static createDebateNode() {
    return async (state: ConversationGraphState): Promise<Partial<ConversationGraphState>> => {
      console.log(`[Debate] Starting debate phase`);
      
      const manager = state.conversationManager;
      const agentKeys = Object.keys(state.agentApiKeys);
      
      // Advance to debate phase
      manager.advancePhase();
      
      // Each agent critiques and challenges ideas
      for (const agentId of agentKeys) {
        const agentKey = state.agentApiKeys[agentId];
        const contextSummary = manager.generateContextSummary();
        const recentTurns = manager.getRecentTurns(5);
        
        const debatePrompt = `${contextSummary}

[DEBATE PHASE - CRITICAL ANALYSIS]
You are ${manager.getState().agentRoles[agentId]} in a critical debate phase.

Original Request: "${manager.getState().userPrompt}"

Recent Team Discussion:
${recentTurns.map(turn => `${turn.agentLabel}: ${turn.message}`).join('\n\n')}

Your task in this debate phase:
1. **CRITIQUE** ideas shared by other agents - identify flaws, gaps, or concerns
2. **CHALLENGE** assumptions and approaches that might not work
3. **DEFEND** good ideas and explain why they're valuable
4. **IDENTIFY** conflicts between different approaches
5. **PROPOSE** alternative solutions to problems you identify
6. **ASK HARD QUESTIONS** that need to be resolved

Be constructively critical - challenge ideas to make them better, not to tear them down. Focus on finding the best path forward through rigorous analysis.`;

        try {
          const result = await executeProviderRequest(
            agentKey.provider,
            agentKey.predefined_model_id,
            await decrypt(agentKey.encrypted_api_key),
            {
              custom_api_config_id: state.customApiConfigId,
              messages: [{ role: 'user', content: debatePrompt }],
              temperature: 0.6, // Balanced for critical thinking
              max_tokens: 2500,
              stream: false,
              role: 'orchestration'
            }
          );

          if (result.success && result.responseData?.choices?.[0]?.message?.content) {
            const response = result.responseData.choices[0].message.content;
            
            // Analyze response for conflicts and issues
            const hasDisagreement = response.toLowerCase().includes('disagree') || 
                                  response.toLowerCase().includes('concern') ||
                                  response.toLowerCase().includes('problem');
            
            if (hasDisagreement) {
              // Extract the main concern/issue
              const issueMatch = response.match(/(?:concern|problem|issue|disagree)[^.]*\.?/i);
              if (issueMatch) {
                manager.addUnresolvedIssue(
                  issueMatch[0],
                  [agentId],
                  'medium'
                );
              }
            }
            
            manager.addTurn({
              agent: agentId,
              agentLabel: agentKey.label,
              message: response,
              messageType: hasDisagreement ? 'critique' : 'improvement',
              respondingTo: recentTurns[recentTurns.length - 1]?.id
            });
            
            console.log(`[Debate] ✅ ${agentKey.label} provided critical analysis (${response.length} chars)`);
          }
        } catch (error) {
          console.error(`[Debate] Error with ${agentKey.label}:`, error);
        }
      }
      
      manager.setPhaseProgress('debate', 1.0);
      manager.updateQualityMetrics();
      
      // Check if we need another round of debate or can move to synthesis
      const unresolvedIssues = manager.getUnresolvedIssues();
      if (unresolvedIssues.length > 0 && state.iterationCount < state.maxIterations) {
        return {
          nextAction: 'continue_discussion',
          iterationCount: state.iterationCount + 1
        };
      }
      
      return {
        nextAction: 'advance_phase',
        iterationCount: state.iterationCount + 1
      };
    };
  }

  /**
   * Synthesis Phase Node - Agents collaborate to build the final solution
   */
  static createSynthesisNode() {
    return async (state: ConversationGraphState): Promise<Partial<ConversationGraphState>> => {
      console.log(`[Synthesis] Starting synthesis phase`);
      
      const manager = state.conversationManager;
      const agentKeys = Object.keys(state.agentApiKeys);
      
      // Advance to synthesis phase
      manager.advancePhase();
      
      // First, try to resolve any remaining issues
      const unresolvedIssues = manager.getUnresolvedIssues();
      if (unresolvedIssues.length > 0) {
        console.log(`[Synthesis] Resolving ${unresolvedIssues.length} unresolved issues first`);
        
        for (const issue of unresolvedIssues.slice(0, 3)) { // Handle top 3 issues
          for (const agentId of agentKeys) {
            const agentKey = state.agentApiKeys[agentId];
            
            const resolutionPrompt = `[ISSUE RESOLUTION]
You are ${manager.getState().agentRoles[agentId]} helping resolve a team disagreement.

Original Request: "${manager.getState().userPrompt}"

Unresolved Issue: "${issue.description}"

Context: ${manager.generateContextSummary()}

Your task:
1. **PROPOSE** a specific solution to resolve this issue
2. **EXPLAIN** why your solution addresses the core concern
3. **CONSIDER** how this fits with the overall approach
4. **BUILD CONSENSUS** by finding common ground

Focus on finding a solution that the whole team can agree on.`;

            try {
              const result = await executeProviderRequest(
                agentKey.provider,
                agentKey.predefined_model_id,
                await decrypt(agentKey.encrypted_api_key),
                {
                  custom_api_config_id: state.customApiConfigId,
                  messages: [{ role: 'user', content: resolutionPrompt }],
                  temperature: 0.4, // Lower for consensus building
                  max_tokens: 1500,
                  stream: false,
                  role: 'orchestration'
                }
              );

              if (result.success && result.responseData?.choices?.[0]?.message?.content) {
                const response = result.responseData.choices[0].message.content;
                
                manager.addTurn({
                  agent: agentId,
                  agentLabel: agentKey.label,
                  message: response,
                  messageType: 'agreement',
                  respondingTo: undefined
                });
                
                // Check if this resolves the issue
                if (response.toLowerCase().includes('agree') || response.toLowerCase().includes('solution')) {
                  manager.resolveIssue(issue.id, response);
                }
              }
            } catch (error) {
              console.error(`[Synthesis] Error resolving issue with ${agentKey.label}:`, error);
            }
          }
        }
      }
      
      // Now build the final collaborative solution
      console.log(`[Synthesis] Building final collaborative solution`);
      
      const leadAgent = agentKeys[0]; // Use first agent as synthesis coordinator
      const leadAgentKey = state.agentApiKeys[leadAgent];
      
      const synthesisPrompt = `[FINAL SOLUTION SYNTHESIS]
You are ${manager.getState().agentRoles[leadAgent]} coordinating the final solution synthesis.

Original Request: "${manager.getState().userPrompt}"

Complete Team Discussion:
${manager.getState().conversationHistory.map(turn => 
  `${turn.agentLabel} (${turn.messageType}): ${turn.message}`
).join('\n\n')}

Team Consensus Points:
${manager.getConsensusItems().map(item => `- ${item.topic}: ${item.agreedPoint}`).join('\n')}

Your task:
1. **SYNTHESIZE** all the team's ideas into one comprehensive solution
2. **INCORPORATE** the best elements from each agent's contributions
3. **ADDRESS** the original request completely and thoroughly
4. **ENSURE** the solution reflects the team's consensus and collaboration
5. **PROVIDE** a high-quality, detailed response that showcases the collective intelligence

Create the final solution that represents the best collaborative thinking of the entire team.`;

      try {
        const result = await executeProviderRequest(
          leadAgentKey.provider,
          leadAgentKey.predefined_model_id,
          await decrypt(leadAgentKey.encrypted_api_key),
          {
            custom_api_config_id: state.customApiConfigId,
            messages: [{ role: 'user', content: synthesisPrompt }],
            temperature: 0.3, // Low for final synthesis
            max_tokens: 4000,
            stream: false,
            role: 'orchestration'
          }
        );

        if (result.success && result.responseData?.choices?.[0]?.message?.content) {
          const finalSolution = result.responseData.choices[0].message.content;
          
          manager.setFinalSolution(finalSolution);
          manager.addTurn({
            agent: leadAgent,
            agentLabel: leadAgentKey.label,
            message: finalSolution,
            messageType: 'synthesis'
          });
          
          console.log(`[Synthesis] ✅ Final solution synthesized (${finalSolution.length} chars)`);
        }
      } catch (error) {
        console.error(`[Synthesis] Error creating final solution:`, error);
      }
      
      manager.setPhaseProgress('synthesis', 1.0);
      manager.updateQualityMetrics();
      
      return {
        nextAction: 'advance_phase',
        iterationCount: state.iterationCount + 1
      };
    };
  }

  /**
   * Validation Phase Node - Team validates the final solution
   */
  static createValidationNode() {
    return async (state: ConversationGraphState): Promise<Partial<ConversationGraphState>> => {
      console.log(`[Validation] Starting validation phase`);
      
      const manager = state.conversationManager;
      const agentKeys = Object.keys(state.agentApiKeys);
      
      // Advance to validation phase
      manager.advancePhase();
      
      const finalSolution = manager.getState().finalSolution;
      if (!finalSolution) {
        console.error(`[Validation] No final solution to validate`);
        return { nextAction: 'complete' };
      }
      
      // Each agent validates the final solution
      let validationScore = 0;
      let validationCount = 0;
      
      for (const agentId of agentKeys) {
        const agentKey = state.agentApiKeys[agentId];
        
        const validationPrompt = `[SOLUTION VALIDATION]
You are ${manager.getState().agentRoles[agentId]} validating the team's final solution.

Original Request: "${manager.getState().userPrompt}"

Final Team Solution:
${finalSolution}

Your task:
1. **EVALUATE** how well the solution addresses the original request
2. **CHECK** if it incorporates the team's best ideas and consensus
3. **ASSESS** the quality, completeness, and effectiveness
4. **IDENTIFY** any remaining gaps or improvements needed
5. **RATE** the solution on a scale of 1-10

Provide your validation in this format:
## Validation Assessment
[Your detailed assessment of the solution]

## Quality Rating: X/10
[Explanation of your rating]

## Final Approval: YES/NO
[Whether you approve this as the final solution]`;

        try {
          const result = await executeProviderRequest(
            agentKey.provider,
            agentKey.predefined_model_id,
            await decrypt(agentKey.encrypted_api_key),
            {
              custom_api_config_id: state.customApiConfigId,
              messages: [{ role: 'user', content: validationPrompt }],
              temperature: 0.2, // Very low for objective validation
              max_tokens: 2000,
              stream: false,
              role: 'orchestration'
            }
          );

          if (result.success && result.responseData?.choices?.[0]?.message?.content) {
            const response = result.responseData.choices[0].message.content;
            
            // Extract rating
            const ratingMatch = response.match(/(?:Quality Rating|Rating):\s*(\d+)\/10/i);
            if (ratingMatch) {
              validationScore += parseInt(ratingMatch[1]);
              validationCount++;
            }
            
            manager.addTurn({
              agent: agentId,
              agentLabel: agentKey.label,
              message: response,
              messageType: 'agreement'
            });
            
            console.log(`[Validation] ✅ ${agentKey.label} completed validation`);
          }
        } catch (error) {
          console.error(`[Validation] Error with ${agentKey.label}:`, error);
        }
      }
      
      // Calculate final quality score
      const avgValidationScore = validationCount > 0 ? validationScore / validationCount : 0;
      manager.getState().qualityMetrics.noveltyScore = avgValidationScore / 10;
      
      manager.setPhaseProgress('validation', 1.0);
      manager.updateQualityMetrics();
      
      console.log(`[Validation] ✅ Validation complete. Average score: ${avgValidationScore.toFixed(1)}/10`);
      
      return {
        nextAction: 'complete',
        iterationCount: state.iterationCount + 1
      };
    };
  }
}
