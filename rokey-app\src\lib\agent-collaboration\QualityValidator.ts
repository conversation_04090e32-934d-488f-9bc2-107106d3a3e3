// True Agent Collaboration - Quality Validation System
// Ensures the collaboration produces genuinely better results through quality metrics

import { ConversationStateManager, ConversationTurn, QualityMetrics } from './ConversationState';
import { executeProviderRequest } from '../providers/executeProviderRequest';
import { decrypt } from '../encryption';

export interface QualityGate {
  name: string;
  threshold: number;
  weight: number;
  description: string;
}

export interface ValidationResult {
  passed: boolean;
  score: number;
  details: string;
  recommendations: string[];
}

export interface CollaborationQualityReport {
  overallScore: number;
  gateResults: Record<string, ValidationResult>;
  recommendations: string[];
  shouldContinue: boolean;
  qualityTrend: 'improving' | 'stable' | 'declining';
}

export class QualityValidator {
  private qualityGates: QualityGate[] = [
    {
      name: 'participation_balance',
      threshold: 0.6,
      weight: 0.2,
      description: 'All agents should participate meaningfully in the discussion'
    },
    {
      name: 'iterative_improvement',
      threshold: 0.4,
      weight: 0.3,
      description: 'Ideas should build on each other and improve over time'
    },
    {
      name: 'consensus_strength',
      threshold: 0.7,
      weight: 0.25,
      description: 'Team should reach strong consensus on key points'
    },
    {
      name: 'solution_completeness',
      threshold: 0.8,
      weight: 0.25,
      description: 'Final solution should comprehensively address the original request'
    }
  ];

  /**
   * Validate the quality of the ongoing collaboration
   */
  async validateCollaborationQuality(
    conversationManager: ConversationStateManager,
    agentApiKey?: { provider: string; predefined_model_id: string; encrypted_api_key: string },
    customApiConfigId?: string
  ): Promise<CollaborationQualityReport> {
    console.log(`[Quality Validator] 🔍 Validating collaboration quality...`);

    const state = conversationManager.getState();
    const gateResults: Record<string, ValidationResult> = {};
    let overallScore = 0;

    // Validate each quality gate
    for (const gate of this.qualityGates) {
      const result = await this.validateQualityGate(gate, conversationManager, agentApiKey, customApiConfigId);
      gateResults[gate.name] = result;
      overallScore += result.score * gate.weight;
      
      console.log(`[Quality Validator] ${gate.name}: ${result.score.toFixed(2)} (${result.passed ? 'PASS' : 'FAIL'})`);
    }

    // Determine quality trend
    const qualityTrend = this.calculateQualityTrend(conversationManager);

    // Generate recommendations
    const recommendations = this.generateQualityRecommendations(gateResults, state);

    // Determine if collaboration should continue
    const shouldContinue = this.shouldContinueCollaboration(overallScore, gateResults, state);

    console.log(`[Quality Validator] ✅ Overall quality score: ${overallScore.toFixed(2)}/1.0`);

    return {
      overallScore,
      gateResults,
      recommendations,
      shouldContinue,
      qualityTrend
    };
  }

  /**
   * Validate a specific quality gate
   */
  private async validateQualityGate(
    gate: QualityGate,
    conversationManager: ConversationStateManager,
    agentApiKey?: { provider: string; predefined_model_id: string; encrypted_api_key: string },
    customApiConfigId?: string
  ): Promise<ValidationResult> {
    const state = conversationManager.getState();

    switch (gate.name) {
      case 'participation_balance':
        return this.validateParticipationBalance(state, gate);
      
      case 'iterative_improvement':
        return this.validateIterativeImprovement(state, gate);
      
      case 'consensus_strength':
        return this.validateConsensusStrength(state, gate);
      
      case 'solution_completeness':
        return await this.validateSolutionCompleteness(state, gate, agentApiKey, customApiConfigId);
      
      default:
        return {
          passed: false,
          score: 0,
          details: `Unknown quality gate: ${gate.name}`,
          recommendations: []
        };
    }
  }

  /**
   * Validate participation balance across agents
   */
  private validateParticipationBalance(state: any, gate: QualityGate): ValidationResult {
    const agentParticipation = state.activeAgents.reduce((acc: Record<string, number>, agent: string) => {
      acc[agent] = state.conversationHistory.filter((turn: ConversationTurn) => turn.agent === agent).length;
      return acc;
    }, {});

    const participationValues = Object.values(agentParticipation) as number[];
    const avgParticipation = participationValues.reduce((a, b) => a + b, 0) / participationValues.length;
    const maxDeviation = Math.max(...participationValues.map(val => Math.abs(val - avgParticipation)));
    
    // Score based on how evenly distributed participation is
    const score = Math.max(0, 1 - (maxDeviation / (avgParticipation || 1)));
    const passed = score >= gate.threshold;

    const details = `Participation distribution: ${Object.entries(agentParticipation)
      .map(([agent, count]) => `${agent}: ${count} turns`)
      .join(', ')}. Average: ${avgParticipation.toFixed(1)}, Max deviation: ${maxDeviation.toFixed(1)}`;

    const recommendations = [];
    if (!passed) {
      const quietAgents = Object.entries(agentParticipation)
        .filter(([_, count]) => (count as number) < avgParticipation * 0.7)
        .map(([agent, _]) => agent);
      
      if (quietAgents.length > 0) {
        recommendations.push(`Encourage more participation from: ${quietAgents.join(', ')}`);
      }
    }

    return { passed, score, details, recommendations };
  }

  /**
   * Validate iterative improvement in the conversation
   */
  private validateIterativeImprovement(state: any, gate: QualityGate): ValidationResult {
    const history = state.conversationHistory as ConversationTurn[];
    
    // Count turns that respond to previous turns
    const responseTurns = history.filter(turn => turn.respondingTo).length;
    const improvementTurns = history.filter(turn => 
      turn.messageType === 'improvement' || turn.messageType === 'synthesis'
    ).length;
    
    // Score based on how much agents build on each other's work
    const responseRatio = history.length > 0 ? responseTurns / history.length : 0;
    const improvementRatio = history.length > 0 ? improvementTurns / history.length : 0;
    
    const score = (responseRatio * 0.6) + (improvementRatio * 0.4);
    const passed = score >= gate.threshold;

    const details = `${responseTurns}/${history.length} turns respond to others (${(responseRatio * 100).toFixed(1)}%). ${improvementTurns} improvement turns (${(improvementRatio * 100).toFixed(1)}%).`;

    const recommendations = [];
    if (!passed) {
      if (responseRatio < 0.3) {
        recommendations.push('Encourage agents to respond to and build on each other\'s ideas');
      }
      if (improvementRatio < 0.2) {
        recommendations.push('Focus more on iterative improvement and synthesis of ideas');
      }
    }

    return { passed, score, details, recommendations };
  }

  /**
   * Validate consensus strength
   */
  private validateConsensusStrength(state: any, gate: QualityGate): ValidationResult {
    const consensusItems = state.consensusItems || [];
    const unresolvedIssues = state.unresolvedIssues || [];
    
    const totalIssues = consensusItems.length + unresolvedIssues.length;
    const consensusRatio = totalIssues > 0 ? consensusItems.length / totalIssues : 0;
    
    // Also consider the confidence of consensus items
    const avgConfidence = consensusItems.length > 0 
      ? consensusItems.reduce((sum: number, item: any) => sum + item.confidence, 0) / consensusItems.length
      : 0;
    
    const score = (consensusRatio * 0.7) + (avgConfidence * 0.3);
    const passed = score >= gate.threshold;

    const details = `${consensusItems.length} consensus items, ${unresolvedIssues.length} unresolved issues. Consensus ratio: ${(consensusRatio * 100).toFixed(1)}%, Average confidence: ${(avgConfidence * 100).toFixed(1)}%`;

    const recommendations = [];
    if (!passed) {
      if (unresolvedIssues.length > 2) {
        recommendations.push(`Resolve ${unresolvedIssues.length} remaining unresolved issues`);
      }
      if (avgConfidence < 0.7) {
        recommendations.push('Strengthen consensus by addressing remaining doubts and concerns');
      }
    }

    return { passed, score, details, recommendations };
  }

  /**
   * Validate solution completeness using AI analysis
   */
  private async validateSolutionCompleteness(
    state: any,
    gate: QualityGate,
    agentApiKey?: { provider: string; predefined_model_id: string; encrypted_api_key: string },
    customApiConfigId?: string
  ): ValidationResult {
    const finalSolution = state.finalSolution;
    const userPrompt = state.userPrompt;

    if (!finalSolution) {
      return {
        passed: false,
        score: 0,
        details: 'No final solution available for validation',
        recommendations: ['Complete the synthesis phase to generate a final solution']
      };
    }

    if (!agentApiKey || !customApiConfigId) {
      // Fallback to basic validation
      const wordCount = finalSolution.split(' ').length;
      const score = Math.min(1, wordCount / 200); // Assume 200+ words is complete
      
      return {
        passed: score >= gate.threshold,
        score,
        details: `Basic validation: ${wordCount} words in solution`,
        recommendations: score < gate.threshold ? ['Expand the solution with more detail'] : []
      };
    }

    try {
      const validationPrompt = `[SOLUTION COMPLETENESS VALIDATION]
Original Request: "${userPrompt}"

Final Solution: "${finalSolution}"

Evaluate how completely this solution addresses the original request:

1. **Coverage**: Does it address all aspects of the request?
2. **Detail**: Is it sufficiently detailed and actionable?
3. **Quality**: Is it well-structured and coherent?
4. **Completeness**: Would the user be satisfied with this response?

Respond in JSON format:
{
  "completeness_score": 0.85,
  "coverage_analysis": "Addresses X, Y, Z but missing A",
  "quality_assessment": "Well-structured with clear examples",
  "missing_elements": ["element1", "element2"],
  "overall_rating": "good"
}`;

      const result = await executeProviderRequest(
        agentApiKey.provider,
        agentApiKey.predefined_model_id,
        await decrypt(agentApiKey.encrypted_api_key),
        {
          custom_api_config_id: customApiConfigId,
          messages: [{ role: 'user', content: validationPrompt }],
          temperature: 0.2,
          max_tokens: 1000,
          stream: false,
          role: 'orchestration'
        }
      );

      if (result.success && result.responseData?.choices?.[0]?.message?.content) {
        const response = result.responseData.choices[0].message.content;
        
        try {
          const analysis = JSON.parse(response);
          const score = analysis.completeness_score || 0;
          const passed = score >= gate.threshold;

          const details = `AI Analysis: ${analysis.coverage_analysis || 'No coverage analysis'}. Quality: ${analysis.quality_assessment || 'No quality assessment'}`;
          
          const recommendations = [];
          if (analysis.missing_elements && analysis.missing_elements.length > 0) {
            recommendations.push(`Address missing elements: ${analysis.missing_elements.join(', ')}`);
          }
          if (score < gate.threshold) {
            recommendations.push('Enhance solution completeness and detail');
          }

          return { passed, score, details, recommendations };
        } catch (parseError) {
          console.warn(`[Quality Validator] Failed to parse AI validation response:`, parseError);
        }
      }
    } catch (error) {
      console.warn(`[Quality Validator] AI validation failed:`, error);
    }

    // Fallback validation
    const wordCount = finalSolution.split(' ').length;
    const score = Math.min(1, wordCount / 300);
    
    return {
      passed: score >= gate.threshold,
      score,
      details: `Fallback validation: ${wordCount} words`,
      recommendations: score < gate.threshold ? ['Expand solution with more comprehensive detail'] : []
    };
  }

  /**
   * Calculate quality trend over time
   */
  private calculateQualityTrend(conversationManager: ConversationStateManager): 'improving' | 'stable' | 'declining' {
    const history = conversationManager.getState().conversationHistory;
    
    if (history.length < 4) return 'stable';

    // Compare quality of recent vs earlier turns
    const midPoint = Math.floor(history.length / 2);
    const earlierTurns = history.slice(0, midPoint);
    const recentTurns = history.slice(midPoint);

    const earlierQuality = this.calculateTurnQuality(earlierTurns);
    const recentQuality = this.calculateTurnQuality(recentTurns);

    const improvement = recentQuality - earlierQuality;
    
    if (improvement > 0.1) return 'improving';
    if (improvement < -0.1) return 'declining';
    return 'stable';
  }

  /**
   * Calculate average quality of conversation turns
   */
  private calculateTurnQuality(turns: ConversationTurn[]): number {
    if (turns.length === 0) return 0;

    // Quality factors: length, type, responses
    const avgLength = turns.reduce((sum, turn) => sum + turn.message.length, 0) / turns.length;
    const responseRatio = turns.filter(turn => turn.respondingTo).length / turns.length;
    const improvementRatio = turns.filter(turn => 
      turn.messageType === 'improvement' || turn.messageType === 'synthesis'
    ).length / turns.length;

    // Normalize and combine factors
    const lengthScore = Math.min(1, avgLength / 500); // 500 chars = good length
    const responseScore = responseRatio;
    const improvementScore = improvementRatio;

    return (lengthScore * 0.3) + (responseScore * 0.4) + (improvementScore * 0.3);
  }

  /**
   * Generate quality improvement recommendations
   */
  private generateQualityRecommendations(
    gateResults: Record<string, ValidationResult>,
    state: any
  ): string[] {
    const recommendations: string[] = [];

    // Collect recommendations from failed gates
    Object.values(gateResults).forEach(result => {
      if (!result.passed) {
        recommendations.push(...result.recommendations);
      }
    });

    // Add general recommendations based on state
    const history = state.conversationHistory || [];
    if (history.length < state.activeAgents.length * 2) {
      recommendations.push('Encourage more discussion rounds to develop ideas fully');
    }

    const unresolvedCount = (state.unresolvedIssues || []).length;
    if (unresolvedCount > 3) {
      recommendations.push('Focus on resolving key disagreements before proceeding');
    }

    return [...new Set(recommendations)]; // Remove duplicates
  }

  /**
   * Determine if collaboration should continue
   */
  private shouldContinueCollaboration(
    overallScore: number,
    gateResults: Record<string, ValidationResult>,
    state: any
  ): boolean {
    // Continue if overall quality is low and we haven't exceeded limits
    const qualityThreshold = 0.7;
    const maxRounds = state.maxRounds || 5;
    const currentRound = state.currentRound || 1;

    // Stop if we've reached max rounds
    if (currentRound >= maxRounds) return false;

    // Stop if quality is good enough
    if (overallScore >= qualityThreshold) return false;

    // Continue if there are critical failures that can be improved
    const criticalFailures = Object.values(gateResults).filter(result => 
      !result.passed && result.score < 0.3
    ).length;

    return criticalFailures > 0 && currentRound < maxRounds;
  }
}
