// True Agent Collaboration Framework - Main Export
// This module provides genuine agent-to-agent collaboration using LangGraph

export { ConversationStateManager } from './ConversationState';
export type { 
  ConversationTurn, 
  UnresolvedIssue, 
  ConsensusItem, 
  QualityMetrics, 
  CollaborationState 
} from './ConversationState';

export { ConversationNodes } from './ConversationNodes';
export type { 
  ApiKey, 
  ConversationGraphState 
} from './ConversationNodes';

export { ConversationSupervisor } from './ConversationSupervisor';
export type { CollaborationResult } from './ConversationSupervisor';

export { QualityValidator } from './QualityValidator';
export type { 
  QualityGate, 
  ValidationResult, 
  CollaborationQualityReport 
} from './QualityValidator';

// Test utilities (for development and validation)
export { 
  runCollaborationTest, 
  runAllCollaborationTests, 
  validateCollaborationQuality, 
  analyzeCollaborativeResponse 
} from './test-collaboration';

/**
 * Quick start function for Agent Mode collaboration
 * 
 * @param userPrompt - The user's request
 * @param agentApiKeys - Record of agent API keys
 * @param customApiConfigId - API configuration ID
 * @param maxIterations - Maximum collaboration iterations (default: 10)
 * @returns Promise<CollaborationResult>
 */
export async function executeAgentCollaboration(
  userPrompt: string,
  agentApiKeys: Record<string, any>,
  customApiConfigId: string,
  maxIterations: number = 10
): Promise<CollaborationResult> {
  const { ConversationSupervisor } = await import('./ConversationSupervisor');
  
  const conversationId = `agent_collab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  
  const supervisor = new ConversationSupervisor(
    conversationId,
    userPrompt,
    agentApiKeys,
    customApiConfigId,
    maxIterations
  );
  
  return await supervisor.executeCollaboration();
}

/**
 * Validate if a collaboration result shows genuine agent interaction
 * 
 * @param result - The collaboration result to validate
 * @returns Validation details
 */
export function validateCollaborationResult(result: CollaborationResult): {
  isGenuine: boolean;
  qualityScore: number;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];
  
  // Check quality metrics
  const qualityScore = result.qualityMetrics?.overallScore || 0;
  
  if (qualityScore < 0.7) {
    issues.push(`Low overall quality score: ${(qualityScore * 100).toFixed(1)}%`);
    recommendations.push('Increase collaboration iterations or improve agent prompting');
  }
  
  if (result.qualityMetrics?.participationBalance && result.qualityMetrics.participationBalance < 0.6) {
    issues.push('Unbalanced agent participation');
    recommendations.push('Ensure all agents contribute meaningfully to the discussion');
  }
  
  if (result.qualityMetrics?.iterativeImprovement && result.qualityMetrics.iterativeImprovement < 0.4) {
    issues.push('Limited iterative improvement');
    recommendations.push('Encourage agents to build on each other\'s ideas more actively');
  }
  
  // Check final response for collaboration indicators
  const finalResponse = result.finalResponse || '';
  const hasCollaborationIndicators = 
    finalResponse.includes('discussion') || 
    finalResponse.includes('consensus') || 
    finalResponse.includes('debate') ||
    finalResponse.includes('collaboration');
  
  if (!hasCollaborationIndicators) {
    issues.push('Final response lacks collaboration indicators');
    recommendations.push('Improve synthesis to highlight the collaborative process');
  }
  
  const isGenuine = issues.length === 0 && qualityScore >= 0.7;
  
  return {
    isGenuine,
    qualityScore: qualityScore * 100,
    issues,
    recommendations
  };
}

/**
 * Configuration for different collaboration scenarios
 */
export const CollaborationPresets = {
  // Quick collaboration for simple tasks
  QUICK: {
    maxIterations: 6,
    qualityThreshold: 0.6,
    description: 'Fast collaboration for straightforward tasks'
  },
  
  // Standard collaboration for most use cases
  STANDARD: {
    maxIterations: 10,
    qualityThreshold: 0.7,
    description: 'Balanced collaboration for typical complex tasks'
  },
  
  // Deep collaboration for complex, high-stakes tasks
  DEEP: {
    maxIterations: 15,
    qualityThreshold: 0.8,
    description: 'Thorough collaboration for complex, critical tasks'
  },
  
  // Research-grade collaboration for maximum quality
  RESEARCH: {
    maxIterations: 20,
    qualityThreshold: 0.9,
    description: 'Extensive collaboration for research and analysis'
  }
};

/**
 * Get recommended collaboration preset based on prompt complexity
 * 
 * @param userPrompt - The user's request
 * @returns Recommended preset
 */
export function getRecommendedPreset(userPrompt: string): keyof typeof CollaborationPresets {
  const prompt = userPrompt.toLowerCase();
  
  // Research indicators
  if (prompt.includes('research') || prompt.includes('analysis') || prompt.includes('comprehensive')) {
    return 'RESEARCH';
  }
  
  // Complex task indicators
  if (prompt.includes('design') || prompt.includes('architecture') || prompt.includes('strategy') || 
      prompt.includes('framework') || prompt.length > 500) {
    return 'DEEP';
  }
  
  // Simple task indicators
  if (prompt.includes('quick') || prompt.includes('simple') || prompt.includes('basic') || 
      prompt.length < 100) {
    return 'QUICK';
  }
  
  // Default to standard
  return 'STANDARD';
}

// Version and metadata
export const COLLABORATION_FRAMEWORK_VERSION = '1.0.0';
export const FRAMEWORK_DESCRIPTION = 'True Agent Collaboration Framework using LangGraph for genuine multi-agent conversation and consensus building';
