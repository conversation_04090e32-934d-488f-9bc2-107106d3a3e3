// Test script for the new True Agent Collaboration system
// This validates that agents are actually collaborating rather than just parallel processing

import { ConversationSupervisor } from './ConversationSupervisor';

// Mock API key structure for testing
interface MockApiKey {
  id: string;
  provider: string;
  predefined_model_id: string;
  encrypted_api_key: string;
  label: string;
}

// Test configuration
const testAgentKeys: Record<string, MockApiKey> = {
  agent_1: {
    id: 'test_key_1',
    provider: 'openai',
    predefined_model_id: 'gpt-4',
    encrypted_api_key: 'encrypted_test_key_1',
    label: 'GPT-4 Analyst'
  },
  agent_2: {
    id: 'test_key_2',
    provider: 'anthropic',
    predefined_model_id: 'claude-3-sonnet',
    encrypted_api_key: 'encrypted_test_key_2',
    label: 'Claude Sonnet Strategist'
  },
  agent_3: {
    id: 'test_key_3',
    provider: 'google',
    predefined_model_id: 'gemini-pro',
    encrypted_api_key: 'encrypted_test_key_3',
    label: 'Gemini Pro Innovator'
  },
  agent_4: {
    id: 'test_key_4',
    provider: 'openai',
    predefined_model_id: 'gpt-4-turbo',
    encrypted_api_key: 'encrypted_test_key_4',
    label: 'GPT-4 Turbo Optimizer'
  },
  agent_5: {
    id: 'test_key_5',
    provider: 'anthropic',
    predefined_model_id: 'claude-3-opus',
    encrypted_api_key: 'encrypted_test_key_5',
    label: 'Claude Opus Synthesizer'
  }
};

// Test prompts designed to require real collaboration
const testPrompts = [
  {
    name: 'Complex Technical Challenge',
    prompt: 'Design a distributed system architecture for a real-time collaborative document editor that can handle 1 million concurrent users, with conflict resolution, offline support, and end-to-end encryption. Consider scalability, performance, security, and user experience.',
    expectedCollaborationFeatures: [
      'Agents should debate different architectural approaches',
      'Discussion of trade-offs between consistency and availability',
      'Iterative refinement of the solution based on peer feedback',
      'Consensus building on key technical decisions'
    ]
  },
  {
    name: 'Creative Problem Solving',
    prompt: 'Create an innovative business model for sustainable urban transportation that addresses climate change, reduces traffic congestion, improves accessibility for disabled individuals, and remains economically viable. The solution should be implementable in major cities worldwide.',
    expectedCollaborationFeatures: [
      'Creative brainstorming with diverse perspectives',
      'Challenging and refining each other\'s ideas',
      'Building consensus on feasibility and impact',
      'Synthesizing multiple approaches into a cohesive solution'
    ]
  },
  {
    name: 'Ethical Dilemma Resolution',
    prompt: 'Develop a comprehensive framework for AI ethics in healthcare that balances patient privacy, medical efficacy, algorithmic fairness, and regulatory compliance. Address scenarios involving life-critical decisions, data bias, and conflicting stakeholder interests.',
    expectedCollaborationFeatures: [
      'Debate on ethical principles and priorities',
      'Discussion of real-world implementation challenges',
      'Consensus building on controversial aspects',
      'Iterative improvement of the ethical framework'
    ]
  }
];

/**
 * Test the collaboration quality metrics
 */
export function validateCollaborationQuality(conversationHistory: any[], expectedFeatures: string[]): {
  score: number;
  details: string[];
  passed: boolean;
} {
  const details: string[] = [];
  let score = 0;

  // Check for real agent-to-agent responses
  const responseConnections = conversationHistory.filter(turn => turn.respondingTo).length;
  const responseRatio = conversationHistory.length > 0 ? responseConnections / conversationHistory.length : 0;
  
  if (responseRatio >= 0.4) {
    score += 25;
    details.push(`✅ Good agent-to-agent interaction: ${(responseRatio * 100).toFixed(1)}% of messages respond to others`);
  } else {
    details.push(`❌ Poor agent interaction: Only ${(responseRatio * 100).toFixed(1)}% of messages respond to others`);
  }

  // Check for diverse message types
  const messageTypes = new Set(conversationHistory.map(turn => turn.messageType));
  const hasDebate = messageTypes.has('critique') || messageTypes.has('disagreement');
  const hasImprovement = messageTypes.has('improvement') || messageTypes.has('synthesis');
  
  if (hasDebate && hasImprovement) {
    score += 25;
    details.push(`✅ Diverse collaboration: Found debate (${hasDebate}) and improvement (${hasImprovement}) phases`);
  } else {
    details.push(`❌ Limited collaboration types: Debate=${hasDebate}, Improvement=${hasImprovement}`);
  }

  // Check for iterative development
  const proposalTurns = conversationHistory.filter(turn => turn.messageType === 'proposal').length;
  const improvementTurns = conversationHistory.filter(turn => turn.messageType === 'improvement').length;
  
  if (improvementTurns >= proposalTurns * 0.3) {
    score += 25;
    details.push(`✅ Iterative improvement: ${improvementTurns} improvements on ${proposalTurns} proposals`);
  } else {
    details.push(`❌ Limited iteration: Only ${improvementTurns} improvements on ${proposalTurns} proposals`);
  }

  // Check for balanced participation
  const agentParticipation: Record<string, number> = {};
  conversationHistory.forEach(turn => {
    agentParticipation[turn.agent] = (agentParticipation[turn.agent] || 0) + 1;
  });

  const participationValues = Object.values(agentParticipation);
  const avgParticipation = participationValues.reduce((a, b) => a + b, 0) / participationValues.length;
  const maxDeviation = Math.max(...participationValues.map(val => Math.abs(val - avgParticipation)));
  const balanceScore = Math.max(0, 1 - (maxDeviation / (avgParticipation || 1)));

  if (balanceScore >= 0.7) {
    score += 25;
    details.push(`✅ Balanced participation: ${(balanceScore * 100).toFixed(1)}% balance score`);
  } else {
    details.push(`❌ Unbalanced participation: ${(balanceScore * 100).toFixed(1)}% balance score`);
  }

  const passed = score >= 75; // Require 75% to pass
  
  return {
    score,
    details,
    passed
  };
}

/**
 * Analyze the final response for collaboration indicators
 */
export function analyzeCollaborativeResponse(finalResponse: string): {
  hasRealCollaboration: boolean;
  indicators: string[];
  concerns: string[];
} {
  const indicators: string[] = [];
  const concerns: string[] = [];

  // Check for genuine collaboration indicators
  if (finalResponse.includes('Agent') && finalResponse.includes('discussed')) {
    indicators.push('References specific agent discussions');
  }

  if (finalResponse.includes('consensus') || finalResponse.includes('agreement')) {
    indicators.push('Mentions consensus building');
  }

  if (finalResponse.includes('debate') || finalResponse.includes('disagreement')) {
    indicators.push('Acknowledges debate and disagreement');
  }

  if (finalResponse.includes('refined') || finalResponse.includes('improved')) {
    indicators.push('Shows iterative improvement');
  }

  // Check for fake collaboration red flags
  if (finalResponse.includes('This response was created through true round-table collaboration')) {
    concerns.push('Uses boilerplate collaboration language');
  }

  if (finalResponse.includes('Agent 1:', 'Agent 2:', 'Agent 3:') && 
      finalResponse.split('Agent').length > 5) {
    concerns.push('Appears to list separate agent outputs rather than synthesized collaboration');
  }

  if (!finalResponse.includes('discussion') && !finalResponse.includes('collaboration')) {
    concerns.push('No mention of actual collaborative process');
  }

  const hasRealCollaboration = indicators.length >= 2 && concerns.length <= 1;

  return {
    hasRealCollaboration,
    indicators,
    concerns
  };
}

/**
 * Run a comprehensive test of the collaboration system
 */
export async function runCollaborationTest(
  testName: string,
  prompt: string,
  expectedFeatures: string[]
): Promise<{
  testName: string;
  success: boolean;
  qualityScore: number;
  collaborationAnalysis: any;
  responseAnalysis: any;
  recommendations: string[];
}> {
  console.log(`\n🧪 Running collaboration test: ${testName}`);
  console.log(`📝 Prompt: ${prompt.substring(0, 100)}...`);

  try {
    // Create conversation supervisor
    const supervisor = new ConversationSupervisor(
      `test_${Date.now()}`,
      prompt,
      testAgentKeys,
      'test_config',
      8 // Max iterations for thorough testing
    );

    // Execute collaboration
    const result = await supervisor.executeCollaboration();

    if (!result.success) {
      return {
        testName,
        success: false,
        qualityScore: 0,
        collaborationAnalysis: { score: 0, details: [`Failed: ${result.error}`], passed: false },
        responseAnalysis: { hasRealCollaboration: false, indicators: [], concerns: ['Test failed'] },
        recommendations: ['Fix the underlying collaboration framework']
      };
    }

    // Analyze collaboration quality
    const conversationHistory = []; // Would get from supervisor's conversation manager
    const collaborationAnalysis = validateCollaborationQuality(conversationHistory, expectedFeatures);

    // Analyze final response
    const responseAnalysis = analyzeCollaborativeResponse(result.finalResponse || '');

    // Generate recommendations
    const recommendations: string[] = [];
    if (!collaborationAnalysis.passed) {
      recommendations.push('Improve agent-to-agent interaction patterns');
    }
    if (!responseAnalysis.hasRealCollaboration) {
      recommendations.push('Enhance synthesis to show genuine collaboration');
    }
    if (result.qualityMetrics && result.qualityMetrics.overallScore < 0.8) {
      recommendations.push('Increase overall collaboration quality');
    }

    const success = collaborationAnalysis.passed && responseAnalysis.hasRealCollaboration;

    console.log(`${success ? '✅' : '❌'} Test ${testName}: ${success ? 'PASSED' : 'FAILED'}`);
    console.log(`📊 Quality Score: ${collaborationAnalysis.score}/100`);

    return {
      testName,
      success,
      qualityScore: collaborationAnalysis.score,
      collaborationAnalysis,
      responseAnalysis,
      recommendations
    };

  } catch (error) {
    console.error(`❌ Test ${testName} failed with error:`, error);
    return {
      testName,
      success: false,
      qualityScore: 0,
      collaborationAnalysis: { score: 0, details: [`Error: ${error}`], passed: false },
      responseAnalysis: { hasRealCollaboration: false, indicators: [], concerns: ['Test error'] },
      recommendations: ['Debug and fix the collaboration framework']
    };
  }
}

/**
 * Run all collaboration tests
 */
export async function runAllCollaborationTests(): Promise<void> {
  console.log('🚀 Starting comprehensive Agent Mode collaboration tests...\n');

  const results = [];

  for (const testCase of testPrompts) {
    const result = await runCollaborationTest(
      testCase.name,
      testCase.prompt,
      testCase.expectedCollaborationFeatures
    );
    results.push(result);
  }

  // Summary
  const passedTests = results.filter(r => r.success).length;
  const avgQualityScore = results.reduce((sum, r) => sum + r.qualityScore, 0) / results.length;

  console.log('\n📋 Test Summary:');
  console.log(`✅ Passed: ${passedTests}/${results.length} tests`);
  console.log(`📊 Average Quality Score: ${avgQualityScore.toFixed(1)}/100`);

  if (passedTests === results.length) {
    console.log('🎉 All tests passed! The new collaboration system is working correctly.');
  } else {
    console.log('⚠️ Some tests failed. Review the recommendations for improvements.');
    
    results.forEach(result => {
      if (!result.success) {
        console.log(`\n❌ ${result.testName}:`);
        result.recommendations.forEach(rec => console.log(`  - ${rec}`));
      }
    });
  }
}

// Export for use in other test files
export { testAgentKeys, testPrompts };
