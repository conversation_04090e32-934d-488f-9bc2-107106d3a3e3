// Browsing Detection Service - Disabled Stub for Launch
// This is a stub file to prevent build errors while browsing is disabled

export interface BrowsingDecision {
  shouldBrowse: boolean;
  confidence: number;
  browsingType: 'search' | 'navigate' | 'extract';
  refinedQuery?: string;
  reason?: string;
}

export class BrowsingDetectionService {
  private static instance: BrowsingDetectionService;

  static getInstance(): BrowsingDetectionService {
    if (!BrowsingDetectionService.instance) {
      BrowsingDetectionService.instance = new BrowsingDetectionService();
    }
    return BrowsingDetectionService.instance;
  }

  async shouldTriggerBrowsing(
    query: string,
    conversationContext?: string
  ): Promise<BrowsingDecision> {
    // Always return false since browsing is disabled for launch
    return {
      shouldBrowse: false,
      confidence: 0,
      browsingType: 'search',
      reason: 'Browsing is temporarily disabled for launch'
    };
  }
}
