// Browsing Execution Service - Disabled Stub for Launch
// This is a stub file to prevent build errors while browsing is disabled

export interface BrowsingExecutionResult {
  success: boolean;
  content?: string;
  error?: string;
  modelUsed?: string;
  providerUsed?: string;
  browsingData?: any;
}

export interface BrowsingConfig {
  browsing_enabled: boolean;
  browsing_models: any[];
}

export class BrowsingExecutionService {
  private static instance: BrowsingExecutionService;

  static getInstance(): BrowsingExecutionService {
    if (!BrowsingExecutionService.instance) {
      BrowsingExecutionService.instance = new BrowsingExecutionService();
    }
    return BrowsingExecutionService.instance;
  }

  async executeBrowsing(
    query: string,
    browsingConfig: BrowsingConfig,
    browsingType: 'search' | 'navigate' | 'extract' = 'search',
    refinedQuery?: string,
    useSmartBrowsing: boolean = true,
    progressCallback?: any
  ): Promise<BrowsingExecutionResult> {
    // Always return disabled message since browsing is disabled for launch
    return {
      success: false,
      error: 'Browsing is temporarily disabled for launch'
    };
  }
}
