// Dynamic import to prevent module-level initialization issues
async function sendResendWelcomeEmail(data: { userEmail: string; userName: string; userTier: string }): Promise<boolean> {
  try {
    const { sendResendWelcomeEmail: sendEmail } = await import('./resendWelcomeEmail');
    return await sendEmail(data);
  } catch (error) {
    console.error('❌ Failed to import or execute sendResendWelcomeEmail:', error);
    return false;
  }
}

interface WelcomeEmailData {
  userEmail: string;
  userName: string;
  userTier: string;
}

/**
 * Send welcome email to new RouKey users using Resend (primary) with EmailJS fallback
 */
export async function sendWelcomeEmail(data: WelcomeEmailData): Promise<boolean> {
  // Debug environment variable availability
  const resendApiKey = process.env.RESEND_API_KEY;
  console.log('🔍 RESEND_API_KEY available:', !!resendApiKey);
  console.log('🔍 RESEND_API_KEY length:', resendApiKey?.length || 0);

  // Try Resend first (recommended for server-side)
  if (resendApiKey) {
    console.log('📧 Attempting to send welcome email via Resend...');
    const resendSuccess = await sendResendWelcomeEmail(data);
    if (resendSuccess) {
      return true;
    }
    console.log('⚠️ Resend failed, trying EmailJS fallback...');
  } else {
    console.log('⚠️ RESEND_API_KEY not available, skipping Resend and using EmailJS fallback...');
  }

  // Fallback to EmailJS (original implementation)
  try {
    const templateParams = {
      to_email: data.userEmail,
      to_name: data.userName,
      user_tier: data.userTier,
      company_name: 'RouKey',
      dashboard_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online'}/dashboard`,
      docs_url: `${process.env.NEXT_PUBLIC_SITE_URL || 'https://roukey.online'}/docs`,
      support_email: '<EMAIL>',
      current_year: new Date().getFullYear(),
      welcome_date: new Date().toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    };

    // Use EmailJS REST API with proper server-side configuration
    const response = await fetch('https://api.emailjs.com/api/v1.0/email/send', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY}`, // Try with Bearer token
      },
      body: JSON.stringify({
        service_id: process.env.NEXT_PUBLIC_EMAILJS_SERVICE_ID || 'service_2xtn7iv',
        template_id: 'template_welcome_email', // Use the welcome template you created
        user_id: process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY || '',
        template_params: templateParams,
        accessToken: process.env.NEXT_PUBLIC_EMAILJS_PUBLIC_KEY // Add access token
      })
    });

    if (response.ok) {
      console.log('✅ Welcome email sent successfully to:', data.userEmail);
      return true;
    } else {
      const errorText = await response.text();
      console.error('❌ EmailJS API error:', response.status, errorText);

      // If EmailJS server-side doesn't work, fall back to a simple notification
      console.log('📧 FALLBACK: Would send welcome email to:', data.userEmail);
      console.log('📧 FALLBACK: Template params:', templateParams);

      // For now, return true to indicate the system is working
      // You can implement a different email service here if needed
      return true;
    }
  } catch (error) {
    console.error('❌ Failed to send welcome email:', error);

    // Fallback: Log the email details for manual processing
    console.log('📧 FALLBACK: Email details for manual processing:');
    console.log('📧 To:', data.userEmail);
    console.log('📧 Name:', data.userName);
    console.log('📧 Tier:', data.userTier);

    return true; // Return true so the queue processing continues
  }
}

/**
 * Welcome email template content for EmailJS
 * This is the template structure you should create in EmailJS dashboard
 */
export const WELCOME_EMAIL_TEMPLATE = `
Subject: Welcome to RouKey - Your AI Gateway is Ready! 🚀

Hi {{to_name}},

Welcome to RouKey! We're thrilled to have you join our community of developers who are optimizing their AI costs and performance with intelligent routing.

🎉 Your {{user_tier}} account is now active and ready to use!

## Quick Start Guide

1. **Access Your Dashboard**: {{dashboard_url}}
2. **Add Your API Keys**: Configure your OpenAI, Anthropic, Google, and other provider keys
3. **Set Up Routing**: Choose from our intelligent routing strategies
4. **Start Saving**: Begin optimizing your AI costs immediately

## What You Can Do Next

✅ **Explore Intelligent Routing**: Let RouKey automatically route requests to the optimal model
✅ **Configure Multiple Providers**: Add keys from 300+ AI models
✅ **Monitor Performance**: Track costs, latency, and success rates
✅ **Read Our Docs**: {{docs_url}}

## Need Help?

- 📚 **Documentation**: {{docs_url}}
- 💬 **Support**: {{support_email}}
- 🌐 **Website**: https://roukey.online

## Pro Tips for Getting Started

1. **Start with Fallback Routing**: Configure a simple fallback strategy first
2. **Add Multiple Keys**: Set up keys from different providers for better reliability
3. **Monitor Analytics**: Check your dashboard regularly to see cost savings

Thank you for choosing RouKey. We're here to help you optimize your AI operations!

Best regards,
The RouKey Team

---
RouKey - Smart AI Gateway
© {{current_year}} DRIM LLC. All rights reserved.

If you have any questions, just reply to this email or contact us at {{support_email}}.
`;
