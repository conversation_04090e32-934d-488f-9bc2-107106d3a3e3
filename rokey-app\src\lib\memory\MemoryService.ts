/**
 * Memory Service for Manual Build Workflows
 * Provides persistent memory storage and retrieval for connected nodes
 */

import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

interface MemoryEntry {
  id: string;
  memory_name: string;
  user_id: string;
  workflow_id: string;
  node_id: string;
  data_type: 'browsing' | 'routing' | 'general';
  data: any;
  metadata: {
    created_at: string;
    updated_at: string;
    size_kb: number;
    encrypted: boolean;
  };
}

interface MemoryConfig {
  memoryName: string;
  maxSize: number; // in KB
  encryption: boolean;
  description?: string;
}

export class MemoryService {
  private supabase;
  private memoryCache = new Map<string, any>();
  private cacheExpiry = new Map<string, number>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  constructor() {
    this.supabase = createClient(supabaseUrl, supabaseServiceKey);
  }

  /**
   * Store data in memory for a specific node
   */
  async store(
    memoryName: string,
    nodeId: string,
    workflowId: string,
    userId: string,
    data: any,
    dataType: 'browsing' | 'routing' | 'general' = 'general',
    config: MemoryConfig
  ): Promise<boolean> {
    try {
      const dataString = JSON.stringify(data);
      const sizeKB = new TextEncoder().encode(dataString).length / 1024;

      // Check size limit
      if (sizeKB > config.maxSize) {
        console.warn(`Memory data exceeds size limit: ${sizeKB}KB > ${config.maxSize}KB`);
        return false;
      }

      // Encrypt if enabled
      let finalData = data;
      if (config.encryption) {
        finalData = this.encrypt(dataString);
      }

      const memoryEntry: Partial<MemoryEntry> = {
        memory_name: memoryName,
        user_id: userId,
        workflow_id: workflowId,
        node_id: nodeId,
        data_type: dataType,
        data: finalData,
        metadata: {
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          size_kb: sizeKB,
          encrypted: config.encryption
        }
      };

      // Upsert to database
      const { error } = await this.supabase
        .from('workflow_memory')
        .upsert(memoryEntry, {
          onConflict: 'memory_name,user_id,workflow_id,node_id'
        });

      if (error) {
        console.error('Memory store error:', error);
        return false;
      }

      // Update cache
      const cacheKey = `${userId}:${workflowId}:${nodeId}:${memoryName}`;
      this.memoryCache.set(cacheKey, data);
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL);

      console.log(`✅ Memory stored: ${memoryName} (${sizeKB.toFixed(2)}KB)`);
      return true;

    } catch (error) {
      console.error('Memory store error:', error);
      return false;
    }
  }

  /**
   * Retrieve data from memory for a specific node
   */
  async retrieve(
    memoryName: string,
    nodeId: string,
    workflowId: string,
    userId: string
  ): Promise<any | null> {
    try {
      const cacheKey = `${userId}:${workflowId}:${nodeId}:${memoryName}`;

      // Check cache first
      if (this.memoryCache.has(cacheKey)) {
        const expiry = this.cacheExpiry.get(cacheKey) || 0;
        if (Date.now() < expiry) {
          console.log(`🚀 Memory cache hit: ${memoryName}`);
          return this.memoryCache.get(cacheKey);
        } else {
          // Expired
          this.memoryCache.delete(cacheKey);
          this.cacheExpiry.delete(cacheKey);
        }
      }

      // Fetch from database
      const { data, error } = await this.supabase
        .from('workflow_memory')
        .select('*')
        .eq('memory_name', memoryName)
        .eq('user_id', userId)
        .eq('workflow_id', workflowId)
        .eq('node_id', nodeId)
        .single();

      if (error || !data) {
        console.log(`📭 Memory not found: ${memoryName}`);
        return null;
      }

      // Decrypt if needed
      let finalData = data.data;
      if (data.metadata.encrypted) {
        finalData = this.decrypt(data.data);
      }

      // Update cache
      this.memoryCache.set(cacheKey, finalData);
      this.cacheExpiry.set(cacheKey, Date.now() + this.CACHE_TTL);

      console.log(`📖 Memory retrieved: ${memoryName} (${data.metadata.size_kb.toFixed(2)}KB)`);
      return finalData;

    } catch (error) {
      console.error('Memory retrieve error:', error);
      return null;
    }
  }

  /**
   * Store browsing memory (specialized for browsing nodes)
   */
  async storeBrowsingMemory(
    memoryName: string,
    nodeId: string,
    workflowId: string,
    userId: string,
    browsingData: {
      completedSubtasks: string[];
      visitedUrls: string[];
      searchQueries: string[];
      gatheredData: any;
      currentContext: string;
      preferences: any;
    },
    config: MemoryConfig
  ): Promise<boolean> {
    return this.store(memoryName, nodeId, workflowId, userId, browsingData, 'browsing', config);
  }

  /**
   * Store routing memory (specialized for router nodes)
   */
  async storeRoutingMemory(
    memoryName: string,
    nodeId: string,
    workflowId: string,
    userId: string,
    routingData: {
      routingDecisions: Array<{
        query: string;
        selectedProvider: string;
        reason: string;
        performance: number;
        timestamp: string;
      }>;
      userPreferences: any;
      providerPerformance: Record<string, number>;
      learningData: any;
    },
    config: MemoryConfig
  ): Promise<boolean> {
    return this.store(memoryName, nodeId, workflowId, userId, routingData, 'routing', config);
  }

  /**
   * Get all memory entries for a workflow
   */
  async getWorkflowMemory(workflowId: string, userId: string): Promise<MemoryEntry[]> {
    try {
      const { data, error } = await this.supabase
        .from('workflow_memory')
        .select('*')
        .eq('workflow_id', workflowId)
        .eq('user_id', userId);

      if (error) {
        console.error('Get workflow memory error:', error);
        return [];
      }

      return data || [];
    } catch (error) {
      console.error('Get workflow memory error:', error);
      return [];
    }
  }

  /**
   * Clear memory for a specific node
   */
  async clearNodeMemory(nodeId: string, workflowId: string, userId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('workflow_memory')
        .delete()
        .eq('node_id', nodeId)
        .eq('workflow_id', workflowId)
        .eq('user_id', userId);

      if (error) {
        console.error('Clear node memory error:', error);
        return false;
      }

      // Clear cache
      const cacheKeys = Array.from(this.memoryCache.keys()).filter(key => 
        key.includes(`${userId}:${workflowId}:${nodeId}`)
      );
      cacheKeys.forEach(key => {
        this.memoryCache.delete(key);
        this.cacheExpiry.delete(key);
      });

      console.log(`🗑️ Memory cleared for node: ${nodeId}`);
      return true;
    } catch (error) {
      console.error('Clear node memory error:', error);
      return false;
    }
  }

  /**
   * Simple encryption (for demo - use proper encryption in production)
   */
  private encrypt(data: string): string {
    // Simple base64 encoding for demo - replace with proper encryption
    return Buffer.from(data).toString('base64');
  }

  /**
   * Simple decryption (for demo - use proper decryption in production)
   */
  private decrypt(encryptedData: string): any {
    try {
      const decrypted = Buffer.from(encryptedData, 'base64').toString();
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Decryption error:', error);
      return null;
    }
  }

  /**
   * Get memory statistics
   */
  getStats(): any {
    return {
      cacheSize: this.memoryCache.size,
      cacheHitRate: '95%', // Placeholder - implement actual tracking
      totalMemoryEntries: 'N/A' // Would need to query DB
    };
  }
}

// Export singleton instance
export const memoryService = new MemoryService();
