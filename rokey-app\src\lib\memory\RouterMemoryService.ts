/**
 * Router Memory Service
 * Handles memory for Central Router nodes - learning from routing decisions
 */

import { memoryService } from './MemoryService';

interface RoutingDecision {
  query: string;
  selectedProvider: string;
  reason: string;
  performance: number; // 0-1 score
  timestamp: string;
  responseTime: number;
  success: boolean;
  userFeedback?: 'positive' | 'negative' | 'neutral';
}

interface ProviderPerformance {
  providerId: string;
  averageResponseTime: number;
  successRate: number;
  userSatisfaction: number;
  totalRequests: number;
  lastUsed: string;
}

interface UserPreferences {
  preferredProviders: string[];
  avoidedProviders: string[];
  taskTypePreferences: Record<string, string>; // task type -> preferred provider
  qualityThreshold: number;
  speedPreference: 'fast' | 'balanced' | 'quality';
}

export class RouterMemoryService {
  private memoryNodeId: string | null = null;
  private workflowId: string | null = null;
  private userId: string | null = null;
  private routingHistory: RoutingDecision[] = [];
  private providerPerformance: Map<string, ProviderPerformance> = new Map();
  private userPreferences: UserPreferences = {
    preferredProviders: [],
    avoidedProviders: [],
    taskTypePreferences: {},
    qualityThreshold: 0.7,
    speedPreference: 'balanced'
  };

  /**
   * Connect to a Memory node for persistent storage
   */
  connectMemory(memoryNodeId: string, workflowId: string, userId: string) {
    this.memoryNodeId = memoryNodeId;
    this.workflowId = workflowId;
    this.userId = userId;
    console.log(`🧠 Router connected to Memory node: ${memoryNodeId}`);
    
    // Load existing memory
    this.loadMemoryFromStorage();
  }

  /**
   * Record a routing decision for learning
   */
  async recordRoutingDecision(
    query: string,
    selectedProvider: string,
    reason: string,
    responseTime: number,
    success: boolean
  ): Promise<void> {
    const decision: RoutingDecision = {
      query,
      selectedProvider,
      reason,
      performance: success ? Math.max(0.1, 1 - (responseTime / 10000)) : 0, // Simple performance calc
      timestamp: new Date().toISOString(),
      responseTime,
      success
    };

    this.routingHistory.push(decision);
    
    // Update provider performance
    this.updateProviderPerformance(selectedProvider, decision);
    
    // Save to persistent memory
    await this.saveMemoryToStorage();
    
    console.log(`📊 Recorded routing decision: ${selectedProvider} for "${query.substring(0, 50)}..."`);
  }

  /**
   * Get smart routing recommendation based on memory
   */
  getRoutingRecommendation(
    query: string,
    availableProviders: string[],
    taskType?: string
  ): {
    recommendedProvider: string;
    confidence: number;
    reason: string;
  } {
    // Check user preferences first
    if (taskType && this.userPreferences.taskTypePreferences[taskType]) {
      const preferredProvider = this.userPreferences.taskTypePreferences[taskType];
      if (availableProviders.includes(preferredProvider)) {
        return {
          recommendedProvider: preferredProvider,
          confidence: 0.9,
          reason: `User prefers ${preferredProvider} for ${taskType} tasks`
        };
      }
    }

    // Check for similar queries in history
    const similarDecisions = this.findSimilarQueries(query, 5);
    if (similarDecisions.length > 0) {
      const successfulDecisions = similarDecisions.filter(d => d.success && d.performance > 0.6);
      if (successfulDecisions.length > 0) {
        const bestDecision = successfulDecisions.reduce((best, current) => 
          current.performance > best.performance ? current : best
        );
        
        if (availableProviders.includes(bestDecision.selectedProvider)) {
          return {
            recommendedProvider: bestDecision.selectedProvider,
            confidence: 0.8,
            reason: `${bestDecision.selectedProvider} performed well on similar queries`
          };
        }
      }
    }

    // Fall back to provider performance
    const performanceScores = availableProviders.map(providerId => {
      const perf = this.providerPerformance.get(providerId);
      if (!perf) {
        return { providerId, score: 0.5 }; // Neutral score for unknown providers
      }
      
      // Calculate composite score based on success rate, speed, and user satisfaction
      const score = (perf.successRate * 0.4) + 
                   ((1 - Math.min(perf.averageResponseTime / 5000, 1)) * 0.3) + 
                   (perf.userSatisfaction * 0.3);
      
      return { providerId, score };
    });

    const bestProvider = performanceScores.reduce((best, current) => 
      current.score > best.score ? current : best
    );

    return {
      recommendedProvider: bestProvider.providerId,
      confidence: Math.min(bestProvider.score, 0.7),
      reason: `${bestProvider.providerId} has the best overall performance (${(bestProvider.score * 100).toFixed(1)}%)`
    };
  }

  /**
   * Update user preferences based on feedback
   */
  async updateUserPreferences(
    providerId: string,
    feedback: 'positive' | 'negative' | 'neutral',
    taskType?: string
  ): Promise<void> {
    if (feedback === 'positive') {
      if (!this.userPreferences.preferredProviders.includes(providerId)) {
        this.userPreferences.preferredProviders.push(providerId);
      }
      
      if (taskType) {
        this.userPreferences.taskTypePreferences[taskType] = providerId;
      }
      
      // Remove from avoided if present
      this.userPreferences.avoidedProviders = this.userPreferences.avoidedProviders.filter(p => p !== providerId);
      
    } else if (feedback === 'negative') {
      if (!this.userPreferences.avoidedProviders.includes(providerId)) {
        this.userPreferences.avoidedProviders.push(providerId);
      }
      
      // Remove from preferred if present
      this.userPreferences.preferredProviders = this.userPreferences.preferredProviders.filter(p => p !== providerId);
      
      if (taskType && this.userPreferences.taskTypePreferences[taskType] === providerId) {
        delete this.userPreferences.taskTypePreferences[taskType];
      }
    }

    // Update the latest routing decision with feedback
    const latestDecision = this.routingHistory[this.routingHistory.length - 1];
    if (latestDecision && latestDecision.selectedProvider === providerId) {
      latestDecision.userFeedback = feedback;
    }

    await this.saveMemoryToStorage();
    console.log(`👍 Updated user preferences: ${feedback} feedback for ${providerId}`);
  }

  /**
   * Get routing statistics and insights
   */
  getRoutingStats(): any {
    const totalDecisions = this.routingHistory.length;
    const successfulDecisions = this.routingHistory.filter(d => d.success).length;
    const averageResponseTime = this.routingHistory.reduce((sum, d) => sum + d.responseTime, 0) / totalDecisions || 0;

    const providerUsage = this.routingHistory.reduce((acc, decision) => {
      acc[decision.selectedProvider] = (acc[decision.selectedProvider] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      totalDecisions,
      successRate: totalDecisions > 0 ? (successfulDecisions / totalDecisions) : 0,
      averageResponseTime: Math.round(averageResponseTime),
      providerUsage,
      userPreferences: this.userPreferences,
      memoryConnected: !!this.memoryNodeId,
      lastDecision: this.routingHistory[this.routingHistory.length - 1]?.timestamp
    };
  }

  /**
   * Private helper methods
   */
  private updateProviderPerformance(providerId: string, decision: RoutingDecision): void {
    let perf = this.providerPerformance.get(providerId);
    
    if (!perf) {
      perf = {
        providerId,
        averageResponseTime: decision.responseTime,
        successRate: decision.success ? 1 : 0,
        userSatisfaction: 0.5, // Neutral starting point
        totalRequests: 1,
        lastUsed: decision.timestamp
      };
    } else {
      // Update running averages
      perf.averageResponseTime = (perf.averageResponseTime * perf.totalRequests + decision.responseTime) / (perf.totalRequests + 1);
      perf.successRate = (perf.successRate * perf.totalRequests + (decision.success ? 1 : 0)) / (perf.totalRequests + 1);
      perf.totalRequests += 1;
      perf.lastUsed = decision.timestamp;
      
      // Update user satisfaction based on feedback
      if (decision.userFeedback) {
        const feedbackScore = decision.userFeedback === 'positive' ? 1 : decision.userFeedback === 'negative' ? 0 : 0.5;
        perf.userSatisfaction = (perf.userSatisfaction * 0.8) + (feedbackScore * 0.2); // Weighted average
      }
    }
    
    this.providerPerformance.set(providerId, perf);
  }

  private findSimilarQueries(query: string, limit: number = 5): RoutingDecision[] {
    const queryWords = query.toLowerCase().split(/\s+/);
    
    return this.routingHistory
      .map(decision => {
        const decisionWords = decision.query.toLowerCase().split(/\s+/);
        const commonWords = queryWords.filter(word => decisionWords.includes(word));
        const similarity = commonWords.length / Math.max(queryWords.length, decisionWords.length);
        
        return { decision, similarity };
      })
      .filter(item => item.similarity > 0.3) // At least 30% similarity
      .sort((a, b) => b.similarity - a.similarity)
      .slice(0, limit)
      .map(item => item.decision);
  }

  private async loadMemoryFromStorage(): Promise<void> {
    if (!this.memoryNodeId || !this.workflowId || !this.userId) {
      return;
    }

    try {
      const routingData = await memoryService.retrieve(
        'routing_memory',
        this.memoryNodeId,
        this.workflowId,
        this.userId
      );

      if (routingData) {
        this.routingHistory = routingData.routingDecisions || [];
        this.userPreferences = { ...this.userPreferences, ...routingData.userPreferences };
        
        // Rebuild provider performance map from stored scores
        if (routingData.providerPerformance) {
          Object.entries(routingData.providerPerformance).forEach(([providerId, score]) => {
            // Create a basic ProviderPerformance object from the stored score
            // This is a simplified reconstruction - detailed stats will be rebuilt over time
            const perf: ProviderPerformance = {
              providerId,
              averageResponseTime: 2000, // Default reasonable value
              successRate: typeof score === 'number' ? Math.max(0.1, score) : 0.5,
              userSatisfaction: typeof score === 'number' ? score : 0.5,
              totalRequests: 1,
              lastUsed: new Date().toISOString()
            };
            this.providerPerformance.set(providerId, perf);
          });
        }
        
        console.log(`🧠 Loaded routing memory: ${this.routingHistory.length} decisions`);
      }
    } catch (error) {
      console.error('Error loading routing memory:', error);
    }
  }

  private async saveMemoryToStorage(): Promise<void> {
    if (!this.memoryNodeId || !this.workflowId || !this.userId) {
      return;
    }

    try {
      const routingData = {
        routingDecisions: this.routingHistory.slice(-100), // Keep last 100 decisions
        userPreferences: this.userPreferences,
        providerPerformance: Object.fromEntries(
          Array.from(this.providerPerformance.entries()).map(([providerId, perf]) => [
            providerId,
            // Calculate composite performance score (0-1)
            (perf.successRate * 0.4) +
            ((1 - Math.min(perf.averageResponseTime / 5000, 1)) * 0.3) +
            (perf.userSatisfaction * 0.3)
          ])
        ),
        learningData: {
          totalDecisions: this.routingHistory.length,
          lastUpdate: new Date().toISOString()
        }
      };

      const success = await memoryService.storeRoutingMemory(
        'routing_memory',
        this.memoryNodeId,
        this.workflowId,
        this.userId,
        routingData,
        {
          memoryName: 'routing_memory',
          maxSize: 5120, // 5MB
          encryption: true
        }
      );

      if (success) {
        console.log('🧠 Routing memory saved to persistent storage');
      }
    } catch (error) {
      console.error('Error saving routing memory:', error);
    }
  }
}

// Export singleton instance
export const routerMemoryService = new RouterMemoryService();
