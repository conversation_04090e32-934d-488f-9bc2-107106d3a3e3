-- Memory storage table for Manual Build workflows
CREATE TABLE IF NOT EXISTS workflow_memory (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  memory_name TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  workflow_id TEXT NOT NULL,
  node_id TEXT NOT NULL,
  data_type TEXT NOT NULL CHECK (data_type IN ('browsing', 'routing', 'general')),
  data JSONB NOT NULL,
  metadata JSONB NOT NULL DEFAULT '{}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  -- Composite unique constraint to prevent duplicates
  UNIQUE(memory_name, user_id, workflow_id, node_id)
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_workflow_memory_user_workflow 
ON workflow_memory(user_id, workflow_id);

CREATE INDEX IF NOT EXISTS idx_workflow_memory_node 
ON workflow_memory(node_id);

CREATE INDEX IF NOT EXISTS idx_workflow_memory_type 
ON workflow_memory(data_type);

CREATE INDEX IF NOT EXISTS idx_workflow_memory_name 
ON workflow_memory(memory_name);

-- RLS policies
ALTER TABLE workflow_memory ENABLE ROW LEVEL SECURITY;

-- Users can only access their own memory
CREATE POLICY "Users can access own memory" ON workflow_memory
  FOR ALL USING (auth.uid() = user_id);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_workflow_memory_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at
CREATE TRIGGER update_workflow_memory_updated_at
  BEFORE UPDATE ON workflow_memory
  FOR EACH ROW
  EXECUTE FUNCTION update_workflow_memory_updated_at();
