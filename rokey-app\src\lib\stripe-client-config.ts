// Client-side Stripe Configuration
// This file handles environment detection for the frontend

const isProduction = process.env.NODE_ENV === 'production';

// Client-side Price IDs - Auto-selected based on environment
export const CLIENT_STRIPE_PRICE_IDS = {
  FREE: isProduction 
    ? process.env.NEXT_PUBLIC_STRIPE_LIVE_FREE_PRICE_ID
    : process.env.NEXT_PUBLIC_STRIPE_TEST_FREE_PRICE_ID,
    
  STARTER: isProduction 
    ? process.env.NEXT_PUBLIC_STRIPE_LIVE_STARTER_PRICE_ID
    : process.env.NEXT_PUBLIC_STRIPE_TEST_STARTER_PRICE_ID,
    
  PROFESSIONAL: isProduction 
    ? process.env.NEXT_PUBLIC_STRIPE_LIVE_PROFESSIONAL_PRICE_ID
    : process.env.NEXT_PUBLIC_STRIPE_TEST_PROFESSIONAL_PRICE_ID,
    
  ENTERPRISE: isProduction 
    ? process.env.NEXT_PUBLIC_STRIPE_LIVE_ENTERPRISE_PRICE_ID
    : process.env.NEXT_PUBLIC_STRIPE_TEST_ENTERPRISE_PRICE_ID
};

// Helper function to get price ID for a tier
export function getPriceIdForTier(tier: string): string {
  switch (tier.toLowerCase()) {
    case 'free':
      return CLIENT_STRIPE_PRICE_IDS.FREE || '';
    case 'starter':
      return CLIENT_STRIPE_PRICE_IDS.STARTER || '';
    case 'professional':
      return CLIENT_STRIPE_PRICE_IDS.PROFESSIONAL || '';
    // case 'enterprise': // Temporarily disabled for launch
    //   return CLIENT_STRIPE_PRICE_IDS.ENTERPRISE || '';
    default:
      throw new Error(`Invalid tier: ${tier}`);
  }
}

// Environment info for debugging
export const CLIENT_STRIPE_ENV_INFO = {
  isProduction,
  environment: isProduction ? 'LIVE' : 'TEST',
  priceIds: CLIENT_STRIPE_PRICE_IDS
};

// Log environment info in development
if (!isProduction && typeof window !== 'undefined') {
  console.log('🔧 Client Stripe Environment:', CLIENT_STRIPE_ENV_INFO.environment);
  console.log('🔑 Using price IDs:', CLIENT_STRIPE_ENV_INFO.priceIds);
}
