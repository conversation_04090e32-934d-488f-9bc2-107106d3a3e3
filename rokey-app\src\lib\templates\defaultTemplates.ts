/**
 * Default workflow templates for Manual Build
 * These templates provide starting points for common workflow patterns
 */

export const defaultTemplates = [
  {
    name: "Content Creation Assistant",
    description: "A workflow for generating blog posts, articles, and marketing content with AI assistance",
    category: "Content Creation",
    template_nodes: [
      {
        id: "user-request",
        type: "userRequest",
        position: { x: 50, y: 200 },
        data: {
          label: "User Request",
          config: {},
          isConfigured: true,
          description: "Content creation request input"
        }
      },
      {
        id: "classifier",
        type: "classifier",
        position: { x: 350, y: 200 },
        data: {
          label: "Content Classifier",
          config: {},
          isConfigured: true,
          description: "Analyzes content type and requirements"
        }
      },
      {
        id: "writing-role",
        type: "roleAgent",
        position: { x: 650, y: 100 },
        data: {
          label: "Content Writer",
          config: {
            roleId: "content_writer",
            roleName: "Content Writer",
            roleType: "predefined"
          },
          isConfigured: true,
          description: "Specialized content writing role"
        }
      },
      {
        id: "ai-provider",
        type: "provider",
        position: { x: 950, y: 200 },
        data: {
          label: "AI Provider",
          config: {
            providerId: "openai",
            modelId: "gpt-4",
            parameters: {
              temperature: 0.7,
              maxTokens: 2000
            }
          },
          isConfigured: true,
          description: "AI model for content generation"
        }
      },
      {
        id: "router",
        type: "centralRouter",
        position: { x: 1250, y: 200 },
        data: {
          label: "Content Router",
          config: {
            routingStrategy: "smart",
            maxRetries: 3,
            timeout: 30000
          },
          isConfigured: true,
          description: "Routes content requests intelligently"
        }
      },
      {
        id: "output",
        type: "output",
        position: { x: 1550, y: 200 },
        data: {
          label: "Content Output",
          config: {},
          isConfigured: true,
          description: "Final content delivery"
        }
      }
    ],
    template_edges: [
      { id: "e1", source: "user-request", target: "classifier" },
      { id: "e2", source: "classifier", target: "router", targetHandle: "classifier" },
      { id: "e3", source: "writing-role", target: "ai-provider", targetHandle: "role" },
      { id: "e4", source: "ai-provider", target: "router", targetHandle: "providers" },
      { id: "e5", source: "router", target: "output" }
    ],
    default_settings: {
      maxExecutionTime: 300,
      retryCount: 3,
      enableLogging: true
    },
    is_official: true,
    download_count: 245,
    rating: 4.8,
    tags: ["content", "writing", "blog", "marketing", "ai-assistant"]
  },
  {
    name: "Web Research & Analysis",
    description: "Automated web browsing and research workflow with intelligent data extraction",
    category: "Research & Analysis",
    template_nodes: [
      {
        id: "user-request",
        type: "userRequest",
        position: { x: 50, y: 200 },
        data: {
          label: "Research Query",
          config: {},
          isConfigured: true,
          description: "Research question or topic input"
        }
      },
      {
        id: "classifier",
        type: "classifier",
        position: { x: 350, y: 200 },
        data: {
          label: "Query Classifier",
          config: {},
          isConfigured: true,
          description: "Analyzes research requirements"
        }
      },
      {
        id: "planner",
        type: "planner",
        position: { x: 650, y: 100 },
        data: {
          label: "Research Planner",
          config: {
            providerId: "google",
            modelId: "gemini-pro",
            parameters: {
              temperature: 0.3,
              maxTokens: 1000
            },
            maxSubtasks: 8
          },
          isConfigured: true,
          description: "Plans research strategy"
        }
      },
      {
        id: "memory",
        type: "memory",
        position: { x: 650, y: 300 },
        data: {
          label: "Research Memory",
          config: {},
          isConfigured: true,
          description: "Stores research progress"
        }
      },
      {
        id: "browsing",
        type: "browsing",
        position: { x: 950, y: 200 },
        data: {
          label: "Web Browser",
          config: {},
          isConfigured: true,
          description: "Automated web browsing"
        }
      },
      {
        id: "router",
        type: "centralRouter",
        position: { x: 1250, y: 200 },
        data: {
          label: "Research Router",
          config: {
            routingStrategy: "smart",
            maxRetries: 3,
            timeout: 60000
          },
          isConfigured: true,
          description: "Coordinates research workflow"
        }
      },
      {
        id: "output",
        type: "output",
        position: { x: 1550, y: 200 },
        data: {
          label: "Research Report",
          config: {},
          isConfigured: true,
          description: "Compiled research results"
        }
      }
    ],
    template_edges: [
      { id: "e1", source: "user-request", target: "classifier" },
      { id: "e2", source: "classifier", target: "router", targetHandle: "classifier" },
      { id: "e3", source: "planner", target: "browsing", targetHandle: "planner" },
      { id: "e4", source: "memory", target: "browsing", targetHandle: "memory" },
      { id: "e5", source: "browsing", target: "router", targetHandle: "providers" },
      { id: "e6", source: "memory", target: "router", targetHandle: "memory" },
      { id: "e7", source: "router", target: "output" }
    ],
    default_settings: {
      maxExecutionTime: 600,
      retryCount: 5,
      enableLogging: true
    },
    is_official: true,
    download_count: 189,
    rating: 4.6,
    tags: ["research", "web-browsing", "analysis", "automation", "data-extraction"]
  },
  {
    name: "Multi-Role Collaboration",
    description: "Coordinate multiple AI roles for complex tasks requiring different expertise",
    category: "AI Workflows",
    template_nodes: [
      {
        id: "user-request",
        type: "userRequest",
        position: { x: 50, y: 300 },
        data: {
          label: "Complex Task",
          config: {},
          isConfigured: true,
          description: "Multi-faceted task input"
        }
      },
      {
        id: "classifier",
        type: "classifier",
        position: { x: 350, y: 300 },
        data: {
          label: "Task Classifier",
          config: {},
          isConfigured: true,
          description: "Identifies required roles"
        }
      },
      {
        id: "analyst-role",
        type: "roleAgent",
        position: { x: 650, y: 150 },
        data: {
          label: "Business Analyst",
          config: {
            roleId: "business_analyst",
            roleName: "Business Analyst",
            roleType: "predefined"
          },
          isConfigured: true,
          description: "Analysis and strategy role"
        }
      },
      {
        id: "developer-role",
        type: "roleAgent",
        position: { x: 650, y: 300 },
        data: {
          label: "Developer",
          config: {
            roleId: "software_developer",
            roleName: "Software Developer",
            roleType: "predefined"
          },
          isConfigured: true,
          description: "Technical implementation role"
        }
      },
      {
        id: "writer-role",
        type: "roleAgent",
        position: { x: 650, y: 450 },
        data: {
          label: "Technical Writer",
          config: {
            roleId: "technical_writer",
            roleName: "Technical Writer",
            roleType: "predefined"
          },
          isConfigured: true,
          description: "Documentation role"
        }
      },
      {
        id: "ai-provider-1",
        type: "provider",
        position: { x: 950, y: 200 },
        data: {
          label: "Primary AI",
          config: {
            providerId: "anthropic",
            modelId: "claude-3-sonnet",
            parameters: {
              temperature: 0.5,
              maxTokens: 3000
            }
          },
          isConfigured: true,
          description: "Main AI provider"
        }
      },
      {
        id: "ai-provider-2",
        type: "provider",
        position: { x: 950, y: 400 },
        data: {
          label: "Secondary AI",
          config: {
            providerId: "openai",
            modelId: "gpt-4",
            parameters: {
              temperature: 0.3,
              maxTokens: 2000
            }
          },
          isConfigured: true,
          description: "Secondary AI provider"
        }
      },
      {
        id: "router",
        type: "centralRouter",
        position: { x: 1250, y: 300 },
        data: {
          label: "Collaboration Router",
          config: {
            routingStrategy: "smart",
            maxRetries: 3,
            timeout: 45000
          },
          isConfigured: true,
          description: "Orchestrates multi-role collaboration"
        }
      },
      {
        id: "output",
        type: "output",
        position: { x: 1550, y: 300 },
        data: {
          label: "Collaborative Output",
          config: {},
          isConfigured: true,
          description: "Synthesized results from all roles"
        }
      }
    ],
    template_edges: [
      { id: "e1", source: "user-request", target: "classifier" },
      { id: "e2", source: "classifier", target: "router", targetHandle: "classifier" },
      { id: "e3", source: "analyst-role", target: "ai-provider-1", targetHandle: "role" },
      { id: "e4", source: "developer-role", target: "ai-provider-1", targetHandle: "role" },
      { id: "e5", source: "writer-role", target: "ai-provider-2", targetHandle: "role" },
      { id: "e6", source: "ai-provider-1", target: "router", targetHandle: "providers" },
      { id: "e7", source: "ai-provider-2", target: "router", targetHandle: "providers" },
      { id: "e8", source: "router", target: "output" }
    ],
    default_settings: {
      maxExecutionTime: 900,
      retryCount: 3,
      enableLogging: true
    },
    is_official: true,
    download_count: 156,
    rating: 4.9,
    tags: ["collaboration", "multi-role", "complex-tasks", "orchestration", "ai-teams"]
  }
];

/**
 * Function to seed default templates into the database
 */
export async function seedDefaultTemplates() {
  // This would be called during database setup
  console.log('Default templates ready for seeding:', defaultTemplates.length);
  return defaultTemplates;
}
