/**
 * Database setup script for Memory functionality
 * Run this to create the workflow_memory table in Supabase
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

async function setupMemoryDatabase() {
  console.log('🚀 Setting up Memory database...');
  
  const supabase = createClient(supabaseUrl, supabaseServiceKey);
  
  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, '../lib/memory/memory-migration.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Executing migration SQL...');
    
    // Execute the migration
    const { error } = await supabase.rpc('exec_sql', { sql: migrationSQL });
    
    if (error) {
      console.error('❌ Migration failed:', error);
      process.exit(1);
    }
    
    console.log('✅ Memory database setup complete!');
    
    // Test the table by inserting a sample record
    console.log('🧪 Testing memory table...');
    
    const testData = {
      memory_name: 'test_memory',
      user_id: '00000000-0000-0000-0000-000000000000', // Placeholder UUID
      workflow_id: 'test_workflow',
      node_id: 'test_node',
      data_type: 'general',
      data: { test: 'data' },
      metadata: {
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        size_kb: 0.1,
        encrypted: false
      }
    };
    
    const { error: insertError } = await supabase
      .from('workflow_memory')
      .insert(testData);
    
    if (insertError) {
      console.error('❌ Test insert failed:', insertError);
    } else {
      console.log('✅ Test insert successful!');
      
      // Clean up test data
      await supabase
        .from('workflow_memory')
        .delete()
        .eq('memory_name', 'test_memory')
        .eq('workflow_id', 'test_workflow');
      
      console.log('🧹 Test data cleaned up');
    }
    
    console.log('\n🎉 Memory system is ready for production!');
    console.log('\nNext steps:');
    console.log('1. Memory nodes can now store and retrieve data');
    console.log('2. Browsing nodes will automatically save context');
    console.log('3. Router nodes will learn from routing decisions');
    console.log('4. All memory is encrypted and user-scoped');
    
  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }
}

// Run the setup
setupMemoryDatabase();
