export interface BrowsingModel {
  id: string;
  provider: string;
  model: string;
  api_key: string;
  temperature?: number;
  order: number; // For fallback ordering
}

export interface CustomApiConfig {
  id: string;
  name: string;
  created_at: string; // Assuming ISO 8601 timestamp string
  browsing_enabled?: boolean;
  browsing_models?: BrowsingModel[];
  // user_id?: string; // Optional: if you plan to associate configs with users directly
}