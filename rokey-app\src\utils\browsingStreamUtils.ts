// Browsing Stream Utils - Disabled Stub for Launch
// This is a stub file to prevent build errors while browsing is disabled

export function createBrowsingProgressSSECallback(controller: any) {
  // Return a no-op callback since browsing is disabled
  return {
    onPlanCreated: () => {},
    onSubtaskStarted: () => {},
    onSubtaskCompleted: () => {},
    onSubtaskFailed: () => {},
    onPlanCompleted: () => {},
    onPlanFailed: () => {}
  };
}
