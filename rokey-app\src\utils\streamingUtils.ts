// Streaming utilities for first token tracking, performance monitoring, and cost calculation

// Interface for streaming usage data
export interface StreamingUsageData {
  promptTokens: number;
  completionTokens: number;
  totalTokens: number;
  cost?: number; // For providers like OpenRouter that provide direct cost
}

// Interface for streaming cost callback
export interface StreamingCostCallback {
  (usageData: StreamingUsageData): void;
}

export function createFirstTokenTrackingStream(
  originalStream: ReadableStream,
  provider: string,
  model: string,
  onUsageExtracted?: StreamingCostCallback
): ReadableStream {
  const reader = originalStream.getReader();
  const decoder = new TextDecoder();
  const encoder = new TextEncoder();

  return new ReadableStream({
    async start(controller) {
      let firstTokenSent = false;
      let accumulatedContent = '';
      const streamStartTime = Date.now();

      try {
        while (true) {
          const { done, value } = await reader.read();

          if (done) {
            // Final opportunity to extract usage data if not found in chunks
            if (onUsageExtracted && !firstTokenSent) {
              // Fallback: estimate tokens from accumulated content
              const estimatedUsage = estimateUsageFromContent(accumulatedContent, provider);
              console.log(`💰 [${provider} Cost] Fallback token estimation: ${estimatedUsage.totalTokens} tokens`);
              onUsageExtracted(estimatedUsage);
            }
            controller.close();
            break;
          }

          const chunk = decoder.decode(value, { stream: true });

          // Check if this chunk contains actual content (first token)
          if (!firstTokenSent && chunk.includes('delta')) {
            try {
              // Parse SSE data to check for content
              const lines = chunk.split('\n');
              for (const line of lines) {
                if (line.startsWith('data: ') && !line.includes('[DONE]')) {
                  const jsonData = line.substring(6);
                  try {
                    const parsed = JSON.parse(jsonData);
                    if (parsed.choices?.[0]?.delta?.content) {
                      const firstTokenTime = Date.now() - streamStartTime;
                      console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model})`);
                      firstTokenSent = true;

                      // Accumulate content for fallback estimation
                      accumulatedContent += parsed.choices[0].delta.content;
                      break;
                    }
                  } catch (e) {
                    // Ignore JSON parse errors for individual chunks
                  }
                }
              }
            } catch (e) {
              // Ignore parsing errors, just track timing
              if (!firstTokenSent) {
                const firstTokenTime = Date.now() - streamStartTime;
                console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model}) [fallback detection]`);
                firstTokenSent = true;
              }
            }
          }

          // Extract usage data from streaming chunks
          if (onUsageExtracted && chunk.includes('usage')) {
            try {
              const usageData = extractUsageFromStreamChunk(chunk, provider);
              if (usageData) {
                console.log(`💰 [${provider} Cost] Usage data extracted from stream: ${usageData.totalTokens} tokens`);
                onUsageExtracted(usageData);
              }
            } catch (e) {
              // Ignore usage extraction errors
            }
          }

          // Continue accumulating content for fallback
          if (chunk.includes('delta')) {
            try {
              const lines = chunk.split('\n');
              for (const line of lines) {
                if (line.startsWith('data: ') && !line.includes('[DONE]')) {
                  const jsonData = line.substring(6);
                  try {
                    const parsed = JSON.parse(jsonData);
                    if (parsed.choices?.[0]?.delta?.content) {
                      accumulatedContent += parsed.choices[0].delta.content;
                    }
                  } catch (e) {
                    // Ignore JSON parse errors
                  }
                }
              }
            } catch (e) {
              // Ignore parsing errors
            }
          }

          // Forward the chunk unchanged
          controller.enqueue(value);
        }
      } catch (error) {
        console.error(`[${provider} Stream Tracking] Error:`, error);
        // Phase 1 Optimization: Graceful error handling for connection resets
        if (error instanceof Error && (error.message.includes('aborted') || error.message.includes('ECONNRESET'))) {
          console.log(`[${provider} Stream] Connection reset detected - closing stream gracefully`);
          controller.close();
        } else {
          controller.error(error);
        }
      }
    }
  });
}

// Enhanced logging for streaming performance
export function logStreamingPerformance(
  provider: string,
  model: string,
  metrics: {
    timeToFirstToken?: number;
    totalStreamTime?: number;
    totalTokens?: number;
    averageTokenLatency?: number;
  }
) {
  console.log(`📊 STREAMING PERFORMANCE: ${provider}/${model}`);
  
  if (metrics.timeToFirstToken !== undefined) {
    console.log(`   ⏱️ Time to First Token: ${metrics.timeToFirstToken.toFixed(1)}ms`);
    
    // Performance categories
    if (metrics.timeToFirstToken < 500) {
      console.log(`   ⚡ EXCELLENT first token performance`);
    } else if (metrics.timeToFirstToken < 1000) {
      console.log(`   ✅ GOOD first token performance`);
    } else if (metrics.timeToFirstToken < 2000) {
      console.log(`   ⚠️ SLOW first token performance`);
    } else {
      console.log(`   🐌 VERY SLOW first token performance`);
    }
  }
  
  if (metrics.totalStreamTime !== undefined) {
    console.log(`   🔄 Total Stream Time: ${metrics.totalStreamTime.toFixed(1)}ms`);
  }
  
  if (metrics.totalTokens !== undefined) {
    console.log(`   🎯 Total Tokens: ${metrics.totalTokens}`);
  }
  
  if (metrics.averageTokenLatency !== undefined) {
    console.log(`   📈 Avg Token Latency: ${metrics.averageTokenLatency.toFixed(1)}ms/token`);
  }
}

// Utility to extract provider and model from request context
export function getProviderModelFromContext(
  providerName: string | null,
  modelId: string | null
): { provider: string; model: string } {
  return {
    provider: providerName || 'unknown',
    model: modelId || 'unknown'
  };
}

// Extract usage data from streaming chunk
export function extractUsageFromStreamChunk(chunk: string, provider: string): StreamingUsageData | null {
  try {
    const lines = chunk.split('\n');
    for (const line of lines) {
      if (line.startsWith('data: ') && !line.includes('[DONE]')) {
        const jsonData = line.substring(6);
        try {
          const parsed = JSON.parse(jsonData);

          // Check for usage data in the chunk
          if (parsed.usage) {
            const usage = parsed.usage;

            // Handle different provider formats
            let promptTokens = 0;
            let completionTokens = 0;
            let totalTokens = 0;
            let cost: number | undefined;

            if (provider.toLowerCase() === 'openrouter') {
              // OpenRouter format
              promptTokens = usage.prompt_tokens || 0;
              completionTokens = usage.completion_tokens || 0;
              totalTokens = usage.total_tokens || (promptTokens + completionTokens);
              cost = usage.cost ? usage.cost * 0.000001 : undefined; // Convert credits to USD
            } else {
              // Standard OpenAI format (Google, Anthropic, xAI)
              promptTokens = usage.prompt_tokens || usage.input_tokens || 0;
              completionTokens = usage.completion_tokens || usage.output_tokens || 0;
              totalTokens = usage.total_tokens || (promptTokens + completionTokens);
            }

            if (totalTokens > 0) {
              return {
                promptTokens,
                completionTokens,
                totalTokens,
                cost
              };
            }
          }
        } catch (e) {
          // Ignore JSON parse errors for individual chunks
        }
      }
    }
  } catch (e) {
    // Ignore parsing errors
  }

  return null;
}

// Estimate usage from accumulated content (fallback)
export function estimateUsageFromContent(content: string, provider: string, promptText?: string): StreamingUsageData {
  // More accurate token estimation based on provider
  let tokensPerChar = 0.25; // Default: 4 chars per token

  // Provider-specific token ratios (based on empirical data)
  switch (provider.toLowerCase()) {
    case 'openrouter':
    case 'openai':
      tokensPerChar = 0.25; // ~4 chars per token
      break;
    case 'google':
      tokensPerChar = 0.22; // ~4.5 chars per token (slightly more efficient)
      break;
    case 'anthropic':
      tokensPerChar = 0.26; // ~3.8 chars per token
      break;
    case 'xai':
      tokensPerChar = 0.25; // Similar to OpenAI
      break;
  }

  const completionTokens = Math.ceil(content.length * tokensPerChar);
  const promptTokens = promptText ? Math.ceil(promptText.length * tokensPerChar) : Math.ceil(completionTokens * 0.3); // Estimate 30% of completion
  const totalTokens = promptTokens + completionTokens;

  return {
    promptTokens,
    completionTokens,
    totalTokens
  };
}

// Simple token counter for rough estimation (legacy function)
export function estimateTokenCount(text: string): number {
  // Rough estimation: 1 token ≈ 4 characters for English text
  // This is a simplified approach, real tokenization would be more accurate
  return Math.ceil(text.length / 4);
}

// Performance thresholds for different providers
export const PERFORMANCE_THRESHOLDS = {
  EXCELLENT_FIRST_TOKEN: 500,   // < 500ms
  GOOD_FIRST_TOKEN: 1000,       // < 1000ms
  SLOW_FIRST_TOKEN: 2000,       // < 2000ms
  // Anything above 2000ms is considered very slow
  
  EXCELLENT_TOTAL: 3000,        // < 3s total
  GOOD_TOTAL: 5000,            // < 5s total
  SLOW_TOTAL: 10000,           // < 10s total
  
  TARGET_TOKEN_LATENCY: 50,     // < 50ms per token after first
} as const;

// Check if performance meets targets
export function evaluatePerformance(metrics: {
  timeToFirstToken?: number;
  totalStreamTime?: number;
  averageTokenLatency?: number;
}): {
  firstTokenGrade: 'excellent' | 'good' | 'slow' | 'very_slow';
  totalTimeGrade: 'excellent' | 'good' | 'slow' | 'very_slow';
  tokenLatencyGrade: 'excellent' | 'good' | 'slow' | 'very_slow';
  overallGrade: 'excellent' | 'good' | 'slow' | 'very_slow';
} {
  const firstTokenGrade = 
    !metrics.timeToFirstToken ? 'very_slow' :
    metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN ? 'excellent' :
    metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN ? 'good' :
    metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN ? 'slow' : 'very_slow';
  
  const totalTimeGrade = 
    !metrics.totalStreamTime ? 'very_slow' :
    metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.EXCELLENT_TOTAL ? 'excellent' :
    metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.GOOD_TOTAL ? 'good' :
    metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.SLOW_TOTAL ? 'slow' : 'very_slow';
  
  const tokenLatencyGrade = 
    !metrics.averageTokenLatency ? 'very_slow' :
    metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY ? 'excellent' :
    metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 2 ? 'good' :
    metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 4 ? 'slow' : 'very_slow';
  
  // Overall grade is the worst of the three
  const grades = [firstTokenGrade, totalTimeGrade, tokenLatencyGrade];
  const gradeOrder = ['excellent', 'good', 'slow', 'very_slow'];
  const overallGrade = grades.reduce((worst, current) => {
    return gradeOrder.indexOf(current) > gradeOrder.indexOf(worst) ? current : worst;
  }, 'excellent') as 'excellent' | 'good' | 'slow' | 'very_slow';
  
  return {
    firstTokenGrade,
    totalTimeGrade,
    tokenLatencyGrade,
    overallGrade
  };
}
