-- Manual Build Workflows Migration
-- This migration adds support for visual workflow building in RouKey

-- 1. Create manual_build_workflows table
CREATE TABLE IF NOT EXISTS public.manual_build_workflows (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    
    -- Visual workflow definition
    nodes JSONB NOT NULL DEFAULT '[]'::jsonb,
    edges JSONB NOT NULL DEFAULT '[]'::jsonb,
    
    -- Workflow settings
    settings JSONB NOT NULL DEFAULT '{
        "maxExecutionTime": 300,
        "retryCount": 3,
        "memoryEnabled": true,
        "streamingEnabled": true
    }'::jsonb,
    
    -- Integration with existing RouKey
    custom_api_config_id UUID REFERENCES public.custom_api_configs(id) ON DELETE CASCADE,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT TRUE,
    is_template BOOLEAN DEFAULT FALSE,
    template_category TEXT,
    
    -- Versioning
    version INTEGER DEFAULT 1,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    
    -- Constraints
    CONSTRAINT valid_nodes CHECK (jsonb_typeof(nodes) = 'array'),
    CONSTRAINT valid_edges CHECK (jsonb_typeof(edges) = 'array'),
    CONSTRAINT valid_settings CHECK (jsonb_typeof(settings) = 'object'),
    CONSTRAINT valid_name_length CHECK (char_length(name) >= 1 AND char_length(name) <= 100)
);

-- 2. Create manual_build_executions table
CREATE TABLE IF NOT EXISTS public.manual_build_executions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    workflow_id UUID REFERENCES public.manual_build_workflows(id) ON DELETE CASCADE NOT NULL,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    
    -- Execution data
    input_data JSONB NOT NULL,
    output_data JSONB,
    execution_logs JSONB DEFAULT '[]'::jsonb,
    
    -- Status tracking
    status TEXT NOT NULL CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    current_node_id TEXT,
    
    -- Performance metrics
    execution_time_ms INTEGER,
    total_tokens_used INTEGER,
    total_cost_usd DECIMAL(10,8),
    
    -- Error tracking
    error_message TEXT,
    error_node_id TEXT,
    
    -- Timestamps
    started_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    completed_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL
);

-- 3. Create manual_build_templates table
CREATE TABLE IF NOT EXISTS public.manual_build_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    category TEXT NOT NULL,
    
    -- Template data
    template_nodes JSONB NOT NULL,
    template_edges JSONB NOT NULL,
    default_settings JSONB NOT NULL,
    
    -- Metadata
    is_official BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES auth.users(id),
    download_count INTEGER DEFAULT 0,
    rating DECIMAL(3,2) DEFAULT 0.0,
    
    -- Tags for discovery
    tags TEXT[] DEFAULT '{}',
    
    -- Preview image
    preview_image_url TEXT,
    
    created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
    
    -- Constraints
    CONSTRAINT valid_template_nodes CHECK (jsonb_typeof(template_nodes) = 'array'),
    CONSTRAINT valid_template_edges CHECK (jsonb_typeof(template_edges) = 'array'),
    CONSTRAINT valid_rating CHECK (rating >= 0.0 AND rating <= 5.0),
    CONSTRAINT valid_category_length CHECK (char_length(category) >= 1 AND char_length(category) <= 50)
);

-- 4. Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_manual_build_workflows_user_id ON public.manual_build_workflows(user_id);
CREATE INDEX IF NOT EXISTS idx_manual_build_workflows_config_id ON public.manual_build_workflows(custom_api_config_id);
CREATE INDEX IF NOT EXISTS idx_manual_build_workflows_active ON public.manual_build_workflows(is_active) WHERE is_active = true;
CREATE INDEX IF NOT EXISTS idx_manual_build_workflows_template ON public.manual_build_workflows(is_template) WHERE is_template = true;

CREATE INDEX IF NOT EXISTS idx_manual_build_executions_workflow_id ON public.manual_build_executions(workflow_id);
CREATE INDEX IF NOT EXISTS idx_manual_build_executions_user_id ON public.manual_build_executions(user_id);
CREATE INDEX IF NOT EXISTS idx_manual_build_executions_status ON public.manual_build_executions(status);
CREATE INDEX IF NOT EXISTS idx_manual_build_executions_created_at ON public.manual_build_executions(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_manual_build_templates_category ON public.manual_build_templates(category);
CREATE INDEX IF NOT EXISTS idx_manual_build_templates_official ON public.manual_build_templates(is_official) WHERE is_official = true;
CREATE INDEX IF NOT EXISTS idx_manual_build_templates_rating ON public.manual_build_templates(rating DESC);

-- 5. Enable Row Level Security
ALTER TABLE public.manual_build_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.manual_build_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.manual_build_templates ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS Policies

-- Workflows: Users can only access their own workflows
CREATE POLICY "Users can manage their own workflows" ON public.manual_build_workflows
    FOR ALL USING (auth.uid() = user_id);

-- Executions: Users can only access their own executions
CREATE POLICY "Users can manage their own executions" ON public.manual_build_executions
    FOR ALL USING (auth.uid() = user_id);

-- Templates: Public read access, creators can manage their own
CREATE POLICY "Templates are publicly readable" ON public.manual_build_templates
    FOR SELECT USING (true);

CREATE POLICY "Users can manage their own templates" ON public.manual_build_templates
    FOR INSERT WITH CHECK (auth.uid() = created_by);

CREATE POLICY "Users can update their own templates" ON public.manual_build_templates
    FOR UPDATE USING (auth.uid() = created_by);

CREATE POLICY "Users can delete their own templates" ON public.manual_build_templates
    FOR DELETE USING (auth.uid() = created_by);

-- 7. Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 8. Create triggers for updated_at
CREATE TRIGGER update_manual_build_workflows_updated_at 
    BEFORE UPDATE ON public.manual_build_workflows 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_manual_build_templates_updated_at 
    BEFORE UPDATE ON public.manual_build_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 9. Insert default templates
INSERT INTO public.manual_build_templates (name, description, category, template_nodes, template_edges, default_settings, is_official, tags) VALUES
(
    'Simple Chat Workflow',
    'Basic workflow with user input, classification, and AI response',
    'Basic',
    '[
        {"id": "user-request", "type": "userRequest", "position": {"x": 100, "y": 100}, "data": {"label": "User Request", "config": {}, "isConfigured": true}},
        {"id": "classifier", "type": "classifier", "position": {"x": 300, "y": 100}, "data": {"label": "Classifier", "config": {}, "isConfigured": true}},
        {"id": "provider", "type": "provider", "position": {"x": 500, "y": 100}, "data": {"label": "AI Provider", "config": {"providerId": "openai", "modelId": "gpt-4"}, "isConfigured": false}},
        {"id": "output", "type": "output", "position": {"x": 700, "y": 100}, "data": {"label": "Output", "config": {}, "isConfigured": true}}
    ]',
    '[
        {"id": "e1", "source": "user-request", "target": "classifier"},
        {"id": "e2", "source": "classifier", "target": "provider"},
        {"id": "e3", "source": "provider", "target": "output"}
    ]',
    '{"maxExecutionTime": 300, "retryCount": 3, "memoryEnabled": true, "streamingEnabled": true}',
    true,
    ARRAY['basic', 'chat', 'simple']
),
(
    'Multi-Provider Consensus',
    'Compare responses from multiple AI providers for better quality',
    'Advanced',
    '[
        {"id": "user-request", "type": "userRequest", "position": {"x": 100, "y": 200}, "data": {"label": "User Request", "config": {}, "isConfigured": true}},
        {"id": "classifier", "type": "classifier", "position": {"x": 300, "y": 200}, "data": {"label": "Classifier", "config": {}, "isConfigured": true}},
        {"id": "provider1", "type": "provider", "position": {"x": 500, "y": 100}, "data": {"label": "OpenAI GPT-4", "config": {"providerId": "openai", "modelId": "gpt-4"}, "isConfigured": false}},
        {"id": "provider2", "type": "provider", "position": {"x": 500, "y": 200}, "data": {"label": "Claude Opus", "config": {"providerId": "anthropic", "modelId": "claude-3-opus"}, "isConfigured": false}},
        {"id": "provider3", "type": "provider", "position": {"x": 500, "y": 300}, "data": {"label": "Gemini Pro", "config": {"providerId": "google", "modelId": "gemini-pro"}, "isConfigured": false}},
        {"id": "merge", "type": "merge", "position": {"x": 700, "y": 200}, "data": {"label": "Merge Results", "config": {}, "isConfigured": true}},
        {"id": "output", "type": "output", "position": {"x": 900, "y": 200}, "data": {"label": "Output", "config": {}, "isConfigured": true}}
    ]',
    '[
        {"id": "e1", "source": "user-request", "target": "classifier"},
        {"id": "e2", "source": "classifier", "target": "provider1"},
        {"id": "e3", "source": "classifier", "target": "provider2"},
        {"id": "e4", "source": "classifier", "target": "provider3"},
        {"id": "e5", "source": "provider1", "target": "merge"},
        {"id": "e6", "source": "provider2", "target": "merge"},
        {"id": "e7", "source": "provider3", "target": "merge"},
        {"id": "e8", "source": "merge", "target": "output"}
    ]',
    '{"maxExecutionTime": 600, "retryCount": 3, "memoryEnabled": true, "streamingEnabled": false}',
    true,
    ARRAY['advanced', 'consensus', 'multi-provider']
);

-- 10. Grant necessary permissions
GRANT ALL ON public.manual_build_workflows TO authenticated;
GRANT ALL ON public.manual_build_executions TO authenticated;
GRANT ALL ON public.manual_build_templates TO authenticated;

-- Migration completed successfully
