// Test to verify browsing functionality is properly disabled
// This test ensures browsing detection always returns false

const fetch = require('node-fetch');

const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  configId: 'b39270c0-feb8-4c99-b6d2-9ee224edd57e', // Your actual config ID
  userId: '69d967d5-0b7b-402b-ae1b-711d9b74eef4',
  apiKey: 'Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13'
};

class BrowsingDisabledTest {
  constructor() {
    this.baseUrl = TEST_CONFIG.baseUrl;
    this.configId = TEST_CONFIG.configId;
    this.userId = TEST_CONFIG.userId;
    this.apiKey = TEST_CONFIG.apiKey;
  }

  async runTests() {
    console.log('🚫 BROWSING DISABLED TEST - Verifying browsing is properly disabled');
    console.log('=' .repeat(80));
    console.log(`🔧 Config ID: ${this.configId}`);
    console.log(`👤 User ID: ${this.userId}`);
    console.log('=' .repeat(80));

    const browsingQueries = [
      "when does the earliest flight from Owerri to Abuja leave today?",
      "what's the current weather in Lagos?",
      "latest news about Bitcoin price today",
      "current stock price of Apple",
      "what's happening in the world today?",
      "find me the latest information about AI developments"
    ];

    let allTestsPassed = true;

    for (let i = 0; i < browsingQueries.length; i++) {
      const query = browsingQueries[i];
      console.log(`\n📝 TEST ${i + 1}: "${query}"`);
      console.log('-'.repeat(50));

      try {
        const result = await this.testQuery(query);
        
        if (result.success) {
          // Check if browsing was triggered (it shouldn't be)
          const browsingTriggered = this.checkForBrowsingIndicators(result.response);
          
          if (browsingTriggered) {
            console.log('❌ FAIL: Browsing was triggered when it should be disabled');
            console.log(`   Indicators found: ${browsingTriggered.indicators.join(', ')}`);
            allTestsPassed = false;
          } else {
            console.log('✅ PASS: Browsing was not triggered (correctly disabled)');
            console.log(`   Response type: ${result.responseType}`);
            console.log(`   Response length: ${result.responseLength} characters`);
          }
        } else {
          console.log(`❌ FAIL: API call failed - ${result.error}`);
          allTestsPassed = false;
        }

      } catch (error) {
        console.log(`❌ ERROR: ${error.message}`);
        allTestsPassed = false;
      }

      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    console.log('\n' + '=' .repeat(80));
    if (allTestsPassed) {
      console.log('🎉 ALL TESTS PASSED: Browsing is properly disabled!');
      console.log('✅ Core routing functionality works without browsing');
      console.log('✅ No browsing indicators found in responses');
      console.log('✅ API responses are clean and focused on routing');
    } else {
      console.log('❌ SOME TESTS FAILED: Browsing may not be fully disabled');
      console.log('⚠️  Check the logs above for specific issues');
    }
    console.log('=' .repeat(80));

    return allTestsPassed;
  }

  async testQuery(query) {
    try {
      const response = await fetch(`${this.baseUrl}/api/v1/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': this.apiKey,
          'X-Custom-Config-ID': this.configId
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'user',
              content: query
            }
          ],
          stream: false,
          max_tokens: 500
        })
      });

      if (response.ok) {
        const data = await response.json();
        const content = data.choices?.[0]?.message?.content || '';
        
        return {
          success: true,
          response: data,
          responseType: 'AI Response',
          responseLength: content.length,
          content: content
        };
      } else {
        const errorText = await response.text();
        return {
          success: false,
          error: `HTTP ${response.status}: ${errorText}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  checkForBrowsingIndicators(response) {
    const responseText = JSON.stringify(response).toLowerCase();
    const content = response.choices?.[0]?.message?.content?.toLowerCase() || '';
    
    const browsingIndicators = [
      'browsing',
      'searching the web',
      'web search',
      'browsing plan',
      'subtask',
      'visiting website',
      'scraping',
      'browserless',
      'browsing progress',
      'web automation',
      'site navigation'
    ];

    const foundIndicators = browsingIndicators.filter(indicator => 
      responseText.includes(indicator) || content.includes(indicator)
    );

    if (foundIndicators.length > 0) {
      return {
        triggered: true,
        indicators: foundIndicators
      };
    }

    return false;
  }
}

// Run the test
if (require.main === module) {
  console.log('🚫 STARTING BROWSING DISABLED TEST');
  console.log('This test verifies that browsing functionality is properly disabled\n');
  
  const test = new BrowsingDisabledTest();
  test.runTests()
    .then((allPassed) => {
      if (allPassed) {
        console.log('\n🎯 CONCLUSION: Ready for launch!');
        console.log('   ✅ Browsing is properly disabled');
        console.log('   ✅ Core routing works perfectly');
        console.log('   ✅ Clean user experience without browsing bugs');
        process.exit(0);
      } else {
        console.log('\n⚠️  CONCLUSION: Issues found!');
        console.log('   ❌ Browsing may not be fully disabled');
        console.log('   🔧 Check the implementation and try again');
        process.exit(1);
      }
    })
    .catch((error) => {
      console.error('\n💥 Test failed with error:', error);
      process.exit(1);
    });
}

module.exports = { BrowsingDisabledTest };
