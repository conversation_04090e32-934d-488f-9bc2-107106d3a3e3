// Simple test script to verify browsing functionality
const { BrowserlessService } = require('./src/lib/browserless.ts');

async function testBrowsing() {
  console.log('🧪 Testing Browserless functionality...');
  
  try {
    const browserless = BrowserlessService.getInstance();
    
    console.log('📊 Service stats:', browserless.getStats());
    
    // Test simple session creation
    console.log('🔄 Testing session creation...');
    const session = await browserless.createBrowsingSession('https://www.google.com', {
      timeout: 30000,
      humanLike: false
    });
    
    console.log('✅ Session created successfully:', session);
    
    // Test simple search
    console.log('🔍 Testing simple search...');
    const searchResult = await browserless.searchAndExtractUnblocked('test query');
    
    console.log('✅ Search completed successfully');
    console.log('📊 Final stats:', browserless.getStats());
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack
    });
  }
}

testBrowsing();
