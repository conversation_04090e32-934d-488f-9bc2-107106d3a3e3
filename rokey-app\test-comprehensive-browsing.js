// Comprehensive Browsing Test Script
// Tests the enhanced CAPTCHA handling, popup management, and dynamic site discovery

async function testComprehensiveBrowsing() {
  console.log('🚀 Testing Comprehensive BrowserQL Enhancements\n');
  
  const apiKey = process.env.BROWSERLESS_API_KEY || '2ScDdfCuVNKkYC3f324cbad0915d0c5128dc1805683dcf963';
  const baseUrl = 'https://production-sfo.browserless.io/chromium/bql';

  // Test 1: Dynamic CAPTCHA Detection and Solving
  console.log('📍 Test 1: Dynamic CAPTCHA Detection and Solving');
  console.log('='.repeat(60));
  
  try {
    const captchaTestScript = `
      mutation DynamicCaptchaTest {
        # Navigate to Google reCAPTCHA demo
        navigation: goto(url: "https://www.google.com/recaptcha/api2/demo", waitUntil: networkIdle) {
          status
          time
        }

        # Wait for page load
        waitForLoad: waitForTimeout(time: 3000) {
          time
        }

        # Check for Cloudflare Turnstile and solve if present
        handleCloudflare: if(selector: ".cf-turnstile, [data-sitekey], .cf-challenge") {
          verify(type: cloudflare, timeout: 30000) {
            found
            solved
            time
          }
        }

        # Check for reCAPTCHA and solve if present
        handleRecaptcha: if(selector: ".g-recaptcha, [data-sitekey], .recaptcha, #recaptcha") {
          solve(type: recaptcha, timeout: 30000) {
            found
            solved
            time
          }
        }

        # Check for hCAPTCHA and solve if present
        handleHcaptcha: if(selector: ".h-captcha, [data-sitekey*='hcaptcha'], .hcaptcha") {
          solve(type: hcaptcha, timeout: 30000) {
            found
            solved
            time
          }
        }

        # Get final page state
        finalTitle: title {
          title
        }

        pageContent: html {
          html
        }

        # Take screenshot for verification
        screenshot: screenshot(encoding: base64, type: png) {
          data
        }
      }
    `;

    const response = await fetch(`${baseUrl}?token=${apiKey}&humanlike=true&blockConsentModals=true&timeout=60000`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: captchaTestScript })
    });

    const result = await response.json();
    
    console.log('CAPTCHA Test Result:', {
      success: !!result.data,
      cloudflareFound: result.data?.handleCloudflare?.found,
      cloudflareSolved: result.data?.handleCloudflare?.solved,
      recaptchaFound: result.data?.handleRecaptcha?.found,
      recaptchaSolved: result.data?.handleRecaptcha?.solved,
      hcaptchaFound: result.data?.handleHcaptcha?.found,
      hcaptchaSolved: result.data?.handleHcaptcha?.solved,
      finalTitle: result.data?.finalTitle?.title,
      hasScreenshot: !!result.data?.screenshot?.data
    });
    
  } catch (error) {
    console.error('❌ CAPTCHA test failed:', error.message);
  }

  console.log('\n' + '='.repeat(60) + '\n');

  // Test 2: Comprehensive Popup and Consent Handling
  console.log('📍 Test 2: Comprehensive Popup and Consent Handling');
  console.log('='.repeat(60));
  
  try {
    const popupTestScript = `
      mutation ComprehensivePopupTest {
        # Navigate to a news site with potential popups
        navigation: goto(url: "https://www.cnn.com", waitUntil: networkIdle) {
          status
          time
        }

        # Handle cookie consent banners
        handleCookieConsent: if(selector: "button[id*='accept'], button[class*='accept'], button[class*='consent'], .cookie-accept, #cookie-accept, [data-testid*='accept'], [aria-label*='Accept'], .accept-cookies") {
          click(selector: "button[id*='accept'], button[class*='accept'], button[class*='consent'], .cookie-accept, #cookie-accept, [data-testid*='accept'], [aria-label*='Accept'], .accept-cookies", timeout: 5000) {
            time
          }
        }

        # Handle GDPR banners
        handleGDPRBanner: if(selector: "[class*='gdpr'], [id*='gdpr'], [data-testid*='gdpr'], .privacy-banner, .consent-banner") {
          click(selector: "[class*='gdpr'] button, [id*='gdpr'] button, [data-testid*='gdpr'] button, .privacy-banner button, .consent-banner button", timeout: 5000) {
            time
          }
        }

        # Handle modal close buttons
        handleModals: if(selector: ".modal-close, [aria-label='Close'], .close-button, [data-dismiss='modal'], .overlay-close") {
          click(selector: ".modal-close, [aria-label='Close'], .close-button, [data-dismiss='modal'], .overlay-close", timeout: 5000) {
            time
          }
        }

        # Handle newsletter popups
        handleNewsletterPopups: if(selector: "[class*='newsletter'] .close, [class*='popup'] .close, [class*='subscribe'] .close") {
          click(selector: "[class*='newsletter'] .close, [class*='popup'] .close, [class*='subscribe'] .close", timeout: 5000) {
            time
          }
        }

        # Wait after popup handling
        waitAfterPopups: waitForTimeout(time: 3000) {
          time
        }

        # Get final page state
        finalTitle: title {
          title
        }

        finalUrl: url {
          url
        }

        # Take screenshot to verify popup handling
        screenshot: screenshot(encoding: base64, type: png) {
          data
        }
      }
    `;

    const response = await fetch(`${baseUrl}?token=${apiKey}&humanlike=true&blockConsentModals=true&timeout=45000`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: popupTestScript })
    });

    const result = await response.json();
    
    console.log('Popup Handling Result:', {
      success: !!result.data,
      cookieConsentHandled: !!result.data?.handleCookieConsent?.time,
      gdprBannerHandled: !!result.data?.handleGDPRBanner?.time,
      modalsHandled: !!result.data?.handleModals?.time,
      newsletterPopupsHandled: !!result.data?.handleNewsletterPopups?.time,
      finalTitle: result.data?.finalTitle?.title,
      finalUrl: result.data?.finalUrl?.url,
      hasScreenshot: !!result.data?.screenshot?.data
    });
    
  } catch (error) {
    console.error('❌ Popup handling test failed:', error.message);
  }

  console.log('\n' + '='.repeat(60) + '\n');

  // Test 3: Dynamic Site Discovery with Search
  console.log('📍 Test 3: Dynamic Site Discovery with Search');
  console.log('='.repeat(60));
  
  try {
    const searchTestScript = `
      mutation DynamicSiteDiscovery {
        # Search on Google for dynamic site discovery
        googleSearch: goto(url: "https://www.google.com/search?q=${encodeURIComponent('best Nigerian restaurants Lagos 2025')}", waitUntil: networkIdle) {
          status
          time
        }

        # Handle any popups or consent forms
        handleCookieConsent: if(selector: "button[id*='accept'], button[class*='accept'], button[class*='consent'], .cookie-accept, #cookie-accept, [data-testid*='accept']") {
          click(selector: "button[id*='accept'], button[class*='accept'], button[class*='consent'], .cookie-accept, #cookie-accept, [data-testid*='accept']", timeout: 5000) {
            time
          }
        }

        # Check for reCAPTCHA and solve if present
        handleRecaptcha: if(selector: ".g-recaptcha, [data-sitekey], .recaptcha, #recaptcha") {
          solve(type: recaptcha, timeout: 30000) {
            found
            solved
            time
          }
        }

        # Wait for search results to load
        waitForResults: waitForTimeout(time: 3000) {
          time
        }

        # Extract search results
        searchResults: html {
          html
        }

        # Take screenshot of search results
        searchScreenshot: screenshot(encoding: base64, type: png) {
          data
        }
      }
    `;

    const response = await fetch(`${baseUrl}?token=${apiKey}&humanlike=true&blockConsentModals=true&timeout=60000`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ query: searchTestScript })
    });

    const result = await response.json();
    
    console.log('Dynamic Site Discovery Result:', {
      success: !!result.data,
      searchStatus: result.data?.googleSearch?.status,
      cookieConsentHandled: !!result.data?.handleCookieConsent?.time,
      recaptchaFound: result.data?.handleRecaptcha?.found,
      recaptchaSolved: result.data?.handleRecaptcha?.solved,
      hasSearchResults: !!result.data?.searchResults?.html,
      searchResultsLength: result.data?.searchResults?.html?.length || 0,
      hasScreenshot: !!result.data?.searchScreenshot?.data
    });

    // Basic URL extraction from search results
    if (result.data?.searchResults?.html) {
      const html = result.data.searchResults.html;
      const urlMatches = html.match(/href="([^"]+)"/g) || [];
      const extractedUrls = urlMatches
        .map(match => match.replace('href="', '').replace('"', ''))
        .filter(url => url.startsWith('http') && !url.includes('google.com'))
        .slice(0, 5);
      
      console.log('Extracted URLs:', extractedUrls);
    }
    
  } catch (error) {
    console.error('❌ Dynamic site discovery test failed:', error.message);
  }

  console.log('\n🎉 Comprehensive browsing tests completed!');
  console.log('\n📋 Summary of Enhancements:');
  console.log('✅ Dynamic CAPTCHA detection (reCAPTCHA, hCAPTCHA, Cloudflare Turnstile)');
  console.log('✅ Comprehensive popup handling (cookies, GDPR, modals, newsletters)');
  console.log('✅ Conditional execution - only acts when elements are present');
  console.log('✅ Enhanced error recovery and fallback mechanisms');
  console.log('✅ Dynamic site discovery through search result parsing');
  console.log('✅ Multi-step automation workflows with state persistence');
}

// Run the tests
if (require.main === module) {
  testComprehensiveBrowsing().catch(console.error);
}

module.exports = { testComprehensiveBrowsing };
