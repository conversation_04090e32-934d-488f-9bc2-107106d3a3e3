// Test script for the Context-Aware Continuation System
// This script tests the continuation analysis functionality

const testMessages = [
  // Test case 1: Truncated story generation
  {
    name: "Truncated Story",
    messages: [
      {
        role: "user",
        content: "Write a short story about a magical forest"
      },
      {
        role: "assistant", 
        content: "In the heart of the Whispering Woods, where ancient oaks stretched their gnarled branches toward the starlit sky, lived a young fox named <PERSON>. She possessed a gift that set her apart from all other creatures in the forest - the ability to understand the language of the trees.\n\nEvery morning, <PERSON> would venture deeper into the woods, listening to the stories the trees would tell. The old willow by the stream spoke of lovers who had carved their names into her bark centuries ago. The mighty oak at the forest's center shared tales of battles fought beneath his protective canopy.\n\nBut today was different. Today, the trees were whispering about something new, something that made their leaves rustle with excitement and fear. A stranger had entered their domain, someone who..."
      }
    ]
  },
  
  // Test case 2: Complete response
  {
    name: "Complete Response",
    messages: [
      {
        role: "user",
        content: "What is 2 + 2?"
      },
      {
        role: "assistant",
        content: "2 + 2 equals 4. This is a basic arithmetic operation where we add two units to two other units, resulting in a total of four units."
      }
    ]
  },
  
  // Test case 3: Truncated code generation
  {
    name: "Truncated Code",
    messages: [
      {
        role: "user",
        content: "Create a Python function to calculate fibonacci numbers"
      },
      {
        role: "assistant",
        content: "Here's a Python function to calculate Fibonacci numbers:\n\n```python\ndef fibonacci(n):\n    \"\"\"\n    Calculate the nth Fibonacci number using dynamic programming\n    \n    Args:\n        n (int): The position in the Fibonacci sequence\n    \n    Returns:\n        int: The nth Fibonacci number\n    \"\"\"\n    if n <= 0:\n        return 0\n    elif n == 1:\n        return 1\n    \n    # Create array to store Fibonacci numbers\n    fib = [0] * (n + 1)\n    fib[0] = 0\n    fib[1] = 1\n    \n    # Calculate Fibonacci numbers iteratively\n    for i in range(2, n + 1):\n        fib[i] = fib[i-1] + fib[i-2]\n    \n    return fib[n]\n\n# Example usage:\nprint(fibonacci(10))  # Output: 55\nprint(fibonacci(0))   # Output: 0\nprint(fibonacci(1))   # Output: 1\n\n# You can also create a more memory-efficient version:\ndef fibonacci_optimized(n):\n    if n <= 0:\n        return 0\n    elif n == 1:\n        return 1\n    \n    a, b = 0, 1\n    for i in range(2, n + 1):\n        a, b = b, a + b\n    \n    return b\n\n# This function uses O(1) space complexity instead of O(n)\n# Both functions have O(n) time complexity\n\n# For very large numbers, you might want to consider using memoization:\nfrom functools import lru_cache\n\n@lru_cache(maxsize=None)\ndef fibonacci_memoized(n):\n    if n <= 0:\n        return 0\n    elif n == 1:\n        return 1\n    else:\n        return fibonacci_memoized(n-1) + fibonacci_memoized(n-2)\n\n# This recursive approach with memoization is more intuitive\n# but may hit recursion limits for very large n values\n\n# For production use, I'd recommend the iterative approach:\ndef fibonacci_production(n):\n    \"\"\"Production-ready Fibonacci function with input validation\"\"\"\n    if not isinstance(n, int):\n        raise TypeError(\"Input must be an integer\")\n    if n < 0:\n        raise ValueError(\"Input must be non-negative\")\n    \n    if n <= 1:\n        return n\n    \n    a, b = 0, 1\n    for _ in range(2, n + 1):\n        a, b = b, a + b\n    \n    return b\n\n# Additional utility functions:\ndef fibonacci_sequence(count):\n    \"\"\"Generate a sequence of Fibonacci numbers\"\"\"\n    if count <= 0:\n        return []\n    elif count == 1:\n        return [0]\n    elif count == 2:\n        return [0, 1]\n    \n    sequence = [0, 1]\n    for i in range(2, count):\n        sequence.append(sequence[i-1] + sequence[i-2])\n    \n    return sequence\n\ndef is_fibonacci(num):\n    \"\"\"Check if a number is a Fibonacci number\"\"\"\n    if num < 0:\n        return False\n    \n    # A number is Fibonacci if one of (5*n^2 + 4) or (5*n^2 - 4) is a perfect square\n    import math\n    \n    def is_perfect_square(x):\n        s = int(math.sqrt(x))\n        return s * s == x\n    \n    return is_perfect_square(5 * num * num + 4) or is_perfect_square(5 * num * num - 4)\n\n# Performance comparison function:\ndef compare_fibonacci_methods(n):\n    \"\"\"Compare performance of different Fibonacci implementations\"\"\"\n    import time\n    \n    methods = {\n        'iterative': fibonacci,\n        'optimized': fibonacci_optimized,\n        'memoized': fibonacci_memoized,\n        'production': fibonacci_production\n    }\n    \n    results = {}\n    for name, func in methods.items():\n        start_time = time.time()\n        result = func(n)\n        end_time = time.time()\n        results[name] = {\n            'result': result,\n            'time': end_time - start_time\n        }\n    \n    return results\n\n# Example of performance comparison:\nif __name__ == \"__main__\":\n    print(\"Fibonacci Performance Comparison for n=35:\")\n    comparison = compare_fibonacci_methods(35)\n    for method, data in comparison.items():\n        print(f\"{method}: {data['result']} (Time: {data['time']:.6f}s)\")\n```\n\nThis implementation provides multiple approaches to calculating Fibonacci numbers, each with different trade-offs:\n\n1. **Basic iterative approach**: Good balance of readability and performance\n2. **Memory-optimized version**: Uses constant space\n3. **Memoized recursive**: More intuitive but may hit recursion limits\n4. **Production-ready**: Includes input validation and error handling\n\nThe code also includes utility functions for generating sequences and checking if a number is a Fibonacci number. Choose the approach that best fits your specific use case and performance requirements."
      }
    ]
  }
];

// Mock the analyzeContinuationContext function for testing
async function mockAnalyzeContinuationContext(messages, classificationApiKey, availableRoles) {
  const userTask = messages[0];
  const assistantResponse = messages[1];
  
  const userContent = userTask.content;
  const assistantContent = assistantResponse.content;
  
  // Simple heuristic analysis for testing
  const isLikelyTruncated = assistantContent.length > 1000 && (
    !assistantContent.trim().endsWith('.') ||
    !assistantContent.trim().endsWith('!') ||
    !assistantContent.trim().endsWith('?') ||
    assistantContent.includes('...') ||
    assistantContent.endsWith('who...') ||
    assistantContent.includes('```') && (assistantContent.match(/```/g) || []).length % 2 === 1
  );
  
  const isComplete = assistantContent.length < 500 || (
    assistantContent.trim().endsWith('.') &&
    !assistantContent.includes('...') &&
    assistantContent.length < 1000
  );
  
  return {
    needsContinuation: isLikelyTruncated,
    isComplete: isComplete,
    continuationStrategy: isLikelyTruncated ? 'single_role' : 'new_conversation',
    targetRoles: isLikelyTruncated ? ['writing'] : [],
    originalRole: 'writing',
    reasoning: `Analysis: Length=${assistantContent.length}, EndsWithPeriod=${assistantContent.trim().endsWith('.')}, HasEllipsis=${assistantContent.includes('...')}, Truncated=${isLikelyTruncated}`,
    confidence: isLikelyTruncated ? 0.8 : 0.9
  };
}

// Test function
async function testContinuationAnalysis() {
  console.log('Testing Context-Aware Continuation System\n');
  console.log('=' .repeat(50));
  
  const mockRoles = [
    { id: 'writing', name: 'Creative Writing' },
    { id: 'coding', name: 'Code Generation' },
    { id: 'general', name: 'General Assistant' }
  ];
  
  for (const testCase of testMessages) {
    console.log(`\nTest Case: ${testCase.name}`);
    console.log('-'.repeat(30));
    
    const analysis = await mockAnalyzeContinuationContext(
      testCase.messages,
      'mock-api-key',
      mockRoles
    );
    
    console.log(`User Request: "${testCase.messages[0].content}"`);
    console.log(`Response Length: ${testCase.messages[1].content.length} characters`);
    console.log(`Needs Continuation: ${analysis.needsContinuation}`);
    console.log(`Is Complete: ${analysis.isComplete}`);
    console.log(`Strategy: ${analysis.continuationStrategy}`);
    console.log(`Target Roles: ${analysis.targetRoles.join(', ') || 'None'}`);
    console.log(`Confidence: ${analysis.confidence}`);
    console.log(`Reasoning: ${analysis.reasoning}`);
    
    if (analysis.needsContinuation) {
      console.log(`✅ CONTINUATION NEEDED - Would route to ${analysis.targetRoles[0]} role`);
    } else {
      console.log(`❌ NO CONTINUATION - Would treat as new conversation`);
    }
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('Test completed successfully!');
}

// Run the test
testContinuationAnalysis().catch(console.error);
