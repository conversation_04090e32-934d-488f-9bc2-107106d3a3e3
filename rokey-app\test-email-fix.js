/**
 * Test script to verify the email fix works locally
 */

const API_BASE = 'http://localhost:3000';
const API_KEY = 'Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13';

async function testEnvironmentCheck() {
  console.log('🔍 Testing environment variable availability...');
  
  try {
    const response = await fetch(`${API_BASE}/api/debug/env-check`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('✅ Environment check response:', JSON.stringify(data, null, 2));
    
    return data.environment;
  } catch (error) {
    console.error('❌ Environment check failed:', error);
    return null;
  }
}

async function testWelcomeEmail() {
  console.log('📧 Testing welcome email functionality...');
  
  try {
    const response = await fetch(`${API_BASE}/api/email/test-welcome`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        userEmail: '<EMAIL>',
        userName: 'Test User',
        userTier: 'free'
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Welcome email test response:', JSON.stringify(data, null, 2));
    
    return data;
  } catch (error) {
    console.error('❌ Welcome email test failed:', error);
    return null;
  }
}

async function runTests() {
  console.log('🚀 Starting email fix tests...\n');
  
  // Test 1: Check environment variables
  const envData = await testEnvironmentCheck();
  if (!envData) {
    console.log('❌ Environment check failed - cannot proceed with email test');
    return;
  }
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 2: Test welcome email
  const emailResult = await testWelcomeEmail();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 TEST SUMMARY:');
  console.log('Environment Variables:', envData.RESEND_API_KEY_EXISTS ? '✅ Available' : '❌ Missing');
  console.log('Welcome Email:', emailResult?.success ? '✅ Working' : '❌ Failed');
  
  if (envData.RESEND_API_KEY_EXISTS && emailResult?.success) {
    console.log('\n🎉 All tests passed! The email fix is working correctly.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the logs above for details.');
  }
}

// Run the tests
runTests().catch(console.error);
