/**
 * Test script for enhanced browsing capabilities
 * Tests the new search result parsing and advanced automation features via API
 */

const fetch = require('node-fetch');

async function testEnhancedBrowsing() {
  console.log('🧪 Testing Enhanced Browsing System via API...\n');

  const API_BASE = 'http://localhost:3000/api';

  // Test queries that should demonstrate the improvements
  const testQueries = [
    {
      name: 'Current AI Model Releases',
      query: 'latest AI model releases 2025',
      description: 'Should find current 2025 AI models like DeepSeek R1, GPT-4.1, etc.'
    },
    {
      name: 'Recent Technology Trends',
      query: 'current technology trends January 2025',
      description: 'Should include temporal context and find recent trends'
    },
    {
      name: 'Complex Automation Task',
      query: 'how to book restaurant reservation online',
      description: 'Should detect complex automation needs'
    },
    {
      name: 'Enhanced Search Quality',
      query: 'DeepSeek R1 model capabilities features',
      description: 'Should return high-quality results with good snippets'
    }
  ];

  console.log('🔧 Testing enhanced browsing features:\n');
  console.log('✨ Enhanced search result parsing with modern HTML patterns');
  console.log('📅 Current date/time awareness in search terms');
  console.log('🤖 Complex automation workflow detection');
  console.log('📊 Result quality scoring and ranking');
  console.log('🎯 Better snippet extraction for accurate information\n');

  for (const test of testQueries) {
    console.log(`📋 Test: ${test.name}`);
    console.log(`🔍 Query: "${test.query}"`);
    console.log(`📝 Expected: ${test.description}`);

    try {
      // Test the browsing endpoint
      const response = await fetch(`${API_BASE}/tools/web-browsing`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'search',
          query: test.query,
          searchEngine: 'google'
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ API Response received');

        if (result.data) {
          // Check if it's search results or content
          if (result.data.results && Array.isArray(result.data.results)) {
            console.log(`📊 Found ${result.data.results.length} search results`);

            // Analyze search result quality
            const resultsWithSnippets = result.data.results.filter(r => r.snippet && r.snippet !== 'No description available');
            const hasCurrentYear = result.data.results.some(r =>
              (r.title + ' ' + r.snippet).includes('2025')
            );
            const hasRecentTerms = result.data.results.some(r =>
              /\b(latest|recent|current|new)\b/i.test(r.title + ' ' + r.snippet)
            );

            console.log(`📊 Quality indicators:`);
            console.log(`   📄 Results with snippets: ${resultsWithSnippets.length}/${result.data.results.length}`);
            console.log(`   📅 Contains 2025: ${hasCurrentYear ? '✅' : '❌'}`);
            console.log(`   🆕 Has recent terms: ${hasRecentTerms ? '✅' : '❌'}`);

            // Show sample results
            if (resultsWithSnippets.length > 0) {
              const sample = resultsWithSnippets[0];
              console.log(`📝 Sample result: "${sample.title}"`);
              console.log(`   🔗 ${sample.link}`);
              console.log(`   📄 ${sample.snippet.substring(0, 150)}...`);
            }
          } else if (result.data.content) {
            const preview = result.data.content.substring(0, 300);
            console.log(`📄 Content preview: ${preview}...`);

            // Check for quality indicators
            const hasCurrentYear = result.data.content.includes('2025');
            const hasRecentTerms = /\b(latest|recent|current|new)\b/i.test(result.data.content);
            const hasGoodLength = result.data.content.length > 500;

            console.log(`📊 Quality indicators:`);
            console.log(`   📅 Contains 2025: ${hasCurrentYear ? '✅' : '❌'}`);
            console.log(`   🆕 Has recent terms: ${hasRecentTerms ? '✅' : '❌'}`);
            console.log(`   📏 Good content length: ${hasGoodLength ? '✅' : '❌'}`);
          } else {
            console.log(`📄 Raw data: ${JSON.stringify(result.data).substring(0, 200)}...`);
          }
        } else {
          console.log('⚠️  No data in response');
        }
      } else {
        console.log(`❌ API Error: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.log(`💥 Test failed: ${error.message}`);
    }

    console.log('\n' + '='.repeat(80) + '\n');
  }

  console.log('🎉 Enhanced Browsing Tests Complete!');
  console.log('\n📋 Summary of Improvements:');
  console.log('• Modern Google/Bing HTML parsing patterns for 2025');
  console.log('• Enhanced snippet extraction with relevance scoring');
  console.log('• Current date/time awareness in search terms');
  console.log('• Complex automation workflow detection');
  console.log('• BrowserQL integration for advanced capabilities');
  console.log('• Form filling, CAPTCHA solving, infinite scroll support');
  console.log('• Session management for multi-step workflows');
}

// Run tests if this file is executed directly
if (require.main === module) {
  testEnhancedBrowsing().catch(console.error);
}

module.exports = { testEnhancedBrowsing };
