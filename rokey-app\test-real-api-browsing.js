// Real API Browsing Test - Tests actual RouKey API endpoints
// Compares what the system should do vs what it actually does

const fetch = require('node-fetch');

const REAL_USER_DATA = {
  id: '69d967d5-0b7b-402b-ae1b-711d9b74eef4',
  email: 'roni<PERSON><EMAIL>',
  full_name: '<PERSON><PERSON>',
  subscription_tier: 'professional',
  subscription_status: 'active'
};

const REAL_CONFIG_ID = 'openrouter'; // Assuming this is the config name

class RealAPIBrowsingTest {
  constructor() {
    this.baseUrl = 'http://localhost:3000'; // Adjust if different
    this.apiKey = 'test-api-key'; // You'll need to provide a real API key
  }

  async testRealAPI() {
    console.log('🌐 REAL API BROWSING TEST - Testing Actual RouKey Endpoints');
    console.log('=' .repeat(80));
    console.log(`👤 User: ${REAL_USER_DATA.full_name} (${REAL_USER_DATA.email})`);
    console.log(`🔧 Config: ${REAL_CONFIG_ID}`);
    console.log('=' .repeat(80));

    const complexQuery = "when does the earliest flight from Owerri to Abuja leave today? what flight is that? when that flight arrives at Abuja, what is the earliest that leaves to Dubai from Abuja? how long would travel be on both flights?";

    console.log(`\n📝 Complex Query: ${complexQuery}`);
    console.log('\n🚀 Testing real API endpoints...\n');

    try {
      // Test 1: Check if browsing is enabled for this user/config
      console.log('📍 STEP 1: CHECKING BROWSING CONFIGURATION');
      console.log('-'.repeat(50));
      
      const configResult = await this.checkBrowsingConfig();
      if (configResult.success) {
        console.log('✅ Browsing configuration found');
        console.log(`   Browsing enabled: ${configResult.browsing_enabled}`);
        console.log(`   Models configured: ${configResult.browsing_models?.length || 0}`);
      } else {
        console.log('❌ Failed to get browsing configuration:', configResult.error);
        return;
      }

      // Test 2: Call the actual chat/completion endpoint with browsing
      console.log('\n📍 STEP 2: CALLING REAL CHAT API WITH BROWSING');
      console.log('-'.repeat(50));
      
      const chatResult = await this.callChatAPI(complexQuery);
      if (chatResult.success) {
        console.log('✅ Chat API call successful');
        await this.analyzeChatResponse(chatResult.response);
      } else {
        console.log('❌ Chat API call failed:', chatResult.error);
      }

      // Test 3: Check if there's a dedicated browsing endpoint
      console.log('\n📍 STEP 3: TESTING DEDICATED BROWSING ENDPOINT');
      console.log('-'.repeat(50));
      
      const browsingResult = await this.callBrowsingAPI(complexQuery);
      if (browsingResult.success) {
        console.log('✅ Dedicated browsing endpoint found');
        await this.analyzeBrowsingResponse(browsingResult.response);
      } else {
        console.log('❌ Dedicated browsing endpoint not available or failed:', browsingResult.error);
      }

    } catch (error) {
      console.error('\n💥 Critical error during real API test:', error);
    }
  }

  async checkBrowsingConfig() {
    try {
      // This would call your actual config endpoint
      // Adjust the endpoint based on your actual API structure
      const response = await fetch(`${this.baseUrl}/api/configs/${REAL_CONFIG_ID}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        return {
          success: true,
          browsing_enabled: data.browsing_enabled,
          browsing_models: data.browsing_models
        };
      } else {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async callChatAPI(query) {
    try {
      // Call your actual chat/completion endpoint
      const response = await fetch(`${this.baseUrl}/api/chat/completions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'X-User-ID': REAL_USER_DATA.id
        },
        body: JSON.stringify({
          messages: [
            {
              role: 'user',
              content: query
            }
          ],
          config_name: REAL_CONFIG_ID,
          stream: false,
          browsing_enabled: true
        })
      });

      if (response.ok) {
        const data = await response.json();
        return {
          success: true,
          response: data
        };
      } else {
        const errorText = await response.text();
        return {
          success: false,
          error: `HTTP ${response.status}: ${errorText}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async callBrowsingAPI(query) {
    try {
      // Call dedicated browsing endpoint if it exists
      const response = await fetch(`${this.baseUrl}/api/browsing/search`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.apiKey}`,
          'Content-Type': 'application/json',
          'X-User-ID': REAL_USER_DATA.id
        },
        body: JSON.stringify({
          query: query,
          config_name: REAL_CONFIG_ID,
          browsing_type: 'search'
        })
      });

      if (response.ok) {
        const data = await response.json();
        return {
          success: true,
          response: data
        };
      } else {
        return {
          success: false,
          error: `HTTP ${response.status}: ${response.statusText}`
        };
      }
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  async analyzeChatResponse(response) {
    console.log('📊 CHAT API RESPONSE ANALYSIS:');
    
    // Check if response contains browsing indicators
    const responseText = JSON.stringify(response);
    
    const indicators = {
      hasBrowsingPlan: responseText.includes('plan') || responseText.includes('subtask'),
      hasTemporalAwareness: responseText.includes('today') || responseText.includes('2025'),
      hasFlightInfo: responseText.includes('flight') && (responseText.includes('AM') || responseText.includes('PM')),
      hasSpecificDetails: responseText.includes('Air Peace') || responseText.includes('Emirates') || responseText.includes('APK') || responseText.includes('EK'),
      hasStructuredData: responseText.includes('departure') && responseText.includes('arrival'),
      hasProgressIndicators: responseText.includes('progress') || responseText.includes('executing')
    };

    Object.entries(indicators).forEach(([key, value]) => {
      const status = value ? '✅' : '❌';
      console.log(`   ${status} ${key}: ${value}`);
    });

    // Show actual response content (truncated)
    const content = response.choices?.[0]?.message?.content || response.content || JSON.stringify(response);
    console.log(`\n📄 Response Content (first 500 chars):`);
    console.log(content.substring(0, 500) + (content.length > 500 ? '...' : ''));
  }

  async analyzeBrowsingResponse(response) {
    console.log('📊 BROWSING API RESPONSE ANALYSIS:');
    
    const responseText = JSON.stringify(response);
    
    const browsingIndicators = {
      hasSubtasks: response.plan?.subtasks?.length > 0,
      hasProgress: typeof response.progress === 'number',
      hasVisitedUrls: response.visitedUrls?.length > 0,
      hasGatheredData: Object.keys(response.gatheredData || {}).length > 0,
      hasSearchQueries: response.searchQueries?.length > 0,
      isCompleted: response.status === 'completed'
    };

    Object.entries(browsingIndicators).forEach(([key, value]) => {
      const status = value ? '✅' : '❌';
      console.log(`   ${status} ${key}: ${value}`);
    });

    if (response.plan?.subtasks) {
      console.log(`\n📝 Subtasks Found: ${response.plan.subtasks.length}`);
      response.plan.subtasks.slice(0, 3).forEach((task, index) => {
        console.log(`   ${index + 1}. [${task.type}] ${task.description}`);
      });
    }

    if (response.finalResult) {
      console.log(`\n💬 Final Result:`);
      console.log(response.finalResult.substring(0, 300) + '...');
    }
  }
}

// Run the real API test
if (require.main === module) {
  console.log('🌐 STARTING REAL API BROWSING TEST');
  console.log('This test calls actual RouKey API endpoints to see current behavior\n');
  
  const test = new RealAPIBrowsingTest();
  test.testRealAPI()
    .then(() => {
      console.log('\n✅ Real API test completed!');
      console.log('\n💡 NEXT STEPS:');
      console.log('   1. Compare this output with the intelligent test expectations');
      console.log('   2. Identify gaps in current implementation');
      console.log('   3. Implement missing intelligent browsing features');
      console.log('   4. Add temporal awareness and context building');
      console.log('   5. Enhance content extraction and final answer generation');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Real API test failed:', error);
      process.exit(1);
    });
}

module.exports = { RealAPIBrowsingTest };
