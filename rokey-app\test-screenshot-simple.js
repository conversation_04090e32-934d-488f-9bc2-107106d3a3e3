// Simple screenshot test to debug BrowserQL syntax
const fetch = require('node-fetch');

async function testSimpleScreenshot() {
  console.log('🔍 Testing simple screenshot...');
  
  const apiKey = process.env.BROWSERLESS_API_KEY || '2ScDdfCuVNKkYC3f324cbad0915d0c5128dc1805683dcf963';
  const baseUrl = 'https://production-sfo.browserless.io/chromium/bql';

  const simpleScript = `
    mutation SimpleScreenshot {
      goto(url: "https://example.com/", waitUntil: domContentLoaded) {
        status
      }
      
      screenshot {
        base64
      }
    }
  `;

  try {
    const response = await fetch(`${baseUrl}?token=${apiKey}&humanlike=true&adBlock=true&blockConsentModals=true&timeout=60000`, {
      method: 'POST',
      headers: { 
        'Content-Type': 'application/json',
        'User-Agent': 'RouKey-Browser-Test/1.0'
      },
      body: JSON.stringify({ 
        query: simpleScript,
        variables: {}
      })
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const result = await response.json();

    if (result.errors && result.errors.length > 0) {
      throw new Error(`BrowserQL errors: ${result.errors.map(e => e.message).join(', ')}`);
    }

    console.log('✅ Simple screenshot test successful!');
    console.log('Result:', {
      status: result.data?.goto?.status,
      hasScreenshot: !!result.data?.screenshot?.base64,
      screenshotLength: result.data?.screenshot?.base64?.length || 0
    });

  } catch (error) {
    console.error('❌ Simple screenshot test failed:', error.message);
  }
}

if (require.main === module) {
  testSimpleScreenshot();
}
