/**
 * Test script to verify the signup flow with automatic welcome emails
 */

const API_BASE = 'http://localhost:3000';
const API_KEY = 'Y21ErgiIHrVgoSidqkShBT9npjOuP8fAlzQlTVfkmaeUUY4Kpq%*13';

async function testFreeSignup() {
  console.log('🆓 Testing free signup with automatic welcome email...');
  
  const testEmail = `test-free-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  const testName = 'Test Free User';
  
  try {
    const response = await fetch(`${API_BASE}/api/auth/free-signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
        fullName: testName
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Free signup response:', JSON.stringify(data, null, 2));
    
    return { success: true, data };
  } catch (error) {
    console.error('❌ Free signup failed:', error);
    return { success: false, error: error.message };
  }
}

async function testPaidSignup() {
  console.log('💳 Testing paid signup (creates pending user)...');
  
  const testEmail = `test-paid-${Date.now()}@example.com`;
  const testPassword = 'TestPassword123!';
  const testFirstName = 'Test';
  const testLastName = 'Paid User';
  
  try {
    const response = await fetch(`${API_BASE}/api/auth/paid-signup`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: testEmail,
        password: testPassword,
        firstName: testFirstName,
        lastName: testLastName,
        plan: 'starter'
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const data = await response.json();
    console.log('✅ Paid signup response:', JSON.stringify(data, null, 2));
    
    return { success: true, data };
  } catch (error) {
    console.error('❌ Paid signup failed:', error);
    return { success: false, error: error.message };
  }
}

async function runSignupTests() {
  console.log('🚀 Starting signup flow tests with automatic welcome emails...\n');
  
  // Test 1: Free signup (should send welcome email immediately)
  const freeResult = await testFreeSignup();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  // Test 2: Paid signup (creates pending user, no email yet)
  const paidResult = await testPaidSignup();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 SIGNUP FLOW TEST SUMMARY:');
  console.log('Free Signup:', freeResult.success ? '✅ Working' : '❌ Failed');
  console.log('Paid Signup:', paidResult.success ? '✅ Working' : '❌ Failed');
  
  if (freeResult.success && paidResult.success) {
    console.log('\n🎉 All signup tests passed!');
    console.log('📧 Free users should receive welcome emails immediately.');
    console.log('💳 Paid users will receive welcome emails when payment completes.');
  } else {
    console.log('\n⚠️ Some tests failed. Check the logs above for details.');
  }
  
  console.log('\n📝 NOTE: Check your server logs to see the welcome email sending process.');
}

// Run the tests
runSignupTests().catch(console.error);
