"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_utils_streamingUtils_ts";
exports.ids = ["_rsc_src_utils_streamingUtils_ts"];
exports.modules = {

/***/ "(rsc)/./src/utils/streamingUtils.ts":
/*!*************************************!*\
  !*** ./src/utils/streamingUtils.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PERFORMANCE_THRESHOLDS: () => (/* binding */ PERFORMANCE_THRESHOLDS),\n/* harmony export */   createFirstTokenTrackingStream: () => (/* binding */ createFirstTokenTrackingStream),\n/* harmony export */   estimateTokenCount: () => (/* binding */ estimateTokenCount),\n/* harmony export */   estimateUsageFromContent: () => (/* binding */ estimateUsageFromContent),\n/* harmony export */   evaluatePerformance: () => (/* binding */ evaluatePerformance),\n/* harmony export */   extractUsageFromStreamChunk: () => (/* binding */ extractUsageFromStreamChunk),\n/* harmony export */   getProviderModelFromContext: () => (/* binding */ getProviderModelFromContext),\n/* harmony export */   logStreamingPerformance: () => (/* binding */ logStreamingPerformance)\n/* harmony export */ });\n// Streaming utilities for first token tracking, performance monitoring, and cost calculation\n// Interface for streaming usage data\nfunction createFirstTokenTrackingStream(originalStream, provider, model, onUsageExtracted) {\n    const reader = originalStream.getReader();\n    const decoder = new TextDecoder();\n    const encoder = new TextEncoder();\n    return new ReadableStream({\n        async start (controller) {\n            let firstTokenSent = false;\n            let accumulatedContent = '';\n            const streamStartTime = Date.now();\n            try {\n                while(true){\n                    const { done, value } = await reader.read();\n                    if (done) {\n                        // Final opportunity to extract usage data if not found in chunks\n                        if (onUsageExtracted && !firstTokenSent) {\n                            // Fallback: estimate tokens from accumulated content\n                            const estimatedUsage = estimateUsageFromContent(accumulatedContent, provider);\n                            console.log(`💰 [${provider} Cost] Fallback token estimation: ${estimatedUsage.totalTokens} tokens`);\n                            onUsageExtracted(estimatedUsage);\n                        }\n                        controller.close();\n                        break;\n                    }\n                    const chunk = decoder.decode(value, {\n                        stream: true\n                    });\n                    // Check if this chunk contains actual content (first token)\n                    if (!firstTokenSent && chunk.includes('delta')) {\n                        try {\n                            // Parse SSE data to check for content\n                            const lines = chunk.split('\\n');\n                            for (const line of lines){\n                                if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                                    const jsonData = line.substring(6);\n                                    try {\n                                        const parsed = JSON.parse(jsonData);\n                                        if (parsed.choices?.[0]?.delta?.content) {\n                                            const firstTokenTime = Date.now() - streamStartTime;\n                                            console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model})`);\n                                            firstTokenSent = true;\n                                            // Accumulate content for fallback estimation\n                                            accumulatedContent += parsed.choices[0].delta.content;\n                                            break;\n                                        }\n                                    } catch (e) {\n                                    // Ignore JSON parse errors for individual chunks\n                                    }\n                                }\n                            }\n                        } catch (e) {\n                            // Ignore parsing errors, just track timing\n                            if (!firstTokenSent) {\n                                const firstTokenTime = Date.now() - streamStartTime;\n                                console.log(`🚀 ${provider.toUpperCase()} FIRST TOKEN: ${firstTokenTime}ms (${model}) [fallback detection]`);\n                                firstTokenSent = true;\n                            }\n                        }\n                    }\n                    // Extract usage data from streaming chunks\n                    if (onUsageExtracted && chunk.includes('usage')) {\n                        try {\n                            const usageData = extractUsageFromStreamChunk(chunk, provider);\n                            if (usageData) {\n                                console.log(`💰 [${provider} Cost] Usage data extracted from stream: ${usageData.totalTokens} tokens`);\n                                onUsageExtracted(usageData);\n                            }\n                        } catch (e) {\n                        // Ignore usage extraction errors\n                        }\n                    }\n                    // Continue accumulating content for fallback\n                    if (chunk.includes('delta')) {\n                        try {\n                            const lines = chunk.split('\\n');\n                            for (const line of lines){\n                                if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                                    const jsonData = line.substring(6);\n                                    try {\n                                        const parsed = JSON.parse(jsonData);\n                                        if (parsed.choices?.[0]?.delta?.content) {\n                                            accumulatedContent += parsed.choices[0].delta.content;\n                                        }\n                                    } catch (e) {\n                                    // Ignore JSON parse errors\n                                    }\n                                }\n                            }\n                        } catch (e) {\n                        // Ignore parsing errors\n                        }\n                    }\n                    // Forward the chunk unchanged\n                    controller.enqueue(value);\n                }\n            } catch (error) {\n                console.error(`[${provider} Stream Tracking] Error:`, error);\n                // Phase 1 Optimization: Graceful error handling for connection resets\n                if (error instanceof Error && (error.message.includes('aborted') || error.message.includes('ECONNRESET'))) {\n                    console.log(`[${provider} Stream] Connection reset detected - closing stream gracefully`);\n                    controller.close();\n                } else {\n                    controller.error(error);\n                }\n            }\n        }\n    });\n}\n// Enhanced logging for streaming performance\nfunction logStreamingPerformance(provider, model, metrics) {\n    console.log(`📊 STREAMING PERFORMANCE: ${provider}/${model}`);\n    if (metrics.timeToFirstToken !== undefined) {\n        console.log(`   ⏱️ Time to First Token: ${metrics.timeToFirstToken.toFixed(1)}ms`);\n        // Performance categories\n        if (metrics.timeToFirstToken < 500) {\n            console.log(`   ⚡ EXCELLENT first token performance`);\n        } else if (metrics.timeToFirstToken < 1000) {\n            console.log(`   ✅ GOOD first token performance`);\n        } else if (metrics.timeToFirstToken < 2000) {\n            console.log(`   ⚠️ SLOW first token performance`);\n        } else {\n            console.log(`   🐌 VERY SLOW first token performance`);\n        }\n    }\n    if (metrics.totalStreamTime !== undefined) {\n        console.log(`   🔄 Total Stream Time: ${metrics.totalStreamTime.toFixed(1)}ms`);\n    }\n    if (metrics.totalTokens !== undefined) {\n        console.log(`   🎯 Total Tokens: ${metrics.totalTokens}`);\n    }\n    if (metrics.averageTokenLatency !== undefined) {\n        console.log(`   📈 Avg Token Latency: ${metrics.averageTokenLatency.toFixed(1)}ms/token`);\n    }\n}\n// Utility to extract provider and model from request context\nfunction getProviderModelFromContext(providerName, modelId) {\n    return {\n        provider: providerName || 'unknown',\n        model: modelId || 'unknown'\n    };\n}\n// Extract usage data from streaming chunk\nfunction extractUsageFromStreamChunk(chunk, provider) {\n    try {\n        const lines = chunk.split('\\n');\n        for (const line of lines){\n            if (line.startsWith('data: ') && !line.includes('[DONE]')) {\n                const jsonData = line.substring(6);\n                try {\n                    const parsed = JSON.parse(jsonData);\n                    // Check for usage data in the chunk\n                    if (parsed.usage) {\n                        const usage = parsed.usage;\n                        // Handle different provider formats\n                        let promptTokens = 0;\n                        let completionTokens = 0;\n                        let totalTokens = 0;\n                        let cost;\n                        if (provider.toLowerCase() === 'openrouter') {\n                            // OpenRouter format\n                            promptTokens = usage.prompt_tokens || 0;\n                            completionTokens = usage.completion_tokens || 0;\n                            totalTokens = usage.total_tokens || promptTokens + completionTokens;\n                            cost = usage.cost ? usage.cost * 0.000001 : undefined; // Convert credits to USD\n                        } else {\n                            // Standard OpenAI format (Google, Anthropic, xAI)\n                            promptTokens = usage.prompt_tokens || usage.input_tokens || 0;\n                            completionTokens = usage.completion_tokens || usage.output_tokens || 0;\n                            totalTokens = usage.total_tokens || promptTokens + completionTokens;\n                        }\n                        if (totalTokens > 0) {\n                            return {\n                                promptTokens,\n                                completionTokens,\n                                totalTokens,\n                                cost\n                            };\n                        }\n                    }\n                } catch (e) {\n                // Ignore JSON parse errors for individual chunks\n                }\n            }\n        }\n    } catch (e) {\n    // Ignore parsing errors\n    }\n    return null;\n}\n// Estimate usage from accumulated content (fallback)\nfunction estimateUsageFromContent(content, provider, promptText) {\n    // More accurate token estimation based on provider\n    let tokensPerChar = 0.25; // Default: 4 chars per token\n    // Provider-specific token ratios (based on empirical data)\n    switch(provider.toLowerCase()){\n        case 'openrouter':\n        case 'openai':\n            tokensPerChar = 0.25; // ~4 chars per token\n            break;\n        case 'google':\n            tokensPerChar = 0.22; // ~4.5 chars per token (slightly more efficient)\n            break;\n        case 'anthropic':\n            tokensPerChar = 0.26; // ~3.8 chars per token\n            break;\n        case 'xai':\n            tokensPerChar = 0.25; // Similar to OpenAI\n            break;\n    }\n    const completionTokens = Math.ceil(content.length * tokensPerChar);\n    const promptTokens = promptText ? Math.ceil(promptText.length * tokensPerChar) : Math.ceil(completionTokens * 0.3); // Estimate 30% of completion\n    const totalTokens = promptTokens + completionTokens;\n    return {\n        promptTokens,\n        completionTokens,\n        totalTokens\n    };\n}\n// Simple token counter for rough estimation (legacy function)\nfunction estimateTokenCount(text) {\n    // Rough estimation: 1 token ≈ 4 characters for English text\n    // This is a simplified approach, real tokenization would be more accurate\n    return Math.ceil(text.length / 4);\n}\n// Performance thresholds for different providers\nconst PERFORMANCE_THRESHOLDS = {\n    EXCELLENT_FIRST_TOKEN: 500,\n    GOOD_FIRST_TOKEN: 1000,\n    SLOW_FIRST_TOKEN: 2000,\n    // Anything above 2000ms is considered very slow\n    EXCELLENT_TOTAL: 3000,\n    GOOD_TOTAL: 5000,\n    SLOW_TOTAL: 10000,\n    TARGET_TOKEN_LATENCY: 50\n};\n// Check if performance meets targets\nfunction evaluatePerformance(metrics) {\n    const firstTokenGrade = !metrics.timeToFirstToken ? 'very_slow' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.EXCELLENT_FIRST_TOKEN ? 'excellent' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.GOOD_FIRST_TOKEN ? 'good' : metrics.timeToFirstToken < PERFORMANCE_THRESHOLDS.SLOW_FIRST_TOKEN ? 'slow' : 'very_slow';\n    const totalTimeGrade = !metrics.totalStreamTime ? 'very_slow' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.EXCELLENT_TOTAL ? 'excellent' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.GOOD_TOTAL ? 'good' : metrics.totalStreamTime < PERFORMANCE_THRESHOLDS.SLOW_TOTAL ? 'slow' : 'very_slow';\n    const tokenLatencyGrade = !metrics.averageTokenLatency ? 'very_slow' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY ? 'excellent' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 2 ? 'good' : metrics.averageTokenLatency < PERFORMANCE_THRESHOLDS.TARGET_TOKEN_LATENCY * 4 ? 'slow' : 'very_slow';\n    // Overall grade is the worst of the three\n    const grades = [\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade\n    ];\n    const gradeOrder = [\n        'excellent',\n        'good',\n        'slow',\n        'very_slow'\n    ];\n    const overallGrade = grades.reduce((worst, current)=>{\n        return gradeOrder.indexOf(current) > gradeOrder.indexOf(worst) ? current : worst;\n    }, 'excellent');\n    return {\n        firstTokenGrade,\n        totalTimeGrade,\n        tokenLatencyGrade,\n        overallGrade\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvdXRpbHMvc3RyZWFtaW5nVXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQSw2RkFBNkY7QUFFN0YscUNBQXFDO0FBYTlCLFNBQVNBLCtCQUNkQyxjQUE4QixFQUM5QkMsUUFBZ0IsRUFDaEJDLEtBQWEsRUFDYkMsZ0JBQXdDO0lBRXhDLE1BQU1DLFNBQVNKLGVBQWVLLFNBQVM7SUFDdkMsTUFBTUMsVUFBVSxJQUFJQztJQUNwQixNQUFNQyxVQUFVLElBQUlDO0lBRXBCLE9BQU8sSUFBSUMsZUFBZTtRQUN4QixNQUFNQyxPQUFNQyxVQUFVO1lBQ3BCLElBQUlDLGlCQUFpQjtZQUNyQixJQUFJQyxxQkFBcUI7WUFDekIsTUFBTUMsa0JBQWtCQyxLQUFLQyxHQUFHO1lBRWhDLElBQUk7Z0JBQ0YsTUFBTyxLQUFNO29CQUNYLE1BQU0sRUFBRUMsSUFBSSxFQUFFQyxLQUFLLEVBQUUsR0FBRyxNQUFNZixPQUFPZ0IsSUFBSTtvQkFFekMsSUFBSUYsTUFBTTt3QkFDUixpRUFBaUU7d0JBQ2pFLElBQUlmLG9CQUFvQixDQUFDVSxnQkFBZ0I7NEJBQ3ZDLHFEQUFxRDs0QkFDckQsTUFBTVEsaUJBQWlCQyx5QkFBeUJSLG9CQUFvQmI7NEJBQ3BFc0IsUUFBUUMsR0FBRyxDQUFDLENBQUMsSUFBSSxFQUFFdkIsU0FBUyxrQ0FBa0MsRUFBRW9CLGVBQWVJLFdBQVcsQ0FBQyxPQUFPLENBQUM7NEJBQ25HdEIsaUJBQWlCa0I7d0JBQ25CO3dCQUNBVCxXQUFXYyxLQUFLO3dCQUNoQjtvQkFDRjtvQkFFQSxNQUFNQyxRQUFRckIsUUFBUXNCLE1BQU0sQ0FBQ1QsT0FBTzt3QkFBRVUsUUFBUTtvQkFBSztvQkFFbkQsNERBQTREO29CQUM1RCxJQUFJLENBQUNoQixrQkFBa0JjLE1BQU1HLFFBQVEsQ0FBQyxVQUFVO3dCQUM5QyxJQUFJOzRCQUNGLHNDQUFzQzs0QkFDdEMsTUFBTUMsUUFBUUosTUFBTUssS0FBSyxDQUFDOzRCQUMxQixLQUFLLE1BQU1DLFFBQVFGLE1BQU87Z0NBQ3hCLElBQUlFLEtBQUtDLFVBQVUsQ0FBQyxhQUFhLENBQUNELEtBQUtILFFBQVEsQ0FBQyxXQUFXO29DQUN6RCxNQUFNSyxXQUFXRixLQUFLRyxTQUFTLENBQUM7b0NBQ2hDLElBQUk7d0NBQ0YsTUFBTUMsU0FBU0MsS0FBS0MsS0FBSyxDQUFDSjt3Q0FDMUIsSUFBSUUsT0FBT0csT0FBTyxFQUFFLENBQUMsRUFBRSxFQUFFQyxPQUFPQyxTQUFTOzRDQUN2QyxNQUFNQyxpQkFBaUIzQixLQUFLQyxHQUFHLEtBQUtGOzRDQUNwQ1EsUUFBUUMsR0FBRyxDQUFDLENBQUMsR0FBRyxFQUFFdkIsU0FBUzJDLFdBQVcsR0FBRyxjQUFjLEVBQUVELGVBQWUsSUFBSSxFQUFFekMsTUFBTSxDQUFDLENBQUM7NENBQ3RGVyxpQkFBaUI7NENBRWpCLDZDQUE2Qzs0Q0FDN0NDLHNCQUFzQnVCLE9BQU9HLE9BQU8sQ0FBQyxFQUFFLENBQUNDLEtBQUssQ0FBQ0MsT0FBTzs0Q0FDckQ7d0NBQ0Y7b0NBQ0YsRUFBRSxPQUFPRyxHQUFHO29DQUNWLGlEQUFpRDtvQ0FDbkQ7Z0NBQ0Y7NEJBQ0Y7d0JBQ0YsRUFBRSxPQUFPQSxHQUFHOzRCQUNWLDJDQUEyQzs0QkFDM0MsSUFBSSxDQUFDaEMsZ0JBQWdCO2dDQUNuQixNQUFNOEIsaUJBQWlCM0IsS0FBS0MsR0FBRyxLQUFLRjtnQ0FDcENRLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLEdBQUcsRUFBRXZCLFNBQVMyQyxXQUFXLEdBQUcsY0FBYyxFQUFFRCxlQUFlLElBQUksRUFBRXpDLE1BQU0sc0JBQXNCLENBQUM7Z0NBQzNHVyxpQkFBaUI7NEJBQ25CO3dCQUNGO29CQUNGO29CQUVBLDJDQUEyQztvQkFDM0MsSUFBSVYsb0JBQW9Cd0IsTUFBTUcsUUFBUSxDQUFDLFVBQVU7d0JBQy9DLElBQUk7NEJBQ0YsTUFBTWdCLFlBQVlDLDRCQUE0QnBCLE9BQU8xQjs0QkFDckQsSUFBSTZDLFdBQVc7Z0NBQ2J2QixRQUFRQyxHQUFHLENBQUMsQ0FBQyxJQUFJLEVBQUV2QixTQUFTLHlDQUF5QyxFQUFFNkMsVUFBVXJCLFdBQVcsQ0FBQyxPQUFPLENBQUM7Z0NBQ3JHdEIsaUJBQWlCMkM7NEJBQ25CO3dCQUNGLEVBQUUsT0FBT0QsR0FBRzt3QkFDVixpQ0FBaUM7d0JBQ25DO29CQUNGO29CQUVBLDZDQUE2QztvQkFDN0MsSUFBSWxCLE1BQU1HLFFBQVEsQ0FBQyxVQUFVO3dCQUMzQixJQUFJOzRCQUNGLE1BQU1DLFFBQVFKLE1BQU1LLEtBQUssQ0FBQzs0QkFDMUIsS0FBSyxNQUFNQyxRQUFRRixNQUFPO2dDQUN4QixJQUFJRSxLQUFLQyxVQUFVLENBQUMsYUFBYSxDQUFDRCxLQUFLSCxRQUFRLENBQUMsV0FBVztvQ0FDekQsTUFBTUssV0FBV0YsS0FBS0csU0FBUyxDQUFDO29DQUNoQyxJQUFJO3dDQUNGLE1BQU1DLFNBQVNDLEtBQUtDLEtBQUssQ0FBQ0o7d0NBQzFCLElBQUlFLE9BQU9HLE9BQU8sRUFBRSxDQUFDLEVBQUUsRUFBRUMsT0FBT0MsU0FBUzs0Q0FDdkM1QixzQkFBc0J1QixPQUFPRyxPQUFPLENBQUMsRUFBRSxDQUFDQyxLQUFLLENBQUNDLE9BQU87d0NBQ3ZEO29DQUNGLEVBQUUsT0FBT0csR0FBRztvQ0FDViwyQkFBMkI7b0NBQzdCO2dDQUNGOzRCQUNGO3dCQUNGLEVBQUUsT0FBT0EsR0FBRzt3QkFDVix3QkFBd0I7d0JBQzFCO29CQUNGO29CQUVBLDhCQUE4QjtvQkFDOUJqQyxXQUFXb0MsT0FBTyxDQUFDN0I7Z0JBQ3JCO1lBQ0YsRUFBRSxPQUFPOEIsT0FBTztnQkFDZDFCLFFBQVEwQixLQUFLLENBQUMsQ0FBQyxDQUFDLEVBQUVoRCxTQUFTLHdCQUF3QixDQUFDLEVBQUVnRDtnQkFDdEQsc0VBQXNFO2dCQUN0RSxJQUFJQSxpQkFBaUJDLFNBQVVELENBQUFBLE1BQU1FLE9BQU8sQ0FBQ3JCLFFBQVEsQ0FBQyxjQUFjbUIsTUFBTUUsT0FBTyxDQUFDckIsUUFBUSxDQUFDLGFBQVksR0FBSTtvQkFDekdQLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRXZCLFNBQVMsOERBQThELENBQUM7b0JBQ3hGVyxXQUFXYyxLQUFLO2dCQUNsQixPQUFPO29CQUNMZCxXQUFXcUMsS0FBSyxDQUFDQTtnQkFDbkI7WUFDRjtRQUNGO0lBQ0Y7QUFDRjtBQUVBLDZDQUE2QztBQUN0QyxTQUFTRyx3QkFDZG5ELFFBQWdCLEVBQ2hCQyxLQUFhLEVBQ2JtRCxPQUtDO0lBRUQ5QixRQUFRQyxHQUFHLENBQUMsQ0FBQywwQkFBMEIsRUFBRXZCLFNBQVMsQ0FBQyxFQUFFQyxPQUFPO0lBRTVELElBQUltRCxRQUFRQyxnQkFBZ0IsS0FBS0MsV0FBVztRQUMxQ2hDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLDJCQUEyQixFQUFFNkIsUUFBUUMsZ0JBQWdCLENBQUNFLE9BQU8sQ0FBQyxHQUFHLEVBQUUsQ0FBQztRQUVqRix5QkFBeUI7UUFDekIsSUFBSUgsUUFBUUMsZ0JBQWdCLEdBQUcsS0FBSztZQUNsQy9CLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHNDQUFzQyxDQUFDO1FBQ3RELE9BQU8sSUFBSTZCLFFBQVFDLGdCQUFnQixHQUFHLE1BQU07WUFDMUMvQixRQUFRQyxHQUFHLENBQUMsQ0FBQyxpQ0FBaUMsQ0FBQztRQUNqRCxPQUFPLElBQUk2QixRQUFRQyxnQkFBZ0IsR0FBRyxNQUFNO1lBQzFDL0IsUUFBUUMsR0FBRyxDQUFDLENBQUMsa0NBQWtDLENBQUM7UUFDbEQsT0FBTztZQUNMRCxRQUFRQyxHQUFHLENBQUMsQ0FBQyx1Q0FBdUMsQ0FBQztRQUN2RDtJQUNGO0lBRUEsSUFBSTZCLFFBQVFJLGVBQWUsS0FBS0YsV0FBVztRQUN6Q2hDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlCQUF5QixFQUFFNkIsUUFBUUksZUFBZSxDQUFDRCxPQUFPLENBQUMsR0FBRyxFQUFFLENBQUM7SUFDaEY7SUFFQSxJQUFJSCxRQUFRNUIsV0FBVyxLQUFLOEIsV0FBVztRQUNyQ2hDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9CQUFvQixFQUFFNkIsUUFBUTVCLFdBQVcsRUFBRTtJQUMxRDtJQUVBLElBQUk0QixRQUFRSyxtQkFBbUIsS0FBS0gsV0FBVztRQUM3Q2hDLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLHlCQUF5QixFQUFFNkIsUUFBUUssbUJBQW1CLENBQUNGLE9BQU8sQ0FBQyxHQUFHLFFBQVEsQ0FBQztJQUMxRjtBQUNGO0FBRUEsNkRBQTZEO0FBQ3RELFNBQVNHLDRCQUNkQyxZQUEyQixFQUMzQkMsT0FBc0I7SUFFdEIsT0FBTztRQUNMNUQsVUFBVTJELGdCQUFnQjtRQUMxQjFELE9BQU8yRCxXQUFXO0lBQ3BCO0FBQ0Y7QUFFQSwwQ0FBMEM7QUFDbkMsU0FBU2QsNEJBQTRCcEIsS0FBYSxFQUFFMUIsUUFBZ0I7SUFDekUsSUFBSTtRQUNGLE1BQU04QixRQUFRSixNQUFNSyxLQUFLLENBQUM7UUFDMUIsS0FBSyxNQUFNQyxRQUFRRixNQUFPO1lBQ3hCLElBQUlFLEtBQUtDLFVBQVUsQ0FBQyxhQUFhLENBQUNELEtBQUtILFFBQVEsQ0FBQyxXQUFXO2dCQUN6RCxNQUFNSyxXQUFXRixLQUFLRyxTQUFTLENBQUM7Z0JBQ2hDLElBQUk7b0JBQ0YsTUFBTUMsU0FBU0MsS0FBS0MsS0FBSyxDQUFDSjtvQkFFMUIsb0NBQW9DO29CQUNwQyxJQUFJRSxPQUFPeUIsS0FBSyxFQUFFO3dCQUNoQixNQUFNQSxRQUFRekIsT0FBT3lCLEtBQUs7d0JBRTFCLG9DQUFvQzt3QkFDcEMsSUFBSUMsZUFBZTt3QkFDbkIsSUFBSUMsbUJBQW1CO3dCQUN2QixJQUFJdkMsY0FBYzt3QkFDbEIsSUFBSXdDO3dCQUVKLElBQUloRSxTQUFTaUUsV0FBVyxPQUFPLGNBQWM7NEJBQzNDLG9CQUFvQjs0QkFDcEJILGVBQWVELE1BQU1LLGFBQWEsSUFBSTs0QkFDdENILG1CQUFtQkYsTUFBTU0saUJBQWlCLElBQUk7NEJBQzlDM0MsY0FBY3FDLE1BQU1PLFlBQVksSUFBS04sZUFBZUM7NEJBQ3BEQyxPQUFPSCxNQUFNRyxJQUFJLEdBQUdILE1BQU1HLElBQUksR0FBRyxXQUFXVixXQUFXLHlCQUF5Qjt3QkFDbEYsT0FBTzs0QkFDTCxrREFBa0Q7NEJBQ2xEUSxlQUFlRCxNQUFNSyxhQUFhLElBQUlMLE1BQU1RLFlBQVksSUFBSTs0QkFDNUROLG1CQUFtQkYsTUFBTU0saUJBQWlCLElBQUlOLE1BQU1TLGFBQWEsSUFBSTs0QkFDckU5QyxjQUFjcUMsTUFBTU8sWUFBWSxJQUFLTixlQUFlQzt3QkFDdEQ7d0JBRUEsSUFBSXZDLGNBQWMsR0FBRzs0QkFDbkIsT0FBTztnQ0FDTHNDO2dDQUNBQztnQ0FDQXZDO2dDQUNBd0M7NEJBQ0Y7d0JBQ0Y7b0JBQ0Y7Z0JBQ0YsRUFBRSxPQUFPcEIsR0FBRztnQkFDVixpREFBaUQ7Z0JBQ25EO1lBQ0Y7UUFDRjtJQUNGLEVBQUUsT0FBT0EsR0FBRztJQUNWLHdCQUF3QjtJQUMxQjtJQUVBLE9BQU87QUFDVDtBQUVBLHFEQUFxRDtBQUM5QyxTQUFTdkIseUJBQXlCb0IsT0FBZSxFQUFFekMsUUFBZ0IsRUFBRXVFLFVBQW1CO0lBQzdGLG1EQUFtRDtJQUNuRCxJQUFJQyxnQkFBZ0IsTUFBTSw2QkFBNkI7SUFFdkQsMkRBQTJEO0lBQzNELE9BQVF4RSxTQUFTaUUsV0FBVztRQUMxQixLQUFLO1FBQ0wsS0FBSztZQUNITyxnQkFBZ0IsTUFBTSxxQkFBcUI7WUFDM0M7UUFDRixLQUFLO1lBQ0hBLGdCQUFnQixNQUFNLGlEQUFpRDtZQUN2RTtRQUNGLEtBQUs7WUFDSEEsZ0JBQWdCLE1BQU0sdUJBQXVCO1lBQzdDO1FBQ0YsS0FBSztZQUNIQSxnQkFBZ0IsTUFBTSxvQkFBb0I7WUFDMUM7SUFDSjtJQUVBLE1BQU1ULG1CQUFtQlUsS0FBS0MsSUFBSSxDQUFDakMsUUFBUWtDLE1BQU0sR0FBR0g7SUFDcEQsTUFBTVYsZUFBZVMsYUFBYUUsS0FBS0MsSUFBSSxDQUFDSCxXQUFXSSxNQUFNLEdBQUdILGlCQUFpQkMsS0FBS0MsSUFBSSxDQUFDWCxtQkFBbUIsTUFBTSw2QkFBNkI7SUFDakosTUFBTXZDLGNBQWNzQyxlQUFlQztJQUVuQyxPQUFPO1FBQ0xEO1FBQ0FDO1FBQ0F2QztJQUNGO0FBQ0Y7QUFFQSw4REFBOEQ7QUFDdkQsU0FBU29ELG1CQUFtQkMsSUFBWTtJQUM3Qyw0REFBNEQ7SUFDNUQsMEVBQTBFO0lBQzFFLE9BQU9KLEtBQUtDLElBQUksQ0FBQ0csS0FBS0YsTUFBTSxHQUFHO0FBQ2pDO0FBRUEsaURBQWlEO0FBQzFDLE1BQU1HLHlCQUF5QjtJQUNwQ0MsdUJBQXVCO0lBQ3ZCQyxrQkFBa0I7SUFDbEJDLGtCQUFrQjtJQUNsQixnREFBZ0Q7SUFFaERDLGlCQUFpQjtJQUNqQkMsWUFBWTtJQUNaQyxZQUFZO0lBRVpDLHNCQUFzQjtBQUN4QixFQUFXO0FBRVgscUNBQXFDO0FBQzlCLFNBQVNDLG9CQUFvQmxDLE9BSW5DO0lBTUMsTUFBTW1DLGtCQUNKLENBQUNuQyxRQUFRQyxnQkFBZ0IsR0FBRyxjQUM1QkQsUUFBUUMsZ0JBQWdCLEdBQUd5Qix1QkFBdUJDLHFCQUFxQixHQUFHLGNBQzFFM0IsUUFBUUMsZ0JBQWdCLEdBQUd5Qix1QkFBdUJFLGdCQUFnQixHQUFHLFNBQ3JFNUIsUUFBUUMsZ0JBQWdCLEdBQUd5Qix1QkFBdUJHLGdCQUFnQixHQUFHLFNBQVM7SUFFaEYsTUFBTU8saUJBQ0osQ0FBQ3BDLFFBQVFJLGVBQWUsR0FBRyxjQUMzQkosUUFBUUksZUFBZSxHQUFHc0IsdUJBQXVCSSxlQUFlLEdBQUcsY0FDbkU5QixRQUFRSSxlQUFlLEdBQUdzQix1QkFBdUJLLFVBQVUsR0FBRyxTQUM5RC9CLFFBQVFJLGVBQWUsR0FBR3NCLHVCQUF1Qk0sVUFBVSxHQUFHLFNBQVM7SUFFekUsTUFBTUssb0JBQ0osQ0FBQ3JDLFFBQVFLLG1CQUFtQixHQUFHLGNBQy9CTCxRQUFRSyxtQkFBbUIsR0FBR3FCLHVCQUF1Qk8sb0JBQW9CLEdBQUcsY0FDNUVqQyxRQUFRSyxtQkFBbUIsR0FBR3FCLHVCQUF1Qk8sb0JBQW9CLEdBQUcsSUFBSSxTQUNoRmpDLFFBQVFLLG1CQUFtQixHQUFHcUIsdUJBQXVCTyxvQkFBb0IsR0FBRyxJQUFJLFNBQVM7SUFFM0YsMENBQTBDO0lBQzFDLE1BQU1LLFNBQVM7UUFBQ0g7UUFBaUJDO1FBQWdCQztLQUFrQjtJQUNuRSxNQUFNRSxhQUFhO1FBQUM7UUFBYTtRQUFRO1FBQVE7S0FBWTtJQUM3RCxNQUFNQyxlQUFlRixPQUFPRyxNQUFNLENBQUMsQ0FBQ0MsT0FBT0M7UUFDekMsT0FBT0osV0FBV0ssT0FBTyxDQUFDRCxXQUFXSixXQUFXSyxPQUFPLENBQUNGLFNBQVNDLFVBQVVEO0lBQzdFLEdBQUc7SUFFSCxPQUFPO1FBQ0xQO1FBQ0FDO1FBQ0FDO1FBQ0FHO0lBQ0Y7QUFDRiIsInNvdXJjZXMiOlsiQzpcXFJvS2V5IEFwcFxccm9rZXktYXBwXFxzcmNcXHV0aWxzXFxzdHJlYW1pbmdVdGlscy50cyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBTdHJlYW1pbmcgdXRpbGl0aWVzIGZvciBmaXJzdCB0b2tlbiB0cmFja2luZywgcGVyZm9ybWFuY2UgbW9uaXRvcmluZywgYW5kIGNvc3QgY2FsY3VsYXRpb25cblxuLy8gSW50ZXJmYWNlIGZvciBzdHJlYW1pbmcgdXNhZ2UgZGF0YVxuZXhwb3J0IGludGVyZmFjZSBTdHJlYW1pbmdVc2FnZURhdGEge1xuICBwcm9tcHRUb2tlbnM6IG51bWJlcjtcbiAgY29tcGxldGlvblRva2VuczogbnVtYmVyO1xuICB0b3RhbFRva2VuczogbnVtYmVyO1xuICBjb3N0PzogbnVtYmVyOyAvLyBGb3IgcHJvdmlkZXJzIGxpa2UgT3BlblJvdXRlciB0aGF0IHByb3ZpZGUgZGlyZWN0IGNvc3Rcbn1cblxuLy8gSW50ZXJmYWNlIGZvciBzdHJlYW1pbmcgY29zdCBjYWxsYmFja1xuZXhwb3J0IGludGVyZmFjZSBTdHJlYW1pbmdDb3N0Q2FsbGJhY2sge1xuICAodXNhZ2VEYXRhOiBTdHJlYW1pbmdVc2FnZURhdGEpOiB2b2lkO1xufVxuXG5leHBvcnQgZnVuY3Rpb24gY3JlYXRlRmlyc3RUb2tlblRyYWNraW5nU3RyZWFtKFxuICBvcmlnaW5hbFN0cmVhbTogUmVhZGFibGVTdHJlYW0sXG4gIHByb3ZpZGVyOiBzdHJpbmcsXG4gIG1vZGVsOiBzdHJpbmcsXG4gIG9uVXNhZ2VFeHRyYWN0ZWQ/OiBTdHJlYW1pbmdDb3N0Q2FsbGJhY2tcbik6IFJlYWRhYmxlU3RyZWFtIHtcbiAgY29uc3QgcmVhZGVyID0gb3JpZ2luYWxTdHJlYW0uZ2V0UmVhZGVyKCk7XG4gIGNvbnN0IGRlY29kZXIgPSBuZXcgVGV4dERlY29kZXIoKTtcbiAgY29uc3QgZW5jb2RlciA9IG5ldyBUZXh0RW5jb2RlcigpO1xuXG4gIHJldHVybiBuZXcgUmVhZGFibGVTdHJlYW0oe1xuICAgIGFzeW5jIHN0YXJ0KGNvbnRyb2xsZXIpIHtcbiAgICAgIGxldCBmaXJzdFRva2VuU2VudCA9IGZhbHNlO1xuICAgICAgbGV0IGFjY3VtdWxhdGVkQ29udGVudCA9ICcnO1xuICAgICAgY29uc3Qgc3RyZWFtU3RhcnRUaW1lID0gRGF0ZS5ub3coKTtcblxuICAgICAgdHJ5IHtcbiAgICAgICAgd2hpbGUgKHRydWUpIHtcbiAgICAgICAgICBjb25zdCB7IGRvbmUsIHZhbHVlIH0gPSBhd2FpdCByZWFkZXIucmVhZCgpO1xuXG4gICAgICAgICAgaWYgKGRvbmUpIHtcbiAgICAgICAgICAgIC8vIEZpbmFsIG9wcG9ydHVuaXR5IHRvIGV4dHJhY3QgdXNhZ2UgZGF0YSBpZiBub3QgZm91bmQgaW4gY2h1bmtzXG4gICAgICAgICAgICBpZiAob25Vc2FnZUV4dHJhY3RlZCAmJiAhZmlyc3RUb2tlblNlbnQpIHtcbiAgICAgICAgICAgICAgLy8gRmFsbGJhY2s6IGVzdGltYXRlIHRva2VucyBmcm9tIGFjY3VtdWxhdGVkIGNvbnRlbnRcbiAgICAgICAgICAgICAgY29uc3QgZXN0aW1hdGVkVXNhZ2UgPSBlc3RpbWF0ZVVzYWdlRnJvbUNvbnRlbnQoYWNjdW11bGF0ZWRDb250ZW50LCBwcm92aWRlcik7XG4gICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5KwIFske3Byb3ZpZGVyfSBDb3N0XSBGYWxsYmFjayB0b2tlbiBlc3RpbWF0aW9uOiAke2VzdGltYXRlZFVzYWdlLnRvdGFsVG9rZW5zfSB0b2tlbnNgKTtcbiAgICAgICAgICAgICAgb25Vc2FnZUV4dHJhY3RlZChlc3RpbWF0ZWRVc2FnZSk7XG4gICAgICAgICAgICB9XG4gICAgICAgICAgICBjb250cm9sbGVyLmNsb3NlKCk7XG4gICAgICAgICAgICBicmVhaztcbiAgICAgICAgICB9XG5cbiAgICAgICAgICBjb25zdCBjaHVuayA9IGRlY29kZXIuZGVjb2RlKHZhbHVlLCB7IHN0cmVhbTogdHJ1ZSB9KTtcblxuICAgICAgICAgIC8vIENoZWNrIGlmIHRoaXMgY2h1bmsgY29udGFpbnMgYWN0dWFsIGNvbnRlbnQgKGZpcnN0IHRva2VuKVxuICAgICAgICAgIGlmICghZmlyc3RUb2tlblNlbnQgJiYgY2h1bmsuaW5jbHVkZXMoJ2RlbHRhJykpIHtcbiAgICAgICAgICAgIHRyeSB7XG4gICAgICAgICAgICAgIC8vIFBhcnNlIFNTRSBkYXRhIHRvIGNoZWNrIGZvciBjb250ZW50XG4gICAgICAgICAgICAgIGNvbnN0IGxpbmVzID0gY2h1bmsuc3BsaXQoJ1xcbicpO1xuICAgICAgICAgICAgICBmb3IgKGNvbnN0IGxpbmUgb2YgbGluZXMpIHtcbiAgICAgICAgICAgICAgICBpZiAobGluZS5zdGFydHNXaXRoKCdkYXRhOiAnKSAmJiAhbGluZS5pbmNsdWRlcygnW0RPTkVdJykpIHtcbiAgICAgICAgICAgICAgICAgIGNvbnN0IGpzb25EYXRhID0gbGluZS5zdWJzdHJpbmcoNik7XG4gICAgICAgICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKGpzb25EYXRhKTtcbiAgICAgICAgICAgICAgICAgICAgaWYgKHBhcnNlZC5jaG9pY2VzPy5bMF0/LmRlbHRhPy5jb250ZW50KSB7XG4gICAgICAgICAgICAgICAgICAgICAgY29uc3QgZmlyc3RUb2tlblRpbWUgPSBEYXRlLm5vdygpIC0gc3RyZWFtU3RhcnRUaW1lO1xuICAgICAgICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5qAICR7cHJvdmlkZXIudG9VcHBlckNhc2UoKX0gRklSU1QgVE9LRU46ICR7Zmlyc3RUb2tlblRpbWV9bXMgKCR7bW9kZWx9KWApO1xuICAgICAgICAgICAgICAgICAgICAgIGZpcnN0VG9rZW5TZW50ID0gdHJ1ZTtcblxuICAgICAgICAgICAgICAgICAgICAgIC8vIEFjY3VtdWxhdGUgY29udGVudCBmb3IgZmFsbGJhY2sgZXN0aW1hdGlvblxuICAgICAgICAgICAgICAgICAgICAgIGFjY3VtdWxhdGVkQ29udGVudCArPSBwYXJzZWQuY2hvaWNlc1swXS5kZWx0YS5jb250ZW50O1xuICAgICAgICAgICAgICAgICAgICAgIGJyZWFrO1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIElnbm9yZSBKU09OIHBhcnNlIGVycm9ycyBmb3IgaW5kaXZpZHVhbCBjaHVua3NcbiAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgLy8gSWdub3JlIHBhcnNpbmcgZXJyb3JzLCBqdXN0IHRyYWNrIHRpbWluZ1xuICAgICAgICAgICAgICBpZiAoIWZpcnN0VG9rZW5TZW50KSB7XG4gICAgICAgICAgICAgICAgY29uc3QgZmlyc3RUb2tlblRpbWUgPSBEYXRlLm5vdygpIC0gc3RyZWFtU3RhcnRUaW1lO1xuICAgICAgICAgICAgICAgIGNvbnNvbGUubG9nKGDwn5qAICR7cHJvdmlkZXIudG9VcHBlckNhc2UoKX0gRklSU1QgVE9LRU46ICR7Zmlyc3RUb2tlblRpbWV9bXMgKCR7bW9kZWx9KSBbZmFsbGJhY2sgZGV0ZWN0aW9uXWApO1xuICAgICAgICAgICAgICAgIGZpcnN0VG9rZW5TZW50ID0gdHJ1ZTtcbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cblxuICAgICAgICAgIC8vIEV4dHJhY3QgdXNhZ2UgZGF0YSBmcm9tIHN0cmVhbWluZyBjaHVua3NcbiAgICAgICAgICBpZiAob25Vc2FnZUV4dHJhY3RlZCAmJiBjaHVuay5pbmNsdWRlcygndXNhZ2UnKSkge1xuICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgY29uc3QgdXNhZ2VEYXRhID0gZXh0cmFjdFVzYWdlRnJvbVN0cmVhbUNodW5rKGNodW5rLCBwcm92aWRlcik7XG4gICAgICAgICAgICAgIGlmICh1c2FnZURhdGEpIHtcbiAgICAgICAgICAgICAgICBjb25zb2xlLmxvZyhg8J+SsCBbJHtwcm92aWRlcn0gQ29zdF0gVXNhZ2UgZGF0YSBleHRyYWN0ZWQgZnJvbSBzdHJlYW06ICR7dXNhZ2VEYXRhLnRvdGFsVG9rZW5zfSB0b2tlbnNgKTtcbiAgICAgICAgICAgICAgICBvblVzYWdlRXh0cmFjdGVkKHVzYWdlRGF0YSk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0gY2F0Y2ggKGUpIHtcbiAgICAgICAgICAgICAgLy8gSWdub3JlIHVzYWdlIGV4dHJhY3Rpb24gZXJyb3JzXG4gICAgICAgICAgICB9XG4gICAgICAgICAgfVxuXG4gICAgICAgICAgLy8gQ29udGludWUgYWNjdW11bGF0aW5nIGNvbnRlbnQgZm9yIGZhbGxiYWNrXG4gICAgICAgICAgaWYgKGNodW5rLmluY2x1ZGVzKCdkZWx0YScpKSB7XG4gICAgICAgICAgICB0cnkge1xuICAgICAgICAgICAgICBjb25zdCBsaW5lcyA9IGNodW5rLnNwbGl0KCdcXG4nKTtcbiAgICAgICAgICAgICAgZm9yIChjb25zdCBsaW5lIG9mIGxpbmVzKSB7XG4gICAgICAgICAgICAgICAgaWYgKGxpbmUuc3RhcnRzV2l0aCgnZGF0YTogJykgJiYgIWxpbmUuaW5jbHVkZXMoJ1tET05FXScpKSB7XG4gICAgICAgICAgICAgICAgICBjb25zdCBqc29uRGF0YSA9IGxpbmUuc3Vic3RyaW5nKDYpO1xuICAgICAgICAgICAgICAgICAgdHJ5IHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgcGFyc2VkID0gSlNPTi5wYXJzZShqc29uRGF0YSk7XG4gICAgICAgICAgICAgICAgICAgIGlmIChwYXJzZWQuY2hvaWNlcz8uWzBdPy5kZWx0YT8uY29udGVudCkge1xuICAgICAgICAgICAgICAgICAgICAgIGFjY3VtdWxhdGVkQ29udGVudCArPSBwYXJzZWQuY2hvaWNlc1swXS5kZWx0YS5jb250ZW50O1xuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgICAgICAgICAgICAgIC8vIElnbm9yZSBKU09OIHBhcnNlIGVycm9yc1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgICAgICAvLyBJZ25vcmUgcGFyc2luZyBlcnJvcnNcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG5cbiAgICAgICAgICAvLyBGb3J3YXJkIHRoZSBjaHVuayB1bmNoYW5nZWRcbiAgICAgICAgICBjb250cm9sbGVyLmVucXVldWUodmFsdWUpO1xuICAgICAgICB9XG4gICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICBjb25zb2xlLmVycm9yKGBbJHtwcm92aWRlcn0gU3RyZWFtIFRyYWNraW5nXSBFcnJvcjpgLCBlcnJvcik7XG4gICAgICAgIC8vIFBoYXNlIDEgT3B0aW1pemF0aW9uOiBHcmFjZWZ1bCBlcnJvciBoYW5kbGluZyBmb3IgY29ubmVjdGlvbiByZXNldHNcbiAgICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgRXJyb3IgJiYgKGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ2Fib3J0ZWQnKSB8fCBlcnJvci5tZXNzYWdlLmluY2x1ZGVzKCdFQ09OTlJFU0VUJykpKSB7XG4gICAgICAgICAgY29uc29sZS5sb2coYFske3Byb3ZpZGVyfSBTdHJlYW1dIENvbm5lY3Rpb24gcmVzZXQgZGV0ZWN0ZWQgLSBjbG9zaW5nIHN0cmVhbSBncmFjZWZ1bGx5YCk7XG4gICAgICAgICAgY29udHJvbGxlci5jbG9zZSgpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGNvbnRyb2xsZXIuZXJyb3IoZXJyb3IpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgfVxuICB9KTtcbn1cblxuLy8gRW5oYW5jZWQgbG9nZ2luZyBmb3Igc3RyZWFtaW5nIHBlcmZvcm1hbmNlXG5leHBvcnQgZnVuY3Rpb24gbG9nU3RyZWFtaW5nUGVyZm9ybWFuY2UoXG4gIHByb3ZpZGVyOiBzdHJpbmcsXG4gIG1vZGVsOiBzdHJpbmcsXG4gIG1ldHJpY3M6IHtcbiAgICB0aW1lVG9GaXJzdFRva2VuPzogbnVtYmVyO1xuICAgIHRvdGFsU3RyZWFtVGltZT86IG51bWJlcjtcbiAgICB0b3RhbFRva2Vucz86IG51bWJlcjtcbiAgICBhdmVyYWdlVG9rZW5MYXRlbmN5PzogbnVtYmVyO1xuICB9XG4pIHtcbiAgY29uc29sZS5sb2coYPCfk4ogU1RSRUFNSU5HIFBFUkZPUk1BTkNFOiAke3Byb3ZpZGVyfS8ke21vZGVsfWApO1xuICBcbiAgaWYgKG1ldHJpY3MudGltZVRvRmlyc3RUb2tlbiAhPT0gdW5kZWZpbmVkKSB7XG4gICAgY29uc29sZS5sb2coYCAgIOKPse+4jyBUaW1lIHRvIEZpcnN0IFRva2VuOiAke21ldHJpY3MudGltZVRvRmlyc3RUb2tlbi50b0ZpeGVkKDEpfW1zYCk7XG4gICAgXG4gICAgLy8gUGVyZm9ybWFuY2UgY2F0ZWdvcmllc1xuICAgIGlmIChtZXRyaWNzLnRpbWVUb0ZpcnN0VG9rZW4gPCA1MDApIHtcbiAgICAgIGNvbnNvbGUubG9nKGAgICDimqEgRVhDRUxMRU5UIGZpcnN0IHRva2VuIHBlcmZvcm1hbmNlYCk7XG4gICAgfSBlbHNlIGlmIChtZXRyaWNzLnRpbWVUb0ZpcnN0VG9rZW4gPCAxMDAwKSB7XG4gICAgICBjb25zb2xlLmxvZyhgICAg4pyFIEdPT0QgZmlyc3QgdG9rZW4gcGVyZm9ybWFuY2VgKTtcbiAgICB9IGVsc2UgaWYgKG1ldHJpY3MudGltZVRvRmlyc3RUb2tlbiA8IDIwMDApIHtcbiAgICAgIGNvbnNvbGUubG9nKGAgICDimqDvuI8gU0xPVyBmaXJzdCB0b2tlbiBwZXJmb3JtYW5jZWApO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zb2xlLmxvZyhgICAg8J+QjCBWRVJZIFNMT1cgZmlyc3QgdG9rZW4gcGVyZm9ybWFuY2VgKTtcbiAgICB9XG4gIH1cbiAgXG4gIGlmIChtZXRyaWNzLnRvdGFsU3RyZWFtVGltZSAhPT0gdW5kZWZpbmVkKSB7XG4gICAgY29uc29sZS5sb2coYCAgIPCflIQgVG90YWwgU3RyZWFtIFRpbWU6ICR7bWV0cmljcy50b3RhbFN0cmVhbVRpbWUudG9GaXhlZCgxKX1tc2ApO1xuICB9XG4gIFxuICBpZiAobWV0cmljcy50b3RhbFRva2VucyAhPT0gdW5kZWZpbmVkKSB7XG4gICAgY29uc29sZS5sb2coYCAgIPCfjq8gVG90YWwgVG9rZW5zOiAke21ldHJpY3MudG90YWxUb2tlbnN9YCk7XG4gIH1cbiAgXG4gIGlmIChtZXRyaWNzLmF2ZXJhZ2VUb2tlbkxhdGVuY3kgIT09IHVuZGVmaW5lZCkge1xuICAgIGNvbnNvbGUubG9nKGAgICDwn5OIIEF2ZyBUb2tlbiBMYXRlbmN5OiAke21ldHJpY3MuYXZlcmFnZVRva2VuTGF0ZW5jeS50b0ZpeGVkKDEpfW1zL3Rva2VuYCk7XG4gIH1cbn1cblxuLy8gVXRpbGl0eSB0byBleHRyYWN0IHByb3ZpZGVyIGFuZCBtb2RlbCBmcm9tIHJlcXVlc3QgY29udGV4dFxuZXhwb3J0IGZ1bmN0aW9uIGdldFByb3ZpZGVyTW9kZWxGcm9tQ29udGV4dChcbiAgcHJvdmlkZXJOYW1lOiBzdHJpbmcgfCBudWxsLFxuICBtb2RlbElkOiBzdHJpbmcgfCBudWxsXG4pOiB7IHByb3ZpZGVyOiBzdHJpbmc7IG1vZGVsOiBzdHJpbmcgfSB7XG4gIHJldHVybiB7XG4gICAgcHJvdmlkZXI6IHByb3ZpZGVyTmFtZSB8fCAndW5rbm93bicsXG4gICAgbW9kZWw6IG1vZGVsSWQgfHwgJ3Vua25vd24nXG4gIH07XG59XG5cbi8vIEV4dHJhY3QgdXNhZ2UgZGF0YSBmcm9tIHN0cmVhbWluZyBjaHVua1xuZXhwb3J0IGZ1bmN0aW9uIGV4dHJhY3RVc2FnZUZyb21TdHJlYW1DaHVuayhjaHVuazogc3RyaW5nLCBwcm92aWRlcjogc3RyaW5nKTogU3RyZWFtaW5nVXNhZ2VEYXRhIHwgbnVsbCB7XG4gIHRyeSB7XG4gICAgY29uc3QgbGluZXMgPSBjaHVuay5zcGxpdCgnXFxuJyk7XG4gICAgZm9yIChjb25zdCBsaW5lIG9mIGxpbmVzKSB7XG4gICAgICBpZiAobGluZS5zdGFydHNXaXRoKCdkYXRhOiAnKSAmJiAhbGluZS5pbmNsdWRlcygnW0RPTkVdJykpIHtcbiAgICAgICAgY29uc3QganNvbkRhdGEgPSBsaW5lLnN1YnN0cmluZyg2KTtcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBwYXJzZWQgPSBKU09OLnBhcnNlKGpzb25EYXRhKTtcblxuICAgICAgICAgIC8vIENoZWNrIGZvciB1c2FnZSBkYXRhIGluIHRoZSBjaHVua1xuICAgICAgICAgIGlmIChwYXJzZWQudXNhZ2UpIHtcbiAgICAgICAgICAgIGNvbnN0IHVzYWdlID0gcGFyc2VkLnVzYWdlO1xuXG4gICAgICAgICAgICAvLyBIYW5kbGUgZGlmZmVyZW50IHByb3ZpZGVyIGZvcm1hdHNcbiAgICAgICAgICAgIGxldCBwcm9tcHRUb2tlbnMgPSAwO1xuICAgICAgICAgICAgbGV0IGNvbXBsZXRpb25Ub2tlbnMgPSAwO1xuICAgICAgICAgICAgbGV0IHRvdGFsVG9rZW5zID0gMDtcbiAgICAgICAgICAgIGxldCBjb3N0OiBudW1iZXIgfCB1bmRlZmluZWQ7XG5cbiAgICAgICAgICAgIGlmIChwcm92aWRlci50b0xvd2VyQ2FzZSgpID09PSAnb3BlbnJvdXRlcicpIHtcbiAgICAgICAgICAgICAgLy8gT3BlblJvdXRlciBmb3JtYXRcbiAgICAgICAgICAgICAgcHJvbXB0VG9rZW5zID0gdXNhZ2UucHJvbXB0X3Rva2VucyB8fCAwO1xuICAgICAgICAgICAgICBjb21wbGV0aW9uVG9rZW5zID0gdXNhZ2UuY29tcGxldGlvbl90b2tlbnMgfHwgMDtcbiAgICAgICAgICAgICAgdG90YWxUb2tlbnMgPSB1c2FnZS50b3RhbF90b2tlbnMgfHwgKHByb21wdFRva2VucyArIGNvbXBsZXRpb25Ub2tlbnMpO1xuICAgICAgICAgICAgICBjb3N0ID0gdXNhZ2UuY29zdCA/IHVzYWdlLmNvc3QgKiAwLjAwMDAwMSA6IHVuZGVmaW5lZDsgLy8gQ29udmVydCBjcmVkaXRzIHRvIFVTRFxuICAgICAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICAgICAgLy8gU3RhbmRhcmQgT3BlbkFJIGZvcm1hdCAoR29vZ2xlLCBBbnRocm9waWMsIHhBSSlcbiAgICAgICAgICAgICAgcHJvbXB0VG9rZW5zID0gdXNhZ2UucHJvbXB0X3Rva2VucyB8fCB1c2FnZS5pbnB1dF90b2tlbnMgfHwgMDtcbiAgICAgICAgICAgICAgY29tcGxldGlvblRva2VucyA9IHVzYWdlLmNvbXBsZXRpb25fdG9rZW5zIHx8IHVzYWdlLm91dHB1dF90b2tlbnMgfHwgMDtcbiAgICAgICAgICAgICAgdG90YWxUb2tlbnMgPSB1c2FnZS50b3RhbF90b2tlbnMgfHwgKHByb21wdFRva2VucyArIGNvbXBsZXRpb25Ub2tlbnMpO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBpZiAodG90YWxUb2tlbnMgPiAwKSB7XG4gICAgICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICAgICAgcHJvbXB0VG9rZW5zLFxuICAgICAgICAgICAgICAgIGNvbXBsZXRpb25Ub2tlbnMsXG4gICAgICAgICAgICAgICAgdG90YWxUb2tlbnMsXG4gICAgICAgICAgICAgICAgY29zdFxuICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfVxuICAgICAgICAgIH1cbiAgICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICAgIC8vIElnbm9yZSBKU09OIHBhcnNlIGVycm9ycyBmb3IgaW5kaXZpZHVhbCBjaHVua3NcbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfSBjYXRjaCAoZSkge1xuICAgIC8vIElnbm9yZSBwYXJzaW5nIGVycm9yc1xuICB9XG5cbiAgcmV0dXJuIG51bGw7XG59XG5cbi8vIEVzdGltYXRlIHVzYWdlIGZyb20gYWNjdW11bGF0ZWQgY29udGVudCAoZmFsbGJhY2spXG5leHBvcnQgZnVuY3Rpb24gZXN0aW1hdGVVc2FnZUZyb21Db250ZW50KGNvbnRlbnQ6IHN0cmluZywgcHJvdmlkZXI6IHN0cmluZywgcHJvbXB0VGV4dD86IHN0cmluZyk6IFN0cmVhbWluZ1VzYWdlRGF0YSB7XG4gIC8vIE1vcmUgYWNjdXJhdGUgdG9rZW4gZXN0aW1hdGlvbiBiYXNlZCBvbiBwcm92aWRlclxuICBsZXQgdG9rZW5zUGVyQ2hhciA9IDAuMjU7IC8vIERlZmF1bHQ6IDQgY2hhcnMgcGVyIHRva2VuXG5cbiAgLy8gUHJvdmlkZXItc3BlY2lmaWMgdG9rZW4gcmF0aW9zIChiYXNlZCBvbiBlbXBpcmljYWwgZGF0YSlcbiAgc3dpdGNoIChwcm92aWRlci50b0xvd2VyQ2FzZSgpKSB7XG4gICAgY2FzZSAnb3BlbnJvdXRlcic6XG4gICAgY2FzZSAnb3BlbmFpJzpcbiAgICAgIHRva2Vuc1BlckNoYXIgPSAwLjI1OyAvLyB+NCBjaGFycyBwZXIgdG9rZW5cbiAgICAgIGJyZWFrO1xuICAgIGNhc2UgJ2dvb2dsZSc6XG4gICAgICB0b2tlbnNQZXJDaGFyID0gMC4yMjsgLy8gfjQuNSBjaGFycyBwZXIgdG9rZW4gKHNsaWdodGx5IG1vcmUgZWZmaWNpZW50KVxuICAgICAgYnJlYWs7XG4gICAgY2FzZSAnYW50aHJvcGljJzpcbiAgICAgIHRva2Vuc1BlckNoYXIgPSAwLjI2OyAvLyB+My44IGNoYXJzIHBlciB0b2tlblxuICAgICAgYnJlYWs7XG4gICAgY2FzZSAneGFpJzpcbiAgICAgIHRva2Vuc1BlckNoYXIgPSAwLjI1OyAvLyBTaW1pbGFyIHRvIE9wZW5BSVxuICAgICAgYnJlYWs7XG4gIH1cblxuICBjb25zdCBjb21wbGV0aW9uVG9rZW5zID0gTWF0aC5jZWlsKGNvbnRlbnQubGVuZ3RoICogdG9rZW5zUGVyQ2hhcik7XG4gIGNvbnN0IHByb21wdFRva2VucyA9IHByb21wdFRleHQgPyBNYXRoLmNlaWwocHJvbXB0VGV4dC5sZW5ndGggKiB0b2tlbnNQZXJDaGFyKSA6IE1hdGguY2VpbChjb21wbGV0aW9uVG9rZW5zICogMC4zKTsgLy8gRXN0aW1hdGUgMzAlIG9mIGNvbXBsZXRpb25cbiAgY29uc3QgdG90YWxUb2tlbnMgPSBwcm9tcHRUb2tlbnMgKyBjb21wbGV0aW9uVG9rZW5zO1xuXG4gIHJldHVybiB7XG4gICAgcHJvbXB0VG9rZW5zLFxuICAgIGNvbXBsZXRpb25Ub2tlbnMsXG4gICAgdG90YWxUb2tlbnNcbiAgfTtcbn1cblxuLy8gU2ltcGxlIHRva2VuIGNvdW50ZXIgZm9yIHJvdWdoIGVzdGltYXRpb24gKGxlZ2FjeSBmdW5jdGlvbilcbmV4cG9ydCBmdW5jdGlvbiBlc3RpbWF0ZVRva2VuQ291bnQodGV4dDogc3RyaW5nKTogbnVtYmVyIHtcbiAgLy8gUm91Z2ggZXN0aW1hdGlvbjogMSB0b2tlbiDiiYggNCBjaGFyYWN0ZXJzIGZvciBFbmdsaXNoIHRleHRcbiAgLy8gVGhpcyBpcyBhIHNpbXBsaWZpZWQgYXBwcm9hY2gsIHJlYWwgdG9rZW5pemF0aW9uIHdvdWxkIGJlIG1vcmUgYWNjdXJhdGVcbiAgcmV0dXJuIE1hdGguY2VpbCh0ZXh0Lmxlbmd0aCAvIDQpO1xufVxuXG4vLyBQZXJmb3JtYW5jZSB0aHJlc2hvbGRzIGZvciBkaWZmZXJlbnQgcHJvdmlkZXJzXG5leHBvcnQgY29uc3QgUEVSRk9STUFOQ0VfVEhSRVNIT0xEUyA9IHtcbiAgRVhDRUxMRU5UX0ZJUlNUX1RPS0VOOiA1MDAsICAgLy8gPCA1MDBtc1xuICBHT09EX0ZJUlNUX1RPS0VOOiAxMDAwLCAgICAgICAvLyA8IDEwMDBtc1xuICBTTE9XX0ZJUlNUX1RPS0VOOiAyMDAwLCAgICAgICAvLyA8IDIwMDBtc1xuICAvLyBBbnl0aGluZyBhYm92ZSAyMDAwbXMgaXMgY29uc2lkZXJlZCB2ZXJ5IHNsb3dcbiAgXG4gIEVYQ0VMTEVOVF9UT1RBTDogMzAwMCwgICAgICAgIC8vIDwgM3MgdG90YWxcbiAgR09PRF9UT1RBTDogNTAwMCwgICAgICAgICAgICAvLyA8IDVzIHRvdGFsXG4gIFNMT1dfVE9UQUw6IDEwMDAwLCAgICAgICAgICAgLy8gPCAxMHMgdG90YWxcbiAgXG4gIFRBUkdFVF9UT0tFTl9MQVRFTkNZOiA1MCwgICAgIC8vIDwgNTBtcyBwZXIgdG9rZW4gYWZ0ZXIgZmlyc3Rcbn0gYXMgY29uc3Q7XG5cbi8vIENoZWNrIGlmIHBlcmZvcm1hbmNlIG1lZXRzIHRhcmdldHNcbmV4cG9ydCBmdW5jdGlvbiBldmFsdWF0ZVBlcmZvcm1hbmNlKG1ldHJpY3M6IHtcbiAgdGltZVRvRmlyc3RUb2tlbj86IG51bWJlcjtcbiAgdG90YWxTdHJlYW1UaW1lPzogbnVtYmVyO1xuICBhdmVyYWdlVG9rZW5MYXRlbmN5PzogbnVtYmVyO1xufSk6IHtcbiAgZmlyc3RUb2tlbkdyYWRlOiAnZXhjZWxsZW50JyB8ICdnb29kJyB8ICdzbG93JyB8ICd2ZXJ5X3Nsb3cnO1xuICB0b3RhbFRpbWVHcmFkZTogJ2V4Y2VsbGVudCcgfCAnZ29vZCcgfCAnc2xvdycgfCAndmVyeV9zbG93JztcbiAgdG9rZW5MYXRlbmN5R3JhZGU6ICdleGNlbGxlbnQnIHwgJ2dvb2QnIHwgJ3Nsb3cnIHwgJ3Zlcnlfc2xvdyc7XG4gIG92ZXJhbGxHcmFkZTogJ2V4Y2VsbGVudCcgfCAnZ29vZCcgfCAnc2xvdycgfCAndmVyeV9zbG93Jztcbn0ge1xuICBjb25zdCBmaXJzdFRva2VuR3JhZGUgPSBcbiAgICAhbWV0cmljcy50aW1lVG9GaXJzdFRva2VuID8gJ3Zlcnlfc2xvdycgOlxuICAgIG1ldHJpY3MudGltZVRvRmlyc3RUb2tlbiA8IFBFUkZPUk1BTkNFX1RIUkVTSE9MRFMuRVhDRUxMRU5UX0ZJUlNUX1RPS0VOID8gJ2V4Y2VsbGVudCcgOlxuICAgIG1ldHJpY3MudGltZVRvRmlyc3RUb2tlbiA8IFBFUkZPUk1BTkNFX1RIUkVTSE9MRFMuR09PRF9GSVJTVF9UT0tFTiA/ICdnb29kJyA6XG4gICAgbWV0cmljcy50aW1lVG9GaXJzdFRva2VuIDwgUEVSRk9STUFOQ0VfVEhSRVNIT0xEUy5TTE9XX0ZJUlNUX1RPS0VOID8gJ3Nsb3cnIDogJ3Zlcnlfc2xvdyc7XG4gIFxuICBjb25zdCB0b3RhbFRpbWVHcmFkZSA9IFxuICAgICFtZXRyaWNzLnRvdGFsU3RyZWFtVGltZSA/ICd2ZXJ5X3Nsb3cnIDpcbiAgICBtZXRyaWNzLnRvdGFsU3RyZWFtVGltZSA8IFBFUkZPUk1BTkNFX1RIUkVTSE9MRFMuRVhDRUxMRU5UX1RPVEFMID8gJ2V4Y2VsbGVudCcgOlxuICAgIG1ldHJpY3MudG90YWxTdHJlYW1UaW1lIDwgUEVSRk9STUFOQ0VfVEhSRVNIT0xEUy5HT09EX1RPVEFMID8gJ2dvb2QnIDpcbiAgICBtZXRyaWNzLnRvdGFsU3RyZWFtVGltZSA8IFBFUkZPUk1BTkNFX1RIUkVTSE9MRFMuU0xPV19UT1RBTCA/ICdzbG93JyA6ICd2ZXJ5X3Nsb3cnO1xuICBcbiAgY29uc3QgdG9rZW5MYXRlbmN5R3JhZGUgPSBcbiAgICAhbWV0cmljcy5hdmVyYWdlVG9rZW5MYXRlbmN5ID8gJ3Zlcnlfc2xvdycgOlxuICAgIG1ldHJpY3MuYXZlcmFnZVRva2VuTGF0ZW5jeSA8IFBFUkZPUk1BTkNFX1RIUkVTSE9MRFMuVEFSR0VUX1RPS0VOX0xBVEVOQ1kgPyAnZXhjZWxsZW50JyA6XG4gICAgbWV0cmljcy5hdmVyYWdlVG9rZW5MYXRlbmN5IDwgUEVSRk9STUFOQ0VfVEhSRVNIT0xEUy5UQVJHRVRfVE9LRU5fTEFURU5DWSAqIDIgPyAnZ29vZCcgOlxuICAgIG1ldHJpY3MuYXZlcmFnZVRva2VuTGF0ZW5jeSA8IFBFUkZPUk1BTkNFX1RIUkVTSE9MRFMuVEFSR0VUX1RPS0VOX0xBVEVOQ1kgKiA0ID8gJ3Nsb3cnIDogJ3Zlcnlfc2xvdyc7XG4gIFxuICAvLyBPdmVyYWxsIGdyYWRlIGlzIHRoZSB3b3JzdCBvZiB0aGUgdGhyZWVcbiAgY29uc3QgZ3JhZGVzID0gW2ZpcnN0VG9rZW5HcmFkZSwgdG90YWxUaW1lR3JhZGUsIHRva2VuTGF0ZW5jeUdyYWRlXTtcbiAgY29uc3QgZ3JhZGVPcmRlciA9IFsnZXhjZWxsZW50JywgJ2dvb2QnLCAnc2xvdycsICd2ZXJ5X3Nsb3cnXTtcbiAgY29uc3Qgb3ZlcmFsbEdyYWRlID0gZ3JhZGVzLnJlZHVjZSgod29yc3QsIGN1cnJlbnQpID0+IHtcbiAgICByZXR1cm4gZ3JhZGVPcmRlci5pbmRleE9mKGN1cnJlbnQpID4gZ3JhZGVPcmRlci5pbmRleE9mKHdvcnN0KSA/IGN1cnJlbnQgOiB3b3JzdDtcbiAgfSwgJ2V4Y2VsbGVudCcpIGFzICdleGNlbGxlbnQnIHwgJ2dvb2QnIHwgJ3Nsb3cnIHwgJ3Zlcnlfc2xvdyc7XG4gIFxuICByZXR1cm4ge1xuICAgIGZpcnN0VG9rZW5HcmFkZSxcbiAgICB0b3RhbFRpbWVHcmFkZSxcbiAgICB0b2tlbkxhdGVuY3lHcmFkZSxcbiAgICBvdmVyYWxsR3JhZGVcbiAgfTtcbn1cbiJdLCJuYW1lcyI6WyJjcmVhdGVGaXJzdFRva2VuVHJhY2tpbmdTdHJlYW0iLCJvcmlnaW5hbFN0cmVhbSIsInByb3ZpZGVyIiwibW9kZWwiLCJvblVzYWdlRXh0cmFjdGVkIiwicmVhZGVyIiwiZ2V0UmVhZGVyIiwiZGVjb2RlciIsIlRleHREZWNvZGVyIiwiZW5jb2RlciIsIlRleHRFbmNvZGVyIiwiUmVhZGFibGVTdHJlYW0iLCJzdGFydCIsImNvbnRyb2xsZXIiLCJmaXJzdFRva2VuU2VudCIsImFjY3VtdWxhdGVkQ29udGVudCIsInN0cmVhbVN0YXJ0VGltZSIsIkRhdGUiLCJub3ciLCJkb25lIiwidmFsdWUiLCJyZWFkIiwiZXN0aW1hdGVkVXNhZ2UiLCJlc3RpbWF0ZVVzYWdlRnJvbUNvbnRlbnQiLCJjb25zb2xlIiwibG9nIiwidG90YWxUb2tlbnMiLCJjbG9zZSIsImNodW5rIiwiZGVjb2RlIiwic3RyZWFtIiwiaW5jbHVkZXMiLCJsaW5lcyIsInNwbGl0IiwibGluZSIsInN0YXJ0c1dpdGgiLCJqc29uRGF0YSIsInN1YnN0cmluZyIsInBhcnNlZCIsIkpTT04iLCJwYXJzZSIsImNob2ljZXMiLCJkZWx0YSIsImNvbnRlbnQiLCJmaXJzdFRva2VuVGltZSIsInRvVXBwZXJDYXNlIiwiZSIsInVzYWdlRGF0YSIsImV4dHJhY3RVc2FnZUZyb21TdHJlYW1DaHVuayIsImVucXVldWUiLCJlcnJvciIsIkVycm9yIiwibWVzc2FnZSIsImxvZ1N0cmVhbWluZ1BlcmZvcm1hbmNlIiwibWV0cmljcyIsInRpbWVUb0ZpcnN0VG9rZW4iLCJ1bmRlZmluZWQiLCJ0b0ZpeGVkIiwidG90YWxTdHJlYW1UaW1lIiwiYXZlcmFnZVRva2VuTGF0ZW5jeSIsImdldFByb3ZpZGVyTW9kZWxGcm9tQ29udGV4dCIsInByb3ZpZGVyTmFtZSIsIm1vZGVsSWQiLCJ1c2FnZSIsInByb21wdFRva2VucyIsImNvbXBsZXRpb25Ub2tlbnMiLCJjb3N0IiwidG9Mb3dlckNhc2UiLCJwcm9tcHRfdG9rZW5zIiwiY29tcGxldGlvbl90b2tlbnMiLCJ0b3RhbF90b2tlbnMiLCJpbnB1dF90b2tlbnMiLCJvdXRwdXRfdG9rZW5zIiwicHJvbXB0VGV4dCIsInRva2Vuc1BlckNoYXIiLCJNYXRoIiwiY2VpbCIsImxlbmd0aCIsImVzdGltYXRlVG9rZW5Db3VudCIsInRleHQiLCJQRVJGT1JNQU5DRV9USFJFU0hPTERTIiwiRVhDRUxMRU5UX0ZJUlNUX1RPS0VOIiwiR09PRF9GSVJTVF9UT0tFTiIsIlNMT1dfRklSU1RfVE9LRU4iLCJFWENFTExFTlRfVE9UQUwiLCJHT09EX1RPVEFMIiwiU0xPV19UT1RBTCIsIlRBUkdFVF9UT0tFTl9MQVRFTkNZIiwiZXZhbHVhdGVQZXJmb3JtYW5jZSIsImZpcnN0VG9rZW5HcmFkZSIsInRvdGFsVGltZUdyYWRlIiwidG9rZW5MYXRlbmN5R3JhZGUiLCJncmFkZXMiLCJncmFkZU9yZGVyIiwib3ZlcmFsbEdyYWRlIiwicmVkdWNlIiwid29yc3QiLCJjdXJyZW50IiwiaW5kZXhPZiJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/streamingUtils.ts\n");

/***/ })

};
;