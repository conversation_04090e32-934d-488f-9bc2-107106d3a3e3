"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_reranker_jina_ts";
exports.ids = ["_rsc_src_lib_reranker_jina_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/reranker/jina.ts":
/*!**********************************!*\
  !*** ./src/lib/reranker/jina.ts ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   MultiKeyJinaReranker: () => (/* binding */ MultiKeyJinaReranker),\n/* harmony export */   jinaReranker: () => (/* binding */ jinaReranker)\n/* harmony export */ });\n/**\n * Multi-Key Jina Reranker m0 Implementation\n * Provides automatic key rotation and reranking for optimal RAG results\n */ class MultiKeyJinaReranker {\n    constructor(){\n        this.currentKeyIndex = 0;\n        this.keyUsage = new Map();\n        this.baseUrl = 'https://api.jina.ai/v1/rerank';\n        this.model = 'jina-reranker-m0';\n        // Initialize with multiple Jina API keys for high rate limits\n        this.apiKeys = [\n            process.env.JINA_API_KEY,\n            process.env.JINA_API_KEY_2,\n            process.env.JINA_API_KEY_3,\n            process.env.JINA_API_KEY_4,\n            process.env.JINA_API_KEY_5,\n            process.env.JINA_API_KEY_6,\n            process.env.JINA_API_KEY_7,\n            process.env.JINA_API_KEY_9,\n            process.env.JINA_API_KEY_10\n        ].filter(Boolean);\n        if (this.apiKeys.length === 0) {\n            throw new Error('No Jina API keys found in environment variables');\n        }\n        console.log(`[Jina Reranker] Initialized with ${this.apiKeys.length} API keys`);\n        // Initialize usage stats for each key\n        this.apiKeys.forEach((key)=>{\n            this.keyUsage.set(key, {\n                requests: 0,\n                tokens: 0,\n                lastUsed: new Date(0),\n                errors: 0\n            });\n        });\n    }\n    /**\n   * Get the next API key using round-robin rotation\n   */ getNextKey() {\n        const key = this.apiKeys[this.currentKeyIndex];\n        this.currentKeyIndex = (this.currentKeyIndex + 1) % this.apiKeys.length;\n        return key;\n    }\n    /**\n   * Get the best available API key based on usage and error rates\n   */ getBestKey() {\n        // For now, use simple round-robin\n        // TODO: Implement smart selection based on usage stats\n        return this.getNextKey();\n    }\n    /**\n   * Update usage statistics for a key\n   */ updateKeyUsage(apiKey, tokens, isError = false) {\n        const stats = this.keyUsage.get(apiKey);\n        if (stats) {\n            stats.requests++;\n            stats.tokens += tokens;\n            stats.lastUsed = new Date();\n            if (isError) {\n                stats.errors++;\n                stats.lastError = new Date();\n            }\n        }\n    }\n    /**\n   * Rerank documents based on query relevance using jina-reranker-m0\n   */ async rerankDocuments(query, documents, topN) {\n        if (!documents || documents.length === 0) {\n            console.log('[Jina Reranker] No documents to rerank');\n            return [];\n        }\n        const maxRetries = this.apiKeys.length;\n        let lastError = null;\n        // Limit to maximum 2048 documents as per Jina API limits\n        const documentsToRerank = documents.slice(0, 2048);\n        const actualTopN = topN || Math.min(documentsToRerank.length, 10);\n        console.log(`[Jina Reranker] Reranking ${documentsToRerank.length} documents for query: \"${query.substring(0, 100)}...\"`);\n        for(let attempt = 0; attempt < maxRetries; attempt++){\n            try {\n                const apiKey = this.getBestKey();\n                console.log(`[Jina Reranker] Attempt ${attempt + 1}/${maxRetries} with key ${this.apiKeys.indexOf(apiKey) + 1}`);\n                const response = await fetch(this.baseUrl, {\n                    method: 'POST',\n                    headers: {\n                        'Authorization': `Bearer ${apiKey}`,\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        model: this.model,\n                        query: query,\n                        documents: documentsToRerank.map((doc)=>doc.content),\n                        top_n: actualTopN,\n                        return_documents: false // We already have the documents\n                    })\n                });\n                if (!response.ok) {\n                    const errorText = await response.text();\n                    throw new Error(`HTTP ${response.status}: ${errorText}`);\n                }\n                const data = await response.json();\n                if (!data.results || !Array.isArray(data.results)) {\n                    throw new Error('Invalid response format from Jina Reranker API');\n                }\n                // Update usage stats\n                this.updateKeyUsage(apiKey, data.usage?.total_tokens || query.length);\n                console.log(`[Jina Reranker] Success with key ${this.apiKeys.indexOf(apiKey) + 1} (${data.results.length} results, ${data.usage?.total_tokens || 'unknown'} tokens)`);\n                // Map reranked results back to original documents with enhanced scoring\n                const rerankedResults = data.results.map((result)=>{\n                    const originalDoc = documentsToRerank[result.index];\n                    // Combine original similarity with rerank score for final scoring\n                    // Weight: 30% original similarity + 70% rerank score\n                    const finalScore = originalDoc.similarity * 0.3 + result.relevance_score * 0.7;\n                    return {\n                        content: originalDoc.content,\n                        document_id: originalDoc.document_id,\n                        original_similarity: originalDoc.similarity,\n                        rerank_score: result.relevance_score,\n                        final_score: finalScore,\n                        metadata: originalDoc.metadata\n                    };\n                });\n                // Sort by final score (highest first)\n                rerankedResults.sort((a, b)=>b.final_score - a.final_score);\n                console.log(`[Jina Reranker] Reranking complete. Top result improved from similarity ${rerankedResults[0]?.original_similarity.toFixed(3)} to final score ${rerankedResults[0]?.final_score.toFixed(3)}`);\n                // Log reranking improvements\n                rerankedResults.forEach((result, index)=>{\n                    console.log(`[Jina Reranker] Rank ${index + 1}: Original=${result.original_similarity.toFixed(3)}, Rerank=${result.rerank_score.toFixed(3)}, Final=${result.final_score.toFixed(3)}`);\n                });\n                return rerankedResults;\n            } catch (error) {\n                lastError = error;\n                console.log(`[Jina Reranker] Attempt ${attempt + 1} failed:`, error.message);\n                // Update error stats\n                const apiKey = this.apiKeys[this.currentKeyIndex - 1] || this.apiKeys[this.apiKeys.length - 1];\n                this.updateKeyUsage(apiKey, 0, true);\n                // If this is the last attempt, break and throw\n                if (attempt === maxRetries - 1) {\n                    break;\n                }\n            }\n        }\n        // All keys failed - return original documents sorted by similarity\n        console.error(`[Jina Reranker] All ${maxRetries} API keys failed. Falling back to original similarity ranking.`);\n        console.error(`[Jina Reranker] Last error: ${lastError?.message || 'Unknown error'}`);\n        // Fallback: return original documents with similarity as final score\n        const fallbackResults = documentsToRerank.slice(0, actualTopN).map((doc)=>({\n                content: doc.content,\n                document_id: doc.document_id,\n                original_similarity: doc.similarity,\n                rerank_score: doc.similarity,\n                final_score: doc.similarity,\n                metadata: doc.metadata\n            })).sort((a, b)=>b.final_score - a.final_score);\n        return fallbackResults;\n    }\n    /**\n   * Get usage statistics for all API keys\n   */ getUsageStats() {\n        const stats = {};\n        this.keyUsage.forEach((usage, key)=>{\n            const keyIndex = this.apiKeys.indexOf(key) + 1;\n            stats[`key_${keyIndex}`] = {\n                ...usage\n            };\n        });\n        return stats;\n    }\n    /**\n   * Reset usage statistics\n   */ resetUsageStats() {\n        this.apiKeys.forEach((key)=>{\n            this.keyUsage.set(key, {\n                requests: 0,\n                tokens: 0,\n                lastUsed: new Date(0),\n                errors: 0\n            });\n        });\n        console.log('[Jina Reranker] Usage statistics reset');\n    }\n}\n// Export singleton instance\nconst jinaReranker = new MultiKeyJinaReranker();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/reranker/jina.ts\n");

/***/ })

};
;